import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { User } from '@/libs/model/user.schema';

export async function POST(req: NextRequest) {
  try {
    // Get the referrer and new user IDs from the request body
    const body = await req.json();
    const { referrerId, userId } = body;
    
    if (!referrerId || !userId) {
      return NextResponse.json(
        { success: false, error: 'Both referrerId and userId are required' },
        { status: 400 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find both users to verify they exist
    const referrer = await User.findById(referrerId);
    const user = await User.findById(userId);
    
    if (!referrer) {
      return NextResponse.json(
        { success: false, error: 'Referrer not found' },
        { status: 404 }
      );
    }
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Update the user's referredBy field if not already set
    if (!user.referredBy) {
      user.referredBy = referrer._id;
      await user.save();
    }
    
    // You could add referral rewards here, e.g.:
    // - Add tokens to the referrer's wallet
    // - Record the referral in a separate collection
    // - Track referral statistics
    
    return NextResponse.json({
      success: true,
      message: 'Referral tracked successfully'
    });
    
  } catch (error) {
    console.error('Error tracking referral:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to track referral' },
      { status: 500 }
    );
  }
} 