import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Mission } from '@/libs/model/missions.schema';
import { UserRole } from '@/types/user';

// GET: Get a single mission by ID
export async function GET(
  req: NextRequest, 
  { params }: { params: any }
) {
  try {    
    const { id } = params;
    
    // Connect to database
    await connectToDatabase();
    
    // Get mission
    const mission = await Mission.findById(id);
    
    if (!mission) {
      return NextResponse.json({ success: false, error: 'Mission not found' }, { status: 404 });
    }
    
    return NextResponse.json({ success: true, mission });
    
  } catch (error) {
    console.error('Error getting mission:', error);
    return NextResponse.json({ success: false, error: 'Failed to get mission' }, { status: 500 });
  }
}

// PUT: Update a mission
export async function PUT(
  req: NextRequest, 
  { params }: { params:any }
) {
  try {
  
    
    const { id } = params;
    
    // Parse request body
    const body = await req.json();
    
    // Validate required fields
    if (!body.title || !body.description || !body.type || !body.image) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find mission
    const mission = await Mission.findById(id);
    
    if (!mission) {
      return NextResponse.json({ success: false, error: 'Mission not found' }, { status: 404 });
    }
    
    // Update mission
    mission.title = body.title;
    mission.description = body.description;
    mission.type = body.type;
    mission.status = body.status;
    mission.image = body.image;
    mission.taskLink = body.taskLink;
    mission.reward = {
      type: body.reward.type,
      amount: body.reward.amount
    };
    mission.isHighlighted = body.isHighlighted;
    
    await mission.save();
    
    return NextResponse.json({ success: true, mission });
    
  } catch (error) {
    console.error('Error updating mission:', error);
    return NextResponse.json({ success: false, error: 'Failed to update mission' }, { status: 500 });
  }
}

// PATCH: Update specific mission fields (e.g., status, isHighlighted)
export async function PATCH(
  req: NextRequest, 
  { params }: any
) {
  try {
    
    
    
    const { id } = params;
    
    // Parse request body
    const body = await req.json();
    
    // Connect to database
    await connectToDatabase();
    
    // Find mission
    const mission = await Mission.findById(id);
    
    if (!mission) {
      return NextResponse.json({ success: false, error: 'Mission not found' }, { status: 404 });
    }
    
    // Update only the provided fields
    if (body.status !== undefined) {
      mission.status = body.status;
    }
    
    if (body.isHighlighted !== undefined) {
      mission.isHighlighted = body.isHighlighted;
    }
    
    await mission.save();
    
    return NextResponse.json({ success: true, mission });
    
  } catch (error) {
    console.error('Error updating mission:', error);
    return NextResponse.json({ success: false, error: 'Failed to update mission' }, { status: 500 });
  }
}

// DELETE: Delete a mission
export async function DELETE(
  req: NextRequest, 
  { params }: { params: any }
) {
  try {
    
    const { id } = params;
    
    // Connect to database
    await connectToDatabase();
    
    // Delete mission
    const result = await Mission.deleteOne({ _id: id });
    
    if (result.deletedCount === 0) {
      return NextResponse.json({ success: false, error: 'Mission not found' }, { status: 404 });
    }
    
    return NextResponse.json({ success: true, message: 'Mission deleted successfully' });
    
  } catch (error) {
    console.error('Error deleting mission:', error);
    return NextResponse.json({ success: false, error: 'Failed to delete mission' }, { status: 500 });
  }
} 