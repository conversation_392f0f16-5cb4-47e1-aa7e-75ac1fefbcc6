import React from 'react'
import Missions from '@/components/missions/Missions'

// Use ISR instead of force-dynamic for better performance
export const revalidate = 600 // Revalidate every 10 minutes

async function getMissions() {
  try {
    // Must use absolute URL when fetching from a server component
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || process.env.VERCEL_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/missions`, {
      next: { revalidate: 600 } // Cache for 10 minutes
    })
    
    if (!response.ok) {
      throw new Error('Failed to fetch missions')
    }
    
    const data = await response.json()
    return data.missions || [];
  } catch (error) {
    console.error('Error fetching missions:', error)
    return []
  }
}

export default async function MissionsPage() {
  const missions = await getMissions()
  
  return (
    <div className="pt-4 pb-24 px-3">
      <Missions initialMissions={missions} />
    </div>
  )
}