"use client"
import React, { useState, useEffect } from 'react'
import { Suspense } from 'react'
import { TrendingUp, Loader2, AlertCircle } from 'lucide-react'
import { NewsCard } from './NewsCard'
import { useLanguage } from '@/context/LanguageContext'
import { NewsArticle, NewsResponse } from '@/types/news'

interface NewsSectionProps {
  initialData?: NewsResponse;
}

const NewsSection = ({ initialData }: NewsSectionProps) => {
  const [news, setNews] = useState<NewsArticle[]>(initialData?.data || []);
  const [isLoading, setIsLoading] = useState(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(initialData ? initialData.pagination.page < initialData.pagination.pages : false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { t } = useLanguage();

  // Initialize with server-side data if available
  useEffect(() => {
    if (initialData?.data) {
      setNews(initialData.data);
      setIsLoading(false);
      setHasMore(initialData.pagination.page < initialData.pagination.pages);
    } else if (!news.length && !error) {
      fetchNews();
    }
  }, [initialData]);

  // Fetch news data with optimized caching
  const fetchNews = async (pageNum = 1, append = false) => {
    try {
      if (pageNum === 1 && !append) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }
      
      setError(null);
      
      // Use relative URL for API calls
      const url = `/api/news?page=${pageNum}&limit=5&isPublished=true`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(t('news.fetchFailed'));
      }
      
      const data: NewsResponse = await response.json();
      
      if (data.success) {
        if (append) {
          setNews(prev => [...prev, ...data.data]);
        } else {
          setNews(data.data);
        }
        
        setHasMore(pageNum < data.pagination.pages);
      } else {
        throw new Error(t('news.fetchFailed'));
      }
    } catch (error) {
      console.error('Error fetching news:', error);
      setError(t('news.errorLoading'));
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  // Load more articles
  const loadMore = () => {
    if (!isLoadingMore && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchNews(nextPage, true);
    }
  };

  return (
    <div className="max-w-[500px] mx-auto relative z-10">
      {/* Glowing Background Effect - Static, no animations */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-48 h-48 bg-green-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>
      
      {/* Header - Removed motion animations */}
      <div className="relative mb-6 flex items-center">
        <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-gradient-to-b from-green-400 to-transparent rounded-full"></div>
        <h1 className="text-xl font-bold ml-3 text-white">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-green-300">{t('news.articles')}</span>
        </h1>
        {/* Decorative elements */}
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-20 h-[1px] bg-gradient-to-r from-green-500/50 to-transparent"></div>
        <div className="absolute right-5 -top-2 w-3 h-3 rounded-full bg-green-500/20 blur-xl"></div>
        <div className="absolute right-3 -bottom-2 w-2 h-2 rounded-full bg-green-500/30 blur-md"></div>
      </div>
      
      {/* Hot Topics Label */}
      <div className="flex items-center mb-4 px-1">
        <div className="flex items-center bg-black/60 backdrop-blur-md rounded-full px-3 py-1.5 border border-green-500/20">
          <TrendingUp className="w-4 h-4 text-green-400 mr-1.5" />
          <span className="text-xs font-medium text-green-400">{t('news.hotTopics')}</span>
        </div>
        <div className="flex-1 h-[1px] bg-gradient-to-r from-green-500/20 to-transparent ml-3"></div>
      </div>

      {/* Loading State */}
      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-16">
          <Loader2 className="w-8 h-8 animate-spin text-green-400 mb-3" />
          <p className="text-gray-400 text-sm">{t('news.loadingArticles')}</p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-16 px-4">
          <AlertCircle className="w-8 h-8 text-red-400 mb-3" />
          <p className="text-gray-300 text-center mb-3">{error}</p>
          <button 
            onClick={() => fetchNews()} 
            className="px-4 py-2 bg-green-500/20 text-green-400 rounded-full text-sm hover:bg-green-500/30 transition-colors"
          >
            {t('news.tryAgain')}
          </button>
        </div>
      ) : news.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 px-4">
          <p className="text-gray-300 text-center">{t('news.noArticlesFound')}</p>
        </div>
      ) : (
        <>
          {/* News Cards - Removed motion animations for better performance */}
          <div className="space-y-3.5">
            {news.map((article, index) => (
              <NewsCard 
                key={article._id} 
                news={{
                  id: article._id,
                  title: article.title,
                  image: article.image,
                  views: article.viewsCount || 0,
                  likes: article.likesCount || 0,
                  category: article.category,
                  slug: article.slug
                }} 
                index={index}
              />
            ))}
          </div>
          
          {/* Load More Button */}
          {hasMore && (
            <div className="mt-8 text-center">
              <button
                onClick={loadMore}
                disabled={isLoadingMore}
                className="px-5 py-2 bg-black/60 backdrop-blur-md border border-green-500/20 rounded-full text-green-400 text-sm hover:bg-black/80 transition-colors disabled:opacity-50"
              >
                {isLoadingMore ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                    {t('news.loading')}
                  </>
                ) : (
                  t('news.loadMore')
                )}
              </button>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default NewsSection