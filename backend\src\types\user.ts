import { Types } from 'mongoose';

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
}

export interface WalletBalance {
  sol: number;
  flow: number;
  bonk: number;
}

export interface WalletInfo {
  address?: string;
  encryptedPrivateKey?: string;
  balance: WalletBalance;
  lastUpdated?: Date;
}

export interface ReferralStats {
  totalReferrals: number;
  rewardsSol: number;
  rewardsFlow: number;
  rewardsBonk: number;
}

export interface Referral {
  user: string | Types.ObjectId;
  earnedSol: number;
  earnedFlow: number;
  earnedBonk: number;
  joinedAt: Date;
  lastEarned: Date;
}

export interface IUser {
  _id?: string | Types.ObjectId;
  telegramId: string;
  telegramUsername?: string;
  telegramName?: string;
  telegramLanguage?: string;
  telegramPhotoUrl?: string;
  telegramChatId?: string;
  telegramChatType?: string;
  displayName: string;
  wallet?: WalletInfo;
  completedMissions?: string[] | Types.ObjectId[];
  likedNews?: string[] | Types.ObjectId[];
  role: UserRole;
  status: UserStatus;
  lastLogin?: Date;
  lastWithdrawal?: Date;  
  claimsSinceWithdrawal?: number;

  // Referral system
  referredBy?: string | Types.ObjectId;
  referrals?: Referral[];
  referralStats?: ReferralStats;
  redeemedReferralTasks?: string[];
  
  // Daily Streak System
  dailyStreak: {
    currentStreak: number;
    highestStreak: number;
    lastClaimDate: Date | null;
    totalDaysVisited: number;
    streakHistory: Array<{
      date: Date;
      reward: number;
      streakDay: number;
    }>;
    weeklyProgress: number;
    totalRewardsEarned: number;
  };
  
  createdAt?: Date;
  updatedAt?: Date;
  
  // Methods
  addCompletedMission?: (missionId: string) => IUser;
  addLikedNews?: (newsId: string) => IUser;
  removeLikedNews?: (newsId: string) => IUser;
  addReferral: (userId: string) => IUser;
  addReferralReward: (currency: string, amount: number, userId: string) => IUser;
  getTotalReferralEarnings?: () => {sol: number, flow: number, bonk: number};
} 