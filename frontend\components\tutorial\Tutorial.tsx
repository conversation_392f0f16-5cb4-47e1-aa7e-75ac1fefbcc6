"use client"
import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  BookO<PERSON>, 
  Play, 
  CheckCircle, 
  ArrowRight, 
  Wallet, 
  TrendingUp, 
  Users, 
  Gift,
  Star,
  Target,
  Zap,
  Shield
} from 'lucide-react'
import { useLanguage } from '@/context/LanguageContext'

const Tutorial = () => {
  const [activeStep, setActiveStep] = useState(0)
  const { t } = useLanguage()

  const tutorialSteps = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: Play,
      description: 'Learn the basics of Flow Trade and how to get started',
      content: [
        'Welcome to Flow Trade - your advanced crypto trading companion',
        'Connect your wallet to start trading and earning rewards',
        'Explore missions to earn tokens and unlock features',
        'Join our community and start your trading journey'
      ]
    },
    {
      id: 'wallet-setup',
      title: 'Wallet Setup',
      icon: Wallet,
      description: 'Set up and connect your crypto wallet',
      content: [
        'Choose from supported wallets (Phantom, Solflare, etc.)',
        'Connect securely through our verified integration',
        'Your wallet is your key to the Flow Trade ecosystem',
        'Keep your private keys safe and never share them'
      ]
    },
    {
      id: 'trading-basics',
      title: 'Trading Basics',
      icon: TrendingUp,
      description: 'Understanding trading fundamentals',
      content: [
        'Learn about different trading strategies',
        'Understand market analysis and indicators',
        'Start with small amounts to practice',
        'Use our algorithmic tools for better results'
      ]
    },
    {
      id: 'missions-rewards',
      title: 'Missions & Rewards',
      icon: Target,
      description: 'Complete missions to earn tokens',
      content: [
        'Daily missions offer consistent rewards',
        'Partner missions provide special bonuses',
        'Complete social tasks to grow the community',
        'Earn FLOW tokens and SOL rewards'
      ]
    },
    {
      id: 'referrals',
      title: 'Referral System',
      icon: Users,
      description: 'Invite friends and earn together',
      content: [
        'Share your unique referral link',
        'Earn rewards when friends join and trade',
        'Build your network for passive income',
        'Track your referral performance'
      ]
    },
    {
      id: 'advanced-features',
      title: 'Advanced Features',
      icon: Zap,
      description: 'Unlock powerful trading tools',
      content: [
        'Access algorithmic trading strategies',
        'Use advanced charting and analysis tools',
        'Set up automated trading bots',
        'Monitor portfolio performance in real-time'
      ]
    }
  ]

  const quickTips = [
    {
      icon: Shield,
      title: 'Security First',
      description: 'Always verify URLs and never share private keys'
    },
    {
      icon: Star,
      title: 'Start Small',
      description: 'Begin with small amounts to learn and practice'
    },
    {
      icon: Gift,
      title: 'Daily Rewards',
      description: 'Check in daily for missions and bonus rewards'
    }
  ]

  return (
    <div className="max-w-[500px] mx-auto">
      {/* Header */}
      <div className="relative mb-5 flex items-center">
        <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-gradient-to-b from-blue-400 to-transparent rounded-full"></div>
        <h1 className="text-xl font-bold ml-3 text-white">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-blue-300">
            {t('wallet.tutorial')}
          </span>
        </h1>
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-20 h-[1px] bg-gradient-to-r from-blue-500/50 to-transparent"></div>
        <div className="absolute right-5 -top-2 w-3 h-3 rounded-full bg-blue-500/20 blur-xl"></div>
        <div className="absolute right-3 -bottom-2 w-2 h-2 rounded-full bg-blue-500/30 blur-md"></div>
      </div>

      {/* Step Navigation */}
      <div className="grid grid-cols-3 gap-2 mb-5">
        {tutorialSteps.map((step, index) => {
          const Icon = step.icon
          const isActive = activeStep === index
          const isCompleted = activeStep > index
          
          return (
            <div
              key={step.id}
              className={`
                relative cursor-pointer rounded-lg border p-2 flex flex-col items-center justify-center
                transition-all duration-200 backdrop-blur-sm hover:-translate-y-1
                ${isActive 
                  ? 'border-blue-500/50 bg-black/70 shadow-lg shadow-blue-500/10' 
                  : isCompleted
                  ? 'border-green-500/50 bg-black/50'
                  : 'border-gray-800/50 bg-black/30 hover:bg-black/40'}
              `}
              onClick={() => setActiveStep(index)}
            >
              {isActive && (
                <motion.div 
                  className="absolute inset-0 -z-10 rounded-lg opacity-30"
                  layoutId="stepGlow"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  style={{ boxShadow: "0 0 15px 2px rgba(59, 130, 246, 0.2)" }}
                />
              )}

              <div className={`
                mb-1 p-1 rounded
                ${isActive ? 'text-blue-400' : isCompleted ? 'text-green-400' : 'text-gray-400'}
              `}>
                {isCompleted ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <Icon className={`w-4 h-4 ${isActive ? 'stroke-[2.5px]' : 'stroke-[1.5px]'}`} />
                )}
              </div>
              <span className={`
                text-xs font-medium text-center leading-tight
                ${isActive ? 'text-blue-400' : isCompleted ? 'text-green-400' : 'text-gray-400'}
              `}>
                {step.title}
              </span>
              
              {isActive && (
                <motion.div
                  layoutId="activeStepIndicator"
                  className="absolute bottom-0 w-8 h-[2px] bg-blue-500/70 rounded-full"
                  transition={{ duration: 0.2 }}
                />
              )}
            </div>
          )
        })}
      </div>

      {/* Content Area */}
      <div className="relative bg-black/30 backdrop-blur-md rounded-xl border border-gray-800/50 overflow-hidden mb-5">
        <div className="absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"></div>
        <div className="absolute -right-10 -top-10 w-20 h-20 bg-blue-500/5 rounded-full blur-xl"></div>
        <div className="absolute -left-5 bottom-10 w-10 h-10 bg-blue-500/5 rounded-full blur-lg"></div>
        
        <div className="p-4">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mr-3">
                  {React.createElement(tutorialSteps[activeStep].icon, { 
                    className: "w-4 h-4 text-blue-400" 
                  })}
                </div>
                <div>
                  <h3 className="text-base font-semibold text-white">
                    {tutorialSteps[activeStep].title}
                  </h3>
                  <p className="text-xs text-gray-400">
                    {tutorialSteps[activeStep].description}
                  </p>
                </div>
              </div>
              
              <div className="space-y-2">
                {tutorialSteps[activeStep].content.map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-start space-x-2"
                  >
                    <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-sm text-gray-300">{item}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
        
        {/* Navigation Buttons */}
        <div className="flex justify-between items-center bg-black/30 p-3 border-t border-gray-800/50">
          <button
            onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
            disabled={activeStep === 0}
            className="text-xs text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed hover:text-white transition-colors"
          >
            Previous
          </button>
          
          <div className="flex space-x-1">
            {tutorialSteps.map((_, index) => (
              <div
                key={index}
                className={`w-1.5 h-1.5 rounded-full transition-colors ${
                  index === activeStep ? 'bg-blue-400' : 'bg-gray-600'
                }`}
              />
            ))}
          </div>
          
          <button
            onClick={() => setActiveStep(Math.min(tutorialSteps.length - 1, activeStep + 1))}
            disabled={activeStep === tutorialSteps.length - 1}
            className="text-xs text-blue-400 disabled:opacity-50 disabled:cursor-not-allowed hover:text-blue-300 transition-colors flex items-center space-x-1"
          >
            <span>Next</span>
            <ArrowRight className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* Quick Tips */}
      <div className="space-y-3">
        <h3 className="text-sm font-semibold text-white mb-3">Quick Tips</h3>
        {quickTips.map((tip, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-black/40 rounded-lg p-3 border border-gray-800/50 flex items-start space-x-3"
          >
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
              <tip.icon className="w-4 h-4 text-blue-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-white mb-1">{tip.title}</h4>
              <p className="text-xs text-gray-400">{tip.description}</p>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

export default Tutorial
