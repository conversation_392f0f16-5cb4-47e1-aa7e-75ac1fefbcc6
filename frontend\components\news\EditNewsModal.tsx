"use client"

import React, { useState, useEffect, useRef } from 'react'
import { useE<PERSON><PERSON>, EditorContent, BubbleMenu } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "react-hot-toast"
import { 
  Bold, 
  Italic, 
  List, 
  ListOrdered, 
  Link as LinkIcon, 
  AlignLeft, 
  AlignCenter, 
  AlignRight,
  Loader2,
  Heading1,
  Heading2,
  Undo,
  Redo,
  Quote,
  ImagePlus,
  X,
  Upload,
  Check
} from 'lucide-react'
import Image from 'next/image'

export interface NewsArticle {
  _id: string;
  title: string;
  slug: string;
  summary: string;
  content: string;
  category: string;
  authorName: string;
  image: string;
  isPublished: boolean;
  isHighlighted: boolean;
  likesCount?: number;
  viewsCount?: number;
  publishedAt?: string | null;
  createdAt?: string;
  likes?: any[]; // Array of like objects with userId and timestamp
  userLiked?: boolean; // Whether the current user has liked this article
}

enum NewsCategory {
  MARKET = 'market',
  TECHNOLOGY = 'technology',
  DEFI = 'defi',
  TRADING = 'trading',
  GENERAL = 'general',
}

// Rich Text Editor Component - Same as in CreateNews
const RichTextEditor = ({ content, onChange }: { content: string, onChange: (html: string) => void }) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3]
        },
        paragraph: {
          HTMLAttributes: {
            class: 'mb-4', // Add margin to paragraphs
          }
        }
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-400 underline cursor-pointer hover:text-blue-300',
        }
      }),
      Placeholder.configure({
        placeholder: 'Write your content here...',
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
        defaultAlignment: 'left',
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      // Ensure proper spacing is maintained in the HTML
      const html = editor.getHTML()
        .replace(/<p><br><\/p>/g, '<p>&nbsp;</p>') // Replace empty paragraphs with non-breaking space
      onChange(html)
    },
    editorProps: {
      attributes: {
        class: 'focus:outline-none min-h-[200px] h-full text-white prose prose-p:my-4 prose-headings:my-6',
      },
    },
  })

  useEffect(() => {
    if (editor && content) {
      // Only update content if it's different to avoid cursor jumping
      if (editor.getHTML() !== content) {
        editor.commands.setContent(content);
      }
    }
  }, [content, editor]);

  if (!editor) {
    return <div className="h-[200px] border rounded-md p-4 bg-slate-800 text-slate-400 border-slate-700">Loading editor...</div>
  }

  const setLink = () => {
    const previousUrl = editor.getAttributes('link').href
    const url = window.prompt('URL', previousUrl)
    
    if (url === null) {
      return
    }

    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run()
      return
    }

    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
  }

  return (
    <div className="rich-text-editor">
      {editor && (
        <BubbleMenu 
          editor={editor} 
          tippyOptions={{ duration: 150 }}
          className="flex gap-1 p-1 bg-slate-800 border border-slate-700 rounded-md shadow-md"
        >
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            className="h-7 w-7 text-slate-300 hover:bg-slate-700"
            onClick={() => editor.chain().focus().toggleBold().run()}
          >
            <Bold className="h-3 w-3" />
          </Button>
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            className="h-7 w-7 text-slate-300 hover:bg-slate-700"
            onClick={() => editor.chain().focus().toggleItalic().run()}
          >
            <Italic className="h-3 w-3" />
          </Button>
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            className="h-7 w-7 text-slate-300 hover:bg-slate-700"
            onClick={setLink}
          >
            <LinkIcon className="h-3 w-3" />
          </Button>
        </BubbleMenu>
      )}
      
      <div className="editor-toolbar flex flex-wrap gap-1 mb-2 p-2 border rounded-md bg-slate-800 border-slate-700">
        <div className="flex gap-1 mr-2 border-r border-slate-700 pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive('heading', { level: 1 }) ? 'bg-slate-700' : ''}`}
          >
            <Heading1 className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive('heading', { level: 2 }) ? 'bg-slate-700' : ''}`}
          >
            <Heading2 className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2 border-r border-slate-700 pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive('bold') ? 'bg-slate-700' : ''}`}
          >
            <Bold className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive('italic') ? 'bg-slate-700' : ''}`}
          >
            <Italic className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2 border-r border-slate-700 pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive('bulletList') ? 'bg-slate-700' : ''}`}
          >
            <List className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive('orderedList') ? 'bg-slate-700' : ''}`}
          >
            <ListOrdered className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive('blockquote') ? 'bg-slate-700' : ''}`}
          >
            <Quote className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2 border-r border-slate-700 pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={setLink}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive('link') ? 'bg-slate-700' : ''}`}
          >
            <LinkIcon className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2 border-r border-slate-700 pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive({ textAlign: 'left' }) ? 'bg-slate-700' : ''}`}
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive({ textAlign: 'center' }) ? 'bg-slate-700' : ''}`}
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={`text-slate-300 hover:bg-slate-700 ${editor.isActive({ textAlign: 'right' }) ? 'bg-slate-700' : ''}`}
          >
            <AlignRight className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            className="text-slate-300 hover:bg-slate-700 disabled:opacity-50"
          >
            <Undo className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            className="text-slate-300 hover:bg-slate-700 disabled:opacity-50"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="border border-slate-700 rounded-md p-4 prose prose-sm max-w-none bg-slate-800 text-slate-200 overflow-y-auto prose-headings:text-slate-200 prose-p:text-slate-300 prose-strong:text-white prose-blockquote:text-slate-300 prose-blockquote:border-slate-600 news-content whitespace-pre-wrap" style={{ minHeight: '200px', maxHeight: '300px' }}>
        <EditorContent editor={editor} />
      </div>
    </div>
  )
}

interface EditNewsModalProps {
  isOpen: boolean;
  onClose: () => void;
  articleId: string | null;
  articleData?: NewsArticle;
  onSuccess: () => void;
}

const EditNewsModal = ({ isOpen, onClose, articleId, articleData, onSuccess }: EditNewsModalProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [formData, setFormData] = useState<NewsArticle>({
    _id: '',
    title: '',
    slug: '',
    summary: '',
    content: '',
    category: NewsCategory.GENERAL,
    authorName: '',
    image: '',
    isPublished: true,
    isHighlighted: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [savingArticle, setSavingArticle] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load article data from props or fetch if not available
  useEffect(() => {
    if (isOpen && articleId) {
      if (articleData) {
        // Use the provided article data directly
        setFormData(articleData);
        setPreviewImage(articleData.image);
        setIsLoading(false);
      } else {
        // Fallback to fetching if data is not provided
        fetchArticle(articleId);
      }
    } else {
      resetForm();
    }
  }, [isOpen, articleId, articleData]);

  const fetchArticle = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/admin/news/${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch article');
      }
      
      const data = await response.json();
      
      if (data.success) {
        setFormData(data.data);
        setPreviewImage(data.data.image);
      } else {
        throw new Error(data.error || 'Failed to fetch article');
      }
    } catch (error) {
      console.error('Error fetching article:', error);
      setError('Failed to load article. Please try again.');
      toast.error('Failed to load article');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: (e.target as HTMLInputElement).checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleContentChange = (html: string) => {
    setFormData({
      ...formData,
      content: html
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Image is too large. Maximum size is 10MB.");
      return;
    }
    
    // Validate file type
    if (!['image/jpeg', 'image/png', 'image/webp', 'image/gif'].includes(file.type)) {
      toast.error("Invalid file type. Please upload JPEG, PNG, WebP or GIF images.");
      return;
    }
    
    // Store the file for later upload
    setSelectedFile(file);
    
    // Show preview of the image
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setPreviewImage(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
    
    toast.success("New image selected. It will be uploaded when you update the article.");
  };
  
  // Helper function to convert file to base64
  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const removeImage = () => {
    setSelectedFile(null);
    setPreviewImage(formData.image); // Reset to original image
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    toast.success("New image removed");
  };

  const resetForm = () => {
    setFormData({
      _id: '',
      title: '',
      slug: '',
      summary: '',
      content: '',
      category: NewsCategory.GENERAL,
      authorName: '',
      image: '',
      isPublished: true,
      isHighlighted: false
    });
    setPreviewImage('');
    setSelectedFile(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form fields
    if (!formData.title) {
      toast.error("Please enter a title");
      return;
    }
    if (!formData.summary) {
      toast.error("Please enter a summary");
      return;
    }
    if (!formData.content) {
      toast.error("Please enter content for the article");
      return;
    }
    
    // Process the content to preserve spacing
    const processedContent = formData.content
      .replace(/<p><br><\/p>/g, '<p>&nbsp;</p>')
      .replace(/<p><\/p>/g, '<p>&nbsp;</p>');
    
    if (!previewImage && !formData.image) {
      toast.error("Please select an image for the article");
      return;
    }
    if (!formData.authorName) {
      toast.error("Please enter an author name");
      return;
    }
    
    // Set overall submitting state
    setIsSubmitting(true);
    
    try {
      let imageUrl = formData.image;
      
      // Upload new image if selected
      if (selectedFile) {
        setUploadingImage(true);
        toast.loading("Uploading new image...");
        
        const imageBase64 = await convertFileToBase64(selectedFile);
        
        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            image: imageBase64,
            folder: 'news_images',
          }),
        });
        
        if (!uploadResponse.ok) {
          throw new Error('Failed to upload image');
        }
        
        const uploadData = await uploadResponse.json();
        imageUrl = uploadData.url;
        setUploadingImage(false);
        toast.success("Image uploaded successfully");
      }
      
      // Update the news article
      setSavingArticle(true);
      toast.loading("Updating article...");
      
      const newsData = {
        ...formData,
        content: processedContent, // Use the processed content
        image: imageUrl
      };
      
      const response = await fetch(`/api/admin/news/${formData._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newsData),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update news article');
      }
      
      const data = await response.json();
      setSavingArticle(false);
      
      toast.success("News article updated successfully!");
      
      // Close modal and refresh news list
      onSuccess();
      onClose();
      
    } catch (error) {
      console.error('Error updating news article:', error);
      toast.error("Failed to update news article. Please try again.");
    } finally {
      setIsSubmitting(false);
      setUploadingImage(false);
      setSavingArticle(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open: boolean) => !open && onClose()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-900 text-white border-slate-700 shadow-lg">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-white">Edit News Article</DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 animate-spin text-blue-400" />
            <span className="ml-2 text-slate-300">Loading article details...</span>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-red-400">{error}</p>
            <Button 
              onClick={() => articleId && fetchArticle(articleId)} 
              variant="outline" 
              className="mt-4 border-slate-600 text-slate-300 hover:bg-slate-800"
            >
              Try Again
            </Button>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-slate-300">Title</Label>
                <Input
                  id="title"
                  name="title"
                  placeholder="Enter article title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="bg-slate-800 border-slate-700 text-white placeholder:text-slate-500 focus:border-blue-500"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category" className="text-slate-300">Category</Label>
                <select
                  id="category"
                  name="category"
                  className="flex h-10 w-full rounded-md border outline-none border-slate-700 bg-slate-800 px-3 py-2 text-sm text-white"
                  value={formData.category}
                  onChange={handleInputChange}
                  required
                >
                  {Object.values(NewsCategory).map((category) => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="authorName" className="text-slate-300">Author Name</Label>
              <Input
                id="authorName"
                name="authorName"
                placeholder="Enter author name"
                value={formData.authorName}
                onChange={handleInputChange}
                required
                className="bg-slate-800 border-slate-700 text-white placeholder:text-slate-500 focus:border-blue-500"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="summary" className="text-slate-300">Summary</Label>
              <Textarea
                id="summary"
                name="summary"
                placeholder="Enter a short summary of the article"
                rows={2}
                value={formData.summary}
                onChange={handleInputChange}
                required
                className="bg-slate-800 border-slate-700 text-white placeholder:text-slate-500 focus:border-blue-500"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="content" className="text-slate-300">Content</Label>
              <RichTextEditor content={formData.content} onChange={handleContentChange} />
            </div>
            
            <div className="space-y-2">
              <Label className="text-slate-300">Featured Image</Label>
              <div className="flex flex-col space-y-4">
                {/* Current image preview */}
                {previewImage && (
                  <div className="relative rounded-md overflow-hidden bg-slate-800 border border-slate-700">
                    <Image 
                      src={previewImage} 
                      alt="Preview" 
                      width={300}
                      height={200}
                      className="w-full h-40 object-cover"
                    />
                    {selectedFile && (
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2 h-8 w-8 bg-slate-800 text-red-400 hover:bg-slate-700 hover:text-red-300"
                        onClick={removeImage}
                        disabled={isSubmitting}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                )}
                
                {/* Change image option */}
                <div>
                  <input
                    type="file"
                    accept="image/jpeg,image/png,image/webp,image/gif"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                    id="image-upload"
                  />
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => fileInputRef.current?.click()}
                    className="w-full flex items-center justify-center gap-2 py-2 border-slate-700 text-slate-300 hover:bg-slate-800"
                    disabled={isSubmitting}
                  >
                    <ImagePlus className="h-4 w-4" />
                    <span>{previewImage ? "Change Image" : "Select Image"}</span>
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPublished"
                  name="isPublished"
                  className="h-4 w-4 rounded border-slate-700 bg-slate-800"
                  checked={formData.isPublished}
                  onChange={handleInputChange}
                  disabled={isSubmitting}
                />
                <Label htmlFor="isPublished" className="text-slate-300">Published</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isHighlighted"
                  name="isHighlighted"
                  className="h-4 w-4 rounded border-slate-700 bg-slate-800"
                  checked={formData.isHighlighted}
                  onChange={handleInputChange}
                  disabled={isSubmitting}
                />
                <Label htmlFor="isHighlighted" className="text-slate-300">Highlighted</Label>
              </div>
            </div>
            
            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
                className="border-slate-700 text-slate-300 hover:bg-slate-800"
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className="min-w-[120px] bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {uploadingImage && "Uploading..."}
                {!uploadingImage && savingArticle && "Saving..."}
                {!uploadingImage && !savingArticle && !isSubmitting && "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditNewsModal; 