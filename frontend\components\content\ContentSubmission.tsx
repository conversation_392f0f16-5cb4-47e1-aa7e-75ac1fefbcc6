"use client"
import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Crown, ExternalLink, Info, Send, AlertCircle, CheckCircle2, HelpCircle, ArrowLeft, Loader2, Wallet } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useLanguage } from '@/context/LanguageContext'
import { useWallet } from '@/context/SolanaWalletContext'

interface Submission {
  _id: string;
  contentLink: string;
  walletAddress: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  rewardAmount: number;
  rewardCurrency: string;
  reviewNote?: string;
  platform?: string;
  txHash?: string;
}

const ContentSubmission = () => {
  const [contentLink, setContentLink] = useState('')
  const [submissions, setSubmissions] = useState<Submission[]>([])
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [hasPendingSubmission, setHasPendingSubmission] = useState(false)
  const router = useRouter()
  const { t } = useLanguage()
  const { connected, publicKey, connect, isLoading: walletLoading } = useWallet()

  // Fetch user's submissions on component mount
  useEffect(() => {
    fetchSubmissions()
  }, [])

  // Fetch submissions from API
  const fetchSubmissions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/user/content-submission')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || t('content.fetchFailed'))
      }

      if (data.success) {
        setSubmissions(data.data.submissions || [])
        setHasPendingSubmission(data.data.hasPendingSubmission || false)
      }
    } catch (error: any) {
      console.error('Error fetching submissions:', error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  // Handle wallet connection
  const handleConnectWallet = async () => {
    try {
      setError('')
      await connect()
    } catch (error: any) {
      console.error('Error connecting wallet:', error)
      setError(error.message || 'Failed to connect wallet')
    }
  }

  // Handle submit function
  const handleSubmit = async () => {
    // Check if wallet is connected
    if (!connected || !publicKey) {
      setError(t('content.walletRequired'))
      return
    }

    if (!contentLink.trim()) {
      setError(t('content.validLinkRequired'))
      return
    }

    try {
      setError('')
      setSuccess('')
      setSubmitting(true)

      const response = await fetch('/api/user/content-submission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contentLink,
          walletAddress: publicKey
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || t('content.submitFailed'))
      }

      setSuccess(t('content.submitSuccess'))
      setContentLink('')
      fetchSubmissions() // Refresh the list
    } catch (error: any) {
      console.error('Error submitting content:', error)
      setError(error.message)
    } finally {
      setSubmitting(false)
    }
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Get status badge style
  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-500/20 border-green-500/30 text-green-400'
      case 'rejected':
        return 'bg-red-500/20 border-red-500/30 text-red-400'
      default:
        return 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400'
    }
  }

  // Translate submission status
  const translateStatus = (status: string) => {
    return t(`content.status.${status}`)
  }

  return (
    <div className="max-w-[500px] mx-auto relative z-10">
      {/* Glowing Background Effect */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-48 h-48 bg-green-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-green-500/5 rounded-full blur-3xl"></div>
      </div>

      {/* Header Section with Back Button */}
      <div className="flex justify-between items-center mb-6">
        <motion.div
          className="relative flex items-center"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-gradient-to-b from-green-400 to-transparent rounded-full"></div>
          <h1 className="text-xl font-bold ml-3 text-white">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-green-300">{t('content.creators')}</span>
          </h1>
          {/* Decorative elements */}
          <div className="absolute -right-5 top-1/2 transform -translate-y-1/2 w-10 h-[1px] bg-gradient-to-r from-green-500/50 to-transparent"></div>
        </motion.div>

        <motion.button
          onClick={() => router.push('/')}
          className="flex cursor-pointer items-center space-x-1.5 bg-black/60 backdrop-blur-md rounded-lg border border-green-500/30 px-3.5 py-2 text-xs font-medium text-green-400"
          whileHover={{ y: -2, boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)" }}
          whileTap={{ scale: 0.97 }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <ArrowLeft className="w-3.5 h-3.5" />
          <span>{t('content.backToHome')}</span>
        </motion.button>
      </div>

      {/* Hero Section */}
      <motion.div
        className="bg-black/60 backdrop-blur-md rounded-xl mb-5 overflow-hidden relative border border-gray-800/50"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        whileHover={{
          borderColor: "rgba(74, 222, 128, 0.15)",
          boxShadow: '0 0 25px rgba(0, 0, 0, 0.2)'
        }}
      >
        {/* Header with Program Label */}
        <div className="bg-black/80 p-3 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <Crown className="w-5 h-5 text-green-400" />
            <div className="text-base font-bold text-white flex items-center space-x-2">
              <span>{t('content.contentCreator')}</span>
            </div>
          </div>
          <div className="flex items-center space-x-1 bg-green-500/20 rounded-full px-2 py-0.5 border border-green-500/30">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span className="text-xs text-green-400">{t('content.rewardsProgram')}</span>
          </div>
        </div>

        {/* Hero Image Section */}
        <div className="relative h-48 w-full overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20 mix-blend-overlay"></div>

          <div className="relative h-full w-full bg-gradient-to-r from-green-900/40 to-blue-900/40">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative w-28 h-28 rounded-full bg-orange-100/90 overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center text-2xl">
                  👩‍💻
                </div>
              </div>

              {/* Floating icons around the character */}
              <div className="absolute -top-2 -left-10 transform rotate-12 text-2xl">📱</div>
              <div className="absolute top-10 -right-20 transform -rotate-6 text-2xl">💻</div>
              <div className="absolute -bottom-4 left-24 transform rotate-12 text-2xl">📸</div>
              <div className="absolute top-2 right-10 transform rotate-12 text-2xl">🎮</div>
              <div className="absolute bottom-10 -left-12 transform -rotate-6 text-2xl">🎥</div>
              <div className="absolute -top-8 left-28 transform -rotate-6 text-2xl">💎</div>
              <div className="absolute bottom-0 right-2 transform rotate-12 text-2xl">🚀</div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* About Section */}
      <motion.div
        className="mb-5 bg-black/60 backdrop-blur-md rounded-lg border border-gray-800/50 p-4 relative overflow-hidden"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        whileHover={{
          borderColor: "rgba(74, 222, 128, 0.15)",
          boxShadow: '0 0 20px rgba(0, 0, 0, 0.15)'
        }}
      >
        <div className="absolute -right-10 -top-10 w-20 h-20 bg-green-500/5 rounded-full blur-xl"></div>
        <div className="absolute -left-8 -bottom-8 w-16 h-16 bg-blue-500/5 rounded-full blur-lg"></div>

        <h2 className="text-sm font-medium text-white mb-2">{t('content.about')}</h2>

        <p className="text-sm text-gray-300 mb-3">
          {t('content.aboutDescription')}
        </p>

        <div className="flex items-start space-x-1 text-xs text-green-400 mb-1.5">
          <Info className="w-3.5 h-3.5 mt-0.5 flex-shrink-0" />
          <span>{t('content.earnRewards')}</span>
        </div>
      </motion.div>

      {/* Submission Form */}
      <motion.div
        className="mb-5 bg-black/60 backdrop-blur-md rounded-lg border border-gray-800/50 p-4 relative overflow-hidden"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
        whileHover={{
          borderColor: "rgba(74, 222, 128, 0.15)",
          boxShadow: '0 0 20px rgba(0, 0, 0, 0.15)'
        }}
      >
        <div className="absolute -right-10 -top-10 w-20 h-20 bg-green-500/5 rounded-full blur-xl"></div>

        <h2 className="text-sm font-medium text-white mb-3">{t('content.submitYourContent')}</h2>

        {/* Error message */}
        {error && (
          <div className="mb-3 flex items-center space-x-2 bg-red-500/10 border border-red-500/30 text-red-400 rounded-lg p-3 text-sm">
            <AlertCircle className="w-4 h-4 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}

        {/* Success message */}
        {success && (
          <div className="mb-3 flex items-center space-x-2 bg-green-500/10 border border-green-500/30 text-green-400 rounded-lg p-3 text-sm">
            <CheckCircle2 className="w-4 h-4 flex-shrink-0" />
            <span>{success}</span>
          </div>
        )}

        {!connected ? (
          <div className="space-y-3">
            <div className="mb-3 flex items-center space-x-2 bg-blue-500/10 border border-blue-500/30 text-blue-400 rounded-lg p-3 text-sm">
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span>{t('content.connectWalletWarning') || 'Please connect your wallet to submit content'}</span>
            </div>

            <motion.button
              className="w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 border border-blue-500/30 rounded-lg py-2.5 text-sm font-medium flex items-center justify-center space-x-2"
              whileHover={{ y: -1, boxShadow: "0 4px 12px rgba(59, 130, 246, 0.1)" }}
              whileTap={{ scale: 0.98 }}
              onClick={handleConnectWallet}
              disabled={walletLoading}
            >
              {walletLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>{t('wallet.connecting') || 'Connecting Wallet...'}</span>
                </>
              ) : (
                <>
                  <Wallet className="w-4 h-4" />
                  <span>{t('wallet.connectWallet') || 'Connect Wallet'}</span>
                </>
              )}
            </motion.button>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex justify-between items-center mb-1">
              <label className="block text-xs text-gray-300">
                {t('content.linkLabel')}
              </label>
              <div className="text-xs text-green-400 bg-green-500/10 px-2 py-0.5 rounded-full border border-green-500/20 flex items-center">
                <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5"></div>
                <span>{t('content.walletConnected') || 'Wallet Connected'}</span>
              </div>
            </div>

            <input
              type="text"
              className={`w-full bg-black/80 backdrop-blur-md rounded-lg border ${
                hasPendingSubmission
                  ? 'border-yellow-500/50 bg-yellow-500/5'
                  : 'border-gray-800'
              } py-2.5 px-3.5 text-sm text-white outline-none focus:border-green-500/50`}
              placeholder={t('content.linkPlaceholder')}
              value={contentLink}
              onChange={(e) => setContentLink(e.target.value)}
              disabled={hasPendingSubmission || submitting}
            />

            {hasPendingSubmission && (
              <p className="mt-1.5 text-xs text-yellow-400">
                {t('content.pendingSubmissionWarning')}
              </p>
            )}

            <div className="text-xs text-gray-400 flex items-start space-x-2">
              <Info className="w-3.5 h-3.5 flex-shrink-0 mt-0.5" />
              <span>{t('content.walletAddressInfo') || `Submissions will be linked to your wallet address: ${publicKey?.slice(0, 6)}...${publicKey?.slice(-4)}`}</span>
            </div>

            <motion.button
              className={`w-full ${
                hasPendingSubmission
                  ? 'bg-gray-500/20 text-gray-400 border-gray-500/30 cursor-not-allowed'
                  : 'bg-green-500/20 hover:bg-green-500/30 text-green-400 border border-green-500/30'
              } rounded-lg py-2.5 text-sm font-medium flex items-center justify-center space-x-2`}
              whileHover={hasPendingSubmission ? {} : { y: -1, boxShadow: "0 4px 12px rgba(34, 197, 94, 0.1)" }}
              whileTap={hasPendingSubmission ? {} : { scale: 0.98 }}
              onClick={handleSubmit}
              disabled={hasPendingSubmission || submitting}
            >
              {submitting ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>{t('content.submitting')}</span>
                </>
              ) : (
                <>
                  <Send className="w-4 h-4" />
                  <span>{t('content.submitContent')}</span>
                </>
              )}
            </motion.button>
          </div>
        )}
      </motion.div>

      {/* Previous Submissions Section */}
      <motion.div
        className="mb-5 bg-black/60 backdrop-blur-md rounded-lg border border-gray-800/50 p-4 relative overflow-hidden"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        whileHover={{ 
          borderColor: "rgba(74, 222, 128, 0.15)",
          boxShadow: '0 0 20px rgba(0, 0, 0, 0.15)'
        }}
      >
        <div className="absolute -left-10 -top-10 w-20 h-20 bg-green-500/5 rounded-full blur-xl"></div>
        
        <h2 className="text-sm font-medium text-white mb-3">{t('content.yourSubmissions')}</h2>
        
        {loading ? (
          <div className="flex justify-center items-center py-6">
            <Loader2 className="w-6 h-6 text-green-400 animate-spin" />
          </div>
        ) : submissions.length > 0 ? (
          <div className="space-y-3">
            {submissions.map(submission => (
              <div 
                key={submission._id} 
                className="bg-black/80 backdrop-blur-md rounded-lg border border-gray-800 p-3"
              >
                <div className="flex justify-between items-start mb-2">
                  <a 
                    href={submission.contentLink} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-400 hover:underline flex items-center truncate max-w-[300px]"
                  >
                    <span className="truncate">{submission.contentLink}</span>
                    <ExternalLink className="w-3.5 h-3.5 ml-1.5 flex-shrink-0" />
                  </a>
                  <div className={`px-2 py-0.5 text-xs rounded-full border ${getStatusBadgeStyle(submission.status)}`}>
                    {translateStatus(submission.status)}
                  </div>
                </div>
                
                <div className="flex justify-between items-center text-xs text-gray-400">
                  <span>{t('content.submittedOn')} {formatDate(submission.submittedAt)}</span>
                  
                  {submission.status === 'approved' && submission.rewardAmount > 0 && (
                    <span className="text-green-400">
                      {t('content.reward')}: {submission.rewardAmount} {submission.rewardCurrency}
                    </span>
                  )}
                </div>
                
                {submission.reviewNote && (
                  <div className="mt-2 text-xs text-gray-300 bg-white/5 p-2 rounded">
                    <span className="font-medium text-gray-200">{t('content.note')}: </span>
                    {submission.reviewNote}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-gray-400 text-sm">
            {t('content.noSubmissionsYet')}
          </div>
        )}
      </motion.div>
      
      {/* Guidelines Section */}
      <motion.div
        className="mb-5 bg-black/60 backdrop-blur-md rounded-lg border border-gray-800/50 p-4 relative overflow-hidden"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        whileHover={{ 
          borderColor: "rgba(74, 222, 128, 0.15)",
          boxShadow: '0 0 20px rgba(0, 0, 0, 0.15)'
        }}
      >
        <div className="absolute -left-10 -top-10 w-20 h-20 bg-green-500/5 rounded-full blur-xl"></div>
        
        <div className="flex items-center mb-3">
          <h2 className="text-sm font-medium text-white">{t('content.guidelines')}</h2>
          <div className="flex-1 h-[1px] bg-gradient-to-r from-gray-800/50 to-transparent ml-3"></div>
        </div>
        
        <div className="space-y-3">
          <div className="flex">
            <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-br from-blue-900/60 to-blue-500/30 shadow-inner flex items-center justify-center mr-2.5 mt-0.5">
              <span className="text-xs text-white">1</span>
            </div>
            <p className="text-sm text-gray-300">
              <span className="font-medium text-white">{t('content.guideline1.title')}: </span>
              {t('content.guideline1.description')}
            </p>
          </div>
          
          <div className="flex">
            <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-br from-green-900/60 to-green-500/30 shadow-inner flex items-center justify-center mr-2.5 mt-0.5">
              <span className="text-xs text-white">2</span>
            </div>
            <p className="text-sm text-gray-300">
              <span className="font-medium text-white">{t('content.guideline2.title')}: </span>
              {t('content.guideline2.description')}
            </p>
          </div>
          
          <div className="flex">
            <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-br from-green-900/60 to-green-500/30 shadow-inner flex items-center justify-center mr-2.5 mt-0.5">
              <span className="text-xs text-white">3</span>
            </div>
            <p className="text-sm text-gray-300">
              <span className="font-medium text-white">{t('content.guideline3.title')}: </span>
              {t('content.guideline3.description')}
            </p>
          </div>
          
          <div className="flex">
            <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gradient-to-br from-green-900/60 to-green-500/30 shadow-inner flex items-center justify-center mr-2.5 mt-0.5">
              <span className="text-xs text-white">4</span>
            </div>
            <p className="text-sm text-gray-300">
              <span className="font-medium text-white">{t('content.guideline4.title')}: </span>
              {t('content.guideline4.description')}
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default ContentSubmission 