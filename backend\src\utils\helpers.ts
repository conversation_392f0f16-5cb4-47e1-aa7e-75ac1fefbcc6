/**
 * Generates a unique referral code for a user
 * @param username User's Telegram username
 * @param telegramId User's Telegram ID
 * @returns Unique referral code
 */
export function generateReferralCode(username: string, telegramId: string): string {
  // Create a base from username (or part of telegram ID if no username)
  const base = username !== 'Unknown' 
    ? username.replace(/[^a-zA-Z0-9]/g, '').toLowerCase() 
    : telegramId.slice(-5);
  
  // Add random characters for uniqueness
  const randomChars = Math.random().toString(36).substring(2, 6);
  
  // Add timestamp component for additional uniqueness
  const timestamp = Date.now().toString(36).slice(-4);
  
  return `${base}${randomChars}${timestamp}`;
}

/**
 * Formats a currency value with proper formatting
 * @param amount Amount to format
 * @param currency Currency code
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2
  });
  
  return formatter.format(amount);
}

/**
 * Safely access nested object properties
 * @param obj Object to access
 * @param path Path to the property
 * @param defaultValue Default value if property doesn't exist
 * @returns Property value or default value
 */
export function getNestedValue(obj: any, path: string, defaultValue: any = undefined): any {
  const keys = path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result === undefined || result === null) {
      return defaultValue;
    }
    result = result[key];
  }
  
  return result === undefined ? defaultValue : result;
}

/**
 * Truncates text to a specified length and adds ellipsis
 * @param text Text to truncate
 * @param maxLength Maximum length
 * @returns Truncated text
 */
export function truncateText(text: string, maxLength: number = 100): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
} 