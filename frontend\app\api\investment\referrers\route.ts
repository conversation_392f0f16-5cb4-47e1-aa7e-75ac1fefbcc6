import { NextRequest, NextResponse } from 'next/server';
import { getValidReferrers } from '../create/route';
import { cookies } from 'next/headers';

// GET handler to fetch valid referrers for the current user
export async function GET(req: NextRequest) {
  try {
    // Get user ID from cookies or query params
    // const userId = (await cookies()).get('userId')?.value;
    const userId = "68380e3e3b44aa85b374c1f0"; // Hardcoded for testing
    
    const { searchParams } = new URL(req.url);
    const queryUserId = searchParams.get('userId');
    
    const targetUserId = queryUserId || userId;
    
    if (!targetUserId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    const validReferrers = await getValidReferrers(targetUserId);
    
    return NextResponse.json({
      success: true,
      data: {
        referrers: validReferrers,
        count: validReferrers.length,
        message: validReferrers.length > 0 
          ? `Found ${validReferrers.length} valid referrers with active investments`
          : 'No valid referrers found'
      }
    });
    
  } catch (error: any) {
    console.error('Error fetching valid referrers:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch referrers' },
      { status: 500 }
    );
  }
} 