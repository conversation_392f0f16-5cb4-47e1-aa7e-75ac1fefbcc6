import mongoose from 'mongoose';

// Define database connection options
const options: mongoose.ConnectOptions = {
  // Use the new URL parser
  // No longer needed as it's now the default but included for clarity
  // useNewUrlParser: true,
  
  // Use the new topology engine
  // No longer needed as it's now the default but included for clarity
  // useUnifiedTopology: true,
  
  // Maximum number of connections in the pool
  maxPoolSize: 10,
  
  // Minimum number of connections to keep open
  minPoolSize: 2,
  
  // Maximum milliseconds to wait for a connection to become available
  serverSelectionTimeoutMS: 5000,
  
  // Maximum number of milliseconds to wait for a socket to connect
  connectTimeoutMS: 10000,
  
  // Maximum time in milliseconds for socket operations (read, write, ping, etc.)
  socketTimeoutMS: 45000,
};

// Connection state tracking
interface ConnectionState {
  isConnected: number;
}

const connectionState: ConnectionState = {
  isConnected: 0, // 0 = disconnected, 1 = connected, 2 = connecting, 3 = disconnecting
};

/**
 * Database connection function
 * Reuses existing connection when possible
 */
export async function connectToDatabase(): Promise<typeof mongoose> {
  // If we're already connected, return the existing connection
  if (connectionState.isConnected === 1) {
    console.log('Using existing database connection');
    return mongoose;
  }

  // If no connection or previously disconnected, connect
  if (mongoose.connections.length === 0 || mongoose.connections[0].readyState !== 1) {
    try {
      // Get MongoDB URI from environment variables
      const uri = process.env.MONGODB_URI || "mongodb+srv://devtar28k:<EMAIL>/";
      
      // Ensure the URI is defined
      if (!uri) {
        throw new Error("MONGODB_URI environment variable is not defined");
      }
      
      // Connect to database
      console.log('Connecting to MongoDB...');
      const connection = await mongoose.connect(uri, options);
      
      // Update connection state
      connectionState.isConnected = connection.connections[0].readyState;
      
      console.log('Connected to MongoDB');
      return connection;
    } catch (error) {
      console.error('Error connecting to database:', error);
      throw error;
    }
  }

  // Return the existing connection
  return mongoose;
}

/**
 * Disconnect from database (useful for testing)
 */
export async function disconnectFromDatabase(): Promise<void> {
  if (connectionState.isConnected === 0) {
    return;
  }
  
  if (process.env.NODE_ENV === 'production') {
    // In production, don't disconnect to maintain connection pooling
    console.log('Keeping database connection alive in production');
    return;
  }
  
  try {
    await mongoose.disconnect();
    connectionState.isConnected = 0;
    console.log('Disconnected from database');
  } catch (error) {
    console.error('Error disconnecting from database:', error);
    throw error;
  }
}

/**
 * Retry database operation with exponential backoff
 * @param operation Function to execute
 * @param retries Maximum number of retries
 * @param delay Initial delay in milliseconds
 * @param maxDelay Maximum delay in milliseconds
 */
export async function withDatabaseRetry<T>(
  operation: () => Promise<T>,
  retries = 3,
  delay = 500,
  maxDelay = 5000
): Promise<T> {
  try {
    return await operation();
  } catch (error: any) {
    // If no more retries, throw the error
    if (retries <= 0) throw error;
    
    // Check if error is related to database connection
    if (error.name === 'MongoNetworkError' || 
        error.name === 'MongoServerSelectionError' ||
        error.message.includes('topology was destroyed')) {
      
      // Calculate delay with exponential backoff
      const backoffDelay = Math.min(delay * Math.pow(2, retries), maxDelay);
      console.log(`Database operation failed, retrying in ${backoffDelay}ms...`);
      
      // Wait for the delay
      await new Promise(resolve => setTimeout(resolve, backoffDelay));
      
      // Try to reconnect
      await connectToDatabase();
      
      // Retry operation with one less retry
      return await withDatabaseRetry(operation, retries - 1, delay, maxDelay);
    }
    
    // Otherwise, throw the error
    throw error;
  }
}

export { mongoose };

// Export a simple database connection check function
export async function checkDbConnection(): Promise<boolean> {
  try {
    await connectToDatabase();
    return mongoose.connection.readyState === 1;
  } catch (error) {
    console.error('Database connection check failed:', error);
    return false;
  }
}