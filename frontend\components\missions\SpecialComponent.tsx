"use client"
import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { KeyRound, Check, Send } from 'lucide-react'
import { useLanguage } from '@/context/LanguageContext'

const SpecialComponent = () => {
  const [promoCode, setPromoCode] = useState('')
  const [toastVisible, setToastVisible] = useState(false)
  const [toastMessage, setToastMessage] = useState('')
  const { t } = useLanguage()

  const showToast = useCallback((message: string) => {
    setToastMessage(message)
    setToastVisible(true)
    setTimeout(() => {
      setToastVisible(false)
    }, 3000)
  }, [])

  const handleSubmit = useCallback(() => {
    if (promoCode.trim() === '') {
      showToast(t('missions.enterPromoCode'))
      return
    }
    
    showToast(t('missions.promoCodeSubmitted'))
    setPromoCode('')
  }, [promoCode, showToast, t])

  return (
    <div className="space-y-3">
      <div className="bg-black/60 backdrop-blur-sm rounded-lg p-4 overflow-hidden relative">
        {/* Decorative elements */}
        <div className="absolute -top-10 -right-10 w-20 h-20 bg-green-500/5 rounded-full blur-xl"></div>
        <div className="absolute -bottom-5 -left-5 w-10 h-10 bg-green-500/10 rounded-full blur-lg"></div>
        
        <div className="text-center mb-3">
          <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-black border border-green-500/30 mb-2">
            <KeyRound className="w-5 h-5 text-green-400" />
          </div>
          <h3 className="text-white text-sm font-medium">{t('missions.enterPromoCodeTitle')}</h3>
          <p className="text-gray-400 text-xs mt-1">{t('missions.redeemSpecialRewards')}</p>
        </div>
        
        <div className="flex relative">
          <div className="absolute top-0 left-0 right-0 bottom-0 bg-green-500/5 rounded-lg blur-sm"></div>
          <div className="relative flex-1">
            <input
              type="text"
              value={promoCode}
              onChange={(e) => setPromoCode(e.target.value)}
              placeholder={t('missions.enterCodeHere')}
              className="w-full bg-black/70 text-white border border-gray-800 focus:border-green-500/50 rounded-l-lg py-2 px-3 text-sm outline-none"
            />
          </div>
          
          <button
            onClick={handleSubmit}
            className="relative bg-green-500/20 hover:bg-green-500/30 text-green-400 text-xs font-medium py-2 px-3 rounded-r-lg border border-green-500/30 flex items-center space-x-1 transition-colors duration-200 hover:bg-opacity-30 active:scale-95"
          >
            <span>{t('missions.submit')}</span>
            <Send className="w-3 h-3 ml-0.5" />
          </button>
        </div>
      </div>

      {/* Toast notification */}
      <motion.div
        className="fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50"
        initial={{ opacity: 0, y: 20 }}
        animate={{ 
          opacity: toastVisible ? 1 : 0,
          y: toastVisible ? 0 : 20
        }}
        transition={{ duration: 0.3 }}
      >
        {toastVisible && (
          <div className="bg-green-500/20 backdrop-blur-md border border-green-500/30 text-green-400 px-4 py-2 rounded-lg flex items-center space-x-2">
            <Check className="w-4 h-4" />
            <span>{toastMessage}</span>
          </div>
        )}
      </motion.div>
    </div>
  )
}

export default SpecialComponent 