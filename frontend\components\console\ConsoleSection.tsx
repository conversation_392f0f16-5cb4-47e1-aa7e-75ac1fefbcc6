"use client"

import <PERSON><PERSON><PERSON> from "./MovingFan";
import { useState, useEffect, useRef } from 'react';
import MemeOracle from "./MemeOracle";
import { useAuth } from "@/context/AuthContext";
import { useWallet } from "@/context/SolanaWalletContext";
import DailyStreak from "./DailyStreak";
import Leaderboard from "./Leaderboard";
import { TIME_CONFIG } from "@/libs/config";
import { showErrorToast, showSuccessToast } from "../initial/ToasterProvider";
import Link from "next/link";
import { createInvestment, claimRoi, addMoreFunds } from "@/libs/solana/contract";

// Define the claim history item type
interface ClaimHistoryItem {
  timestamp: Date;
  amount: number;
  type: 'zap' | 'referral';  // Updated to match backend schema
}

// Define the investment type based on our API
interface Investment {
  _id: string;
  user: string;
  initialAmount: number;              // Initial investment amount
  earnedAmount: number;               // Total earned from zapping and referrals
  withdrawnAmount: number;            // Total amount withdrawn
  currency: string;                   // 'sol', 'flow', 'bonk'
  startDate: Date;
  status: string;                     // 'active', 'completed', 'cancelled'
  claimsSinceWithdrawal: number;      // Number of claims since last withdrawal
  lastCalculationTime: Date;          // Time of last calculation
  currentZapProgress: number;         // percentage accumulated so far (0-1.5%)
  calculatedZapProgress?: number;     // From API calculation
  canClaimZappedRewards?: boolean;    // From API calculation
  canWithdraw?: boolean;              // From API calculation
  lastClaimTime: Date | null;
  claims: ClaimHistoryItem[];
  referralBonus: number;              // Total zap earned through referrals
  createdAt: Date;
  updatedAt: Date;
  currentValue?: number;              // Calculated field from API
  percentageOfROI?: number;           // Added missing property
  zapMetrics?: {                      // Added metrics from API
    initialInvestment: number;
    totalEarned: number;
    currentBalance: number;
    totalWithdrawn: number;
    claimsSinceWithdrawal: number;
    claimsRemainingForWithdrawal: number;
    referralBonus: number;
    maxPotentialEarnings: number;
    progressTowardMaxROI: number;
  };
}

export default function ConsoleSection() {
  // Auth and wallet context
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { connected, publicKey, wallet, isLoading: walletLoading } = useWallet();
  
  // Enhanced state management for API calls
  const [isInvested, setIsInvested] = useState(false);
  const [investment, setInvestment] = useState<Investment | null>(null);

  
  // API loading states
  const [loadingStates, setLoadingStates] = useState({
    investment: true,       // Loading investment data
    claim: false,          // Claiming rewards
    create: false,         // Creating new investment
    investMore: false,     // Adding to investment
    withdraw: false        // Withdrawing funds
  });
  
  // Helper function to set loading state for a specific operation
  const setLoading = (operation: keyof typeof loadingStates, isLoading: boolean) => {
    setLoadingStates(prev => ({ ...prev, [operation]: isLoading }));
  };
  
  // Quick access variables
  const isLoadingInvestment = loadingStates.investment;
  const isProcessingClaim = loadingStates.claim;
  const isProcessingCreate = loadingStates.create;
  const isProcessingInvestMore = loadingStates.investMore;
  const isProcessingWithdraw = loadingStates.withdraw;
  
  // Any operation in progress
  const isProcessing = Object.values(loadingStates).some(state => state);
  
  // Zapping tracking
  const [totalZapProgress, setTotalZapProgress] = useState(0); // Overall zap percentage (0-250%)
  const [zapAccumulation, setZapAccumulation] = useState(0); // Claimable zap percentage (0-1.5%)
  const [totalEarned, setTotalEarned] = useState(0); // Total earned from zapping
  const [timeRemaining, setTimeRemaining] = useState({ days: 0, hours: 0, minutes: 0 });
  const [timeUntilNextClaim, setTimeUntilNextClaim] = useState('');
  
  // Investment modal
  const [showModal, setShowModal] = useState(false);
  const [investmentAmount, setInvestmentAmount] = useState('');
  const [selectedCurrency, setSelectedCurrency] = useState('sol');
  const modalRef = useRef<HTMLDivElement>(null);
  
  // Summary modal
  const [showSummaryModal, setShowSummaryModal] = useState(false);
  const summaryModalRef = useRef<HTMLDivElement>(null);
  
  // Fan animation controls
  const [fanIsActive, setFanIsActive] = useState(false);
  const [fanSpeed, setFanSpeed] = useState(1);
  
  // Add new state for additional investment amount
  const [additionalInvestmentAmount, setAdditionalInvestmentAmount] = useState('');
  const [showInvestMoreModal, setShowInvestMoreModal] = useState(false);
  const investMoreModalRef = useRef<HTMLDivElement>(null);
  
  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setShowModal(false);
      }
      if (summaryModalRef.current && !summaryModalRef.current.contains(event.target as Node)) {
        setShowSummaryModal(false);
      }
      if (investMoreModalRef.current && !investMoreModalRef.current.contains(event.target as Node)) {
        setShowInvestMoreModal(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Add a proper initialization useEffect
  useEffect(() => {
    // Initialize states
    if (!isAuthenticated ) {
      setLoading('investment', false);
      setIsInvested(false);
      setInvestment(null);
      setFanIsActive(false);
      setTotalZapProgress(0);
      setZapAccumulation(0);
      setTotalEarned(0);
      return;
    }
    
    // Load initial investment data
    fetchInvestment();
  }, [isAuthenticated, connected]);
  
  // Add a helper function at the top of ConsoleSection to calculate max possible ROI
  const getMaxPossibleROI = () => {
    return TIME_CONFIG.TOTAL_ROI_CAP * 100; // 250%
  };
  
  // Improve the useEffect for fetching and updating investment data
  useEffect(() => {
    if (!isAuthenticated ) {
      // Reset investment states when not authenticated
      setInvestment(null);
      setIsInvested(false);
      setFanIsActive(false);
      setLoading('investment', false);
      return;
    }
    
    const fetchAndUpdateInvestment = async () => {
      try {
        setLoading('investment', true);
        const response = await fetch('/api/investment');
        const data = await response.json();
        
        if (data.success && data.data) {
          // We have an active investment
          setInvestment(data.data);
          setIsInvested(true);
          
          // Calculate zap percentage based on investment data
          const initialAmount = data.data.initialAmount;
          const earnedAmount = data.data.earnedAmount;
          
          // Use percentageOfROI from API if available, otherwise calculate it
          // This represents how much of the initial investment has been earned (0-250%)
          const zapPercentage = data.data.percentageOfROI !== undefined 
            ? data.data.percentageOfROI 
            : (earnedAmount / initialAmount) * 100;
          
          // Set total progress (0-250%)
          setTotalZapProgress(Math.min(getMaxPossibleROI(), zapPercentage));
          
          // Get latest accumulation data and update UI
          const accumulation = data.data.calculatedZapProgress || data.data.currentZapProgress;
          setZapAccumulation(accumulation);
          setTotalEarned(earnedAmount);
          
          // Set fan speed and activity based on zap progress
          // Fan stops at 250% (max ROI cap)
          const newFanSpeed = 0.5 + (zapPercentage / getMaxPossibleROI()) * 1.5; // Scale between 0.5-2
          setFanSpeed(zapPercentage >= getMaxPossibleROI() ? 0 : newFanSpeed);
          setFanIsActive(zapPercentage < getMaxPossibleROI());
          
          // Calculate time remaining for next claim if not ready
          if (!data.data.canClaimZappedRewards) {
            calculateTimeUntilNextClaim(accumulation);
          }
        } else {
          // No active investment - reset all states
          setInvestment(null);
          setIsInvested(false);
          setFanIsActive(false);
          setTotalZapProgress(0);
          setZapAccumulation(0);
          setTotalEarned(0);
          setTimeUntilNextClaim('');
        }
      } catch (error) {
        console.error("Error refreshing investment data:", error);
        // On error, leave current state intact but log the error
      } finally {
        setLoading('investment', false);
      }
    };
    
    // Initial fetch
    fetchAndUpdateInvestment();
    
    // No need for interval fetch - we'll update only when needed
    
  }, [isAuthenticated, connected]);
  
  // Additional timer for updating zap accumulation values between API calls
  useEffect(() => {
    if (!isAuthenticated || !isInvested || !investment) return;
    
    const updateLocalAccumulation = () => {
      // Don't update if already at max ROI
      if (totalZapProgress >= getMaxPossibleROI()) return;
      
      // Calculate time elapsed since last calculation
    const now = new Date();
      const lastCalc = new Date(investment.lastCalculationTime);
      const elapsedSeconds = (now.getTime() - lastCalc.getTime()) / 1000;
      
      // Standard accumulation rate using TIME_CONFIG
      const secondRate = TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE / TIME_CONFIG.ROI_ACCUMULATION_SECONDS;
      
      // Get current accumulation, preferring zap progress fields
      const currentAccumulationValue = investment.currentZapProgress ?? 0;
      
      // Update accumulation locally
      let newAccumulation = currentAccumulationValue + (elapsedSeconds * secondRate);
      
      // Cap at configured claim percentage and ensure it can reach exactly 100%
      newAccumulation = Math.min(newAccumulation, TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE);
      if (Math.abs(newAccumulation - TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE) < 0.0001) {
        newAccumulation = TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE;
      }
      
      // Update state with latest calculation
      setZapAccumulation(newAccumulation);
      
      // Update button state if we've reached the claim threshold
      const canClaim = investment.canClaimZappedRewards ?? false;
      if (newAccumulation >= TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE && !canClaim) {
        // Clone investment object and update canClaim property
        setInvestment(prev => {
          if (!prev) return null;
          return {
            ...prev, 
            canClaimZappedRewards: true,
            currentZapProgress: TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE // Ensure it's exactly at the threshold
          };
        });
      }
    };
    
    // Update every second for smooth progress bar movement
    const progressTimerId = setInterval(updateLocalAccumulation, 1000);
    
    return () => clearInterval(progressTimerId);
  }, [isAuthenticated, isInvested, investment, totalZapProgress]);
  
  // Improve the time until next claim function - updated for testing
  const calculateTimeUntilNextClaim = (currentAccumulation: number) => {
    // Ensure we have a valid accumulation value
    const validAccumulation = currentAccumulation || 0;
    const remainingPercentage = TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE - validAccumulation;
    const secondRate = TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE / TIME_CONFIG.ROI_ACCUMULATION_SECONDS;
    
    // Handle edge case when accumulation is very close to the threshold
    if (remainingPercentage < 0.0001) {
      setTimeUntilNextClaim('0s');
      return;
    }
    
    const secondsRemaining = remainingPercentage / secondRate;
    
    // Format time message to show seconds for testing
    let timeMessage = `${Math.round(secondsRemaining)}s`;
    
    setTimeUntilNextClaim(timeMessage);
  };
  
  // Format date for display (e.g., "July 15, 2023")
  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return '—';
    
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };
  
  // Enhanced function for starting a new investment with validation-first approach
  const handleInvest = async () => {
    if (!investmentAmount || parseFloat(investmentAmount) <= 0) return;
    
    if (!wallet || !connected) {
      showErrorToast("Please connect your wallet first");
      return;
    }
    
    const amount = parseFloat(investmentAmount);
    
    // Set specific loading state for create operation
    setLoading('create', true);
    
    try {
      // Step 1: Validate the investment creation first
      console.log('Validating investment creation...');
      const validationResponse = await fetch('/api/investment/validate/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount,
          walletAddress: publicKey,
          currency: selectedCurrency,
        }),
      });
      
      if (!validationResponse.ok) {
        const errorText = await validationResponse.text().catch(() => 'Validation failed');
        showErrorToast(`Validation error: ${errorText}`);
        setLoading('create', false);
        return;
      }
      
      const validationData = await validationResponse.json();
      
      if (!validationData.success) {
        showErrorToast(`Validation failed: ${validationData.error || 'Unknown validation error'}`);
        setLoading('create', false);
        return;
      }
      
      if (!validationData.data.isValid) {
        showErrorToast(validationData.error || 'Investment validation failed');
        setLoading('create', false);
        return;
      }
      
      // Step 2: Get valid referrers from validation response
      const validReferrers = validationData.data.validReferrers || [];
      console.log(`Validation passed. Found ${validReferrers.length} valid referrers:`, validReferrers);
      
      // Step 3: Call the Solana smart contract
      console.log('Proceeding with smart contract call...');
      const contractResult = await createInvestment(wallet as any, amount, validReferrers);
      
      if (!contractResult.success) {
        showErrorToast(`Smart contract error: ${contractResult.error || 'Unknown error'}`);
        setLoading('create', false);
        return;
      }
      
      // Step 4: Create the investment in the database
      console.log('Creating investment in database...');
      const response = await fetch('/api/investment/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount,
          walletAddress: publicKey,
          currency: selectedCurrency,
          txId: contractResult.txId // Pass the transaction ID for verification
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        showErrorToast(`Database error: ${errorText || response.statusText}`);
        setLoading('create', false);
        return;
      }
      
      const data = await response.json();
      
      if (data.success) {
        showSuccessToast("Investment created successfully! Your AI-powered Zapping engine is now active.");
        setShowModal(false);
        setInvestmentAmount('');
        
        // Refresh investment data to show the new investment
        await fetchInvestment();
      } else {
        showErrorToast(data.error || "Failed to create investment");
      }
      
    } catch (error) {
      console.error("Error in investment creation process:", error);
      showErrorToast("An error occurred while creating your investment. Please try again.");
    } finally {
      setLoading('create', false);
    }
  };
  
  // Enhanced claim function with validation-first approach
  const handleClaimZappedRewards = async () => {
    if (!investment) {
      showErrorToast("No active zapping session found");
      return;
    }
    
    if (!wallet || !connected) {
      showErrorToast("Please connect your wallet first");
      return;
    }
    
    // Set specific loading state for claim operation
    setLoading('claim', true);
    
    try {
      // Step 1: Validate the claim first
      console.log('Validating claim...');
      const validationResponse = await fetch('/api/investment/validate/claim', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}), // No additional data needed for claim validation
      });
      
      if (!validationResponse.ok) {
        const errorText = await validationResponse.text().catch(() => 'Validation failed');
        showErrorToast(`Validation error: ${errorText}`);
        setLoading('claim', false);
        return;
      }
      
      const validationData = await validationResponse.json();
      
      if (!validationData.success) {
        showErrorToast(`Validation failed: ${validationData.error || 'Unknown validation error'}`);
        setLoading('claim', false);
        return;
      }
      
      if (!validationData.data.isValid) {
        showErrorToast(validationData.error || 'Claim validation failed');
        setLoading('claim', false);
        return;
      }
      
      const claimInfo = validationData.data;
      console.log('Validation passed. Claim info:', claimInfo);
      
      // Step 2: Call the Solana smart contract
      console.log('Proceeding with smart contract call...');
      const contractResult = await claimRoi(wallet as any);
      
      if (!contractResult.success) {
        showErrorToast(`Smart contract error: ${contractResult.error || 'Unknown error'}`);
        setLoading('claim', false);
        return;
      }
      
      // Step 3: Update the database
      console.log('Updating claim in database...');
      const response = await fetch('/api/investment/claim', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          investmentId: investment._id,
          txId: contractResult.txId // Pass the transaction ID for verification
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        showErrorToast(`Database error: ${errorText || response.statusText}`);
        setLoading('claim', false);
        return;
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Successful claim
        const claimAmount = claimInfo.claimData?.claimAmount || 0;
        showSuccessToast(`Zap rewards claimed successfully! +${claimAmount.toFixed(4)} SOL`);
        
        // Update local state with latest claim info
        setInvestment(prev => {
          if (!prev) return null;
          return {
            ...prev,
            lastClaimTime: new Date(),
            earnedAmount: data.data.newTotalClaimed || prev.earnedAmount,
            claimsSinceWithdrawal: data.data.claimsSinceWithdrawal || (prev.claimsSinceWithdrawal + 1),
            percentageOfROI: data.data.percentageOfROI || prev.percentageOfROI,
            currentZapProgress: 0,  // Reset progress after claim
            canClaimZappedRewards: false, // Reset claim availability
            claims: [
              ...(prev.claims || []),
              {
                timestamp: new Date(),
                amount: data.data.amountClaimed || claimAmount,
                type: 'zap'
              }
            ]
          };
        });
        
        // Update total progress based on new data
        const newPercentage = data.data.percentageOfROI || claimInfo.claimData?.roiAfterClaim || 0;
        setTotalZapProgress(Math.min(getMaxPossibleROI(), newPercentage));
        setTotalEarned(data.data.newTotalClaimed || claimInfo.claimData?.totalAfterClaim || 0);
        
        // Check if we've reached max ROI
        const isMaxReached = newPercentage >= getMaxPossibleROI();
        if (isMaxReached) {
          setFanIsActive(false);
          setFanSpeed(0);
          showSuccessToast("🎉 Maximum ROI reached! Time to withdraw and start a new session!");
        } else {
          // Calculate new fan speed based on updated progress
          const newFanSpeed = 0.5 + (newPercentage / getMaxPossibleROI()) * 1.5;
          setFanSpeed(newFanSpeed);
        }
        
      } else {
        // Server rejected the claim
        showErrorToast(data.error || "Failed to claim zap rewards");
        // Refresh data since there might be issues with our local state
        await fetchInvestment();
      }
      
    } catch (error) {
      console.error("Error in claim process:", error);
      showErrorToast("An error occurred while claiming your zap rewards. Please try again.");
      // Refresh data if there was an error
      await fetchInvestment();
    } finally {
      setLoading('claim', false);
    }
  };

  // Enhanced function for adding to existing investment with validation-first approach
  const handleInvestMore = async () => {
    if (!investment || !additionalInvestmentAmount || parseFloat(additionalInvestmentAmount) <= 0) return;
    
    if (!wallet || !connected) {
      showErrorToast("Please connect your wallet first");
      return;
    }
    
    const amount = parseFloat(additionalInvestmentAmount);
    
    setLoading('investMore', true);
    
    try {

      const validationResponse = await fetch('/api/investment/validate/investMore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount,
          walletAddress: publicKey,
          investmentId: investment._id,
        }),
      });
      
      if (!validationResponse.ok) {
        const errorText = await validationResponse.text().catch(() => 'Validation failed');
        showErrorToast(`Validation error: ${errorText}`);
        setLoading('investMore', false);
        return;
      }
      
      const validationData = await validationResponse.json();
      
      if (!validationData.success) {
        showErrorToast(`Validation failed: ${validationData.error || 'Unknown validation error'}`);
        setLoading('investMore', false);
        return;
      }
      
      if (!validationData.data.isValid) {
        showErrorToast(validationData.error || 'Investment validation failed');
        setLoading('investMore', false);
        return;
      }
      
      // Step 2: Get valid referrers from validation response
      const validReferrers = validationData.data.validReferrers || [];
      console.log(`Validation passed. Found ${validReferrers.length} valid referrers:`, validReferrers);
      
      // Step 3: Call the Solana smart contract
      console.log('Proceeding with smart contract call...');
      const contractResult = await addMoreFunds(wallet as any, amount, validReferrers);
      
      if (!contractResult.success) {
        showErrorToast(`Smart contract error: ${contractResult.error || 'Unknown error'}`);
        setLoading('investMore', false);
        return;
      }
      
      // Step 4: Update the database
      console.log('Updating investment in database...');
      const response = await fetch('/api/investment/investMore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount,
          investmentId: investment._id,
          walletAddress: publicKey,
          txId: contractResult.txId // Pass the transaction ID for verification
        }),
      });
      
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        showErrorToast(`Database error: ${errorText || response.statusText}`);
        setLoading('investMore', false);
        return;
      }
      
      const data = await response.json();
      
      if (data.success) {
        const newTotal = validationData.data.investmentData?.newTotalAmount || (investment.initialAmount + amount);
        showSuccessToast(`Investment boosted successfully! New total: ${newTotal.toFixed(4)} SOL`);
        setShowInvestMoreModal(false);
        setAdditionalInvestmentAmount('');
        
        // Refresh investment data to show updated amounts
        await fetchInvestment();
      } else {
        showErrorToast(data.error || "Failed to add investment");
      }
      
    } catch (error) {
      console.error("Error in invest more process:", error);
      showErrorToast("An error occurred while adding to your investment. Please try again.");
    } finally {
      setLoading('investMore', false);
    }
  };
  
  // Enhanced fetchInvestment function with better error handling and loading states
  const fetchInvestment = async () => {
    if (!isAuthenticated) {
      setLoading('investment', false);
      setIsInvested(false);
      setInvestment(null);
      setFanIsActive(false);
      setTotalZapProgress(0);
      setTotalEarned(0);
      return;
    }
    
    setLoading('investment', true);
    try {
      const response = await fetch('/api/investment');
     
      // Handle connection errors
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Failed to load investment data');
        console.error('Investment fetch error:', errorText);
        
        // Set states for no investment rather than showing error for normal case
        setInvestment(null);
        setIsInvested(false);
        setFanIsActive(false);
        setTotalZapProgress(0);
        setTotalEarned(0);
        return;
      }
      
      const data = await response.json();
      
      if (data.success && data.data) {
        // We have an active investment
        setInvestment(data.data);
        setIsInvested(true);
        
        // Calculate zap percentage based on investment data
        const initialAmount = data.data.initialAmount;
        const earnedAmount = data.data.earnedAmount;
        
        // Use percentageOfROI from API if available, otherwise calculate it
        // This is the percentage of initial investment that has been earned (0-250%)
        const zapPercentage = data.data.percentageOfROI !== undefined 
          ? data.data.percentageOfROI 
          : (earnedAmount / initialAmount) * 100;
        
        // Set total progress (0-250%)
        setTotalZapProgress(Math.min(getMaxPossibleROI(), zapPercentage));
        setTotalEarned(earnedAmount);
        
        // Set fan speed and activity based on zap progress
        // Fan stops at 250% (max ROI cap)
        const newFanSpeed = 0.5 + (zapPercentage / getMaxPossibleROI()) * 1.5; // Scale between 0.5-2
        setFanSpeed(zapPercentage >= getMaxPossibleROI() ? 0 : newFanSpeed);
        setFanIsActive(zapPercentage < getMaxPossibleROI());
        
      } else {
        // No active investment - reset all states
        setInvestment(null);
        setIsInvested(false);
        setFanIsActive(false);
        setTotalZapProgress(0);
        setTimeUntilNextClaim('');
        setTotalEarned(0);
        
        // Only show error if there's actually an error message
        if (data.error && data.error !== 'No active investment found') {
          console.warn('Investment API error:', data.error);
        }
      }
    } catch (error) {
      console.error("Error fetching investment data:", error);
      
      // Reset states for error case
      setInvestment(null);
      setIsInvested(false);
      setFanIsActive(false);
      setTotalZapProgress(0);
      setTotalEarned(0);
      
      // Only show error toast for actual network/server errors
      showErrorToast("Failed to load investment data. Please refresh the page.");
    } finally {
      setLoading('investment', false);
    }
  };

  // Update the renderZapMeter function to use the local timer state
  const renderZapMeter = () => {
    if (!investment) return null;
    
    // Calculate time elapsed since last claim or investment creation
    const now = new Date();
    const lastTime = investment.lastClaimTime ? new Date(investment.lastClaimTime) : new Date(investment.createdAt);
    const elapsedSeconds = (now.getTime() - lastTime.getTime()) / 1000;
    
    // Calculate percentage filled based on elapsed time (0-100%)
    let percentageFilled = Math.min(100, (elapsedSeconds / TIME_CONFIG.ROI_ACCUMULATION_SECONDS) * 100);
    
    // If elapsed time is very close to the required time, set to exactly 100%
    if (elapsedSeconds >= TIME_CONFIG.ROI_ACCUMULATION_SECONDS - 0.5) {
      percentageFilled = 100;
    }
    
    // Determine if it's ready to claim
    const isClaimable = elapsedSeconds >= TIME_CONFIG.ROI_ACCUMULATION_SECONDS;
    
    // Check if max ROI has been reached (250% of initial)
    const isMaxRoiReached = totalZapProgress >= getMaxPossibleROI();
    
    return (
      <>
        <div className="flex justify-between items-center mb-2 relative mx-auto">
          <div className="text-sm text-gray-400">Zap Claim Progress</div>
          <div className={`text-sm font-medium ${isClaimable ? 'text-green-400' : 'text-green-500'}`}>
            {isClaimable ? '100' : percentageFilled.toFixed(0)}% / 100%
          </div>
        </div>
        
        <div className="h-6 bg-black/70 rounded-full border border-gray-800 overflow-hidden relative">
          {/* Progress bar with smooth transition */}
          <div 
            className="h-full rounded-full transition-all duration-300 ease-out"
            style={{ 
              width: `${isClaimable ? 100 : percentageFilled}%`,
              background: 'linear-gradient(90deg, #22c55e 0%, #10b981 100%)',
              boxShadow: isClaimable ? '0 0 15px rgba(16, 185, 129, 0.5)' : 'none'
            }}
          ></div>
          
          {/* Progress labels */}
          <div className="absolute inset-0 flex justify-between items-center px-4">
            <div className="text-xs text-white font-medium">0%</div>
            {isClaimable ? (
              <div className="text-xs text-white font-medium animate-pulse">
                Ready to Claim
              </div>
            ) : (
              <div className="text-xs text-white font-medium">
                {percentageFilled.toFixed(0)}%
              </div>
            )}
            <div className="text-xs text-white font-medium">1.5%</div>
          </div>
        </div>
        
        {/* Time until next claim if not ready */}
        {!isClaimable && timeUntilNextClaim && !isMaxRoiReached && (
          <div className="mt-1 text-xs text-gray-400 text-right">
            Next Zap Claim In: <span className="text-green-400 font-medium">
              {timeUntilNextClaim.endsWith('s') 
                ? `${Math.floor(parseInt(timeUntilNextClaim.slice(0,-1)) / 3600)}h ${Math.floor((parseInt(timeUntilNextClaim.slice(0,-1)) % 3600) / 60)}m ${parseInt(timeUntilNextClaim.slice(0,-1)) % 60}s`
                : timeUntilNextClaim} 
            </span>
          </div>
        )}
        
        {/* Max ROI reached message */}
        {isMaxRoiReached && (
          <div
            className="mt-[5px] text-xs text-yellow-400 text-right cursor-help"
            title="Maximum Zap Level Reached! Withdraw funds to start a new session or add more Solana to continue zapping."
          >
            Max Zap Reached! Withdraw and start new session.
          </div>
        )}
        
        {/* Button section */}
        <div className="mt-4 flex justify-end gap-2">
          <button
            onClick={() => setShowSummaryModal(true)}
            className="px-5 py-2 rounded-md text-sm font-medium transition-all duration-300 flex items-center space-x-2 bg-gray-800 text-white border border-gray-700 hover:bg-gray-700"
            disabled={isProcessing}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 2a1 1 0 00-1 1v1a1 1 0 002 0V3a1 1 0 00-1-1zM4 4h3a3 3 0 006 0h3a2 2 0 012 2v9a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm2.5 7a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm2.45 4a2.5 2.5 0 10-4.9 0h4.9zM12 9a1 1 0 100 2h3a1 1 0 100-2h-3zm-1 4a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
            <span>Summary</span>
          </button>

          <button
            onClick={handleClaimZappedRewards}
            disabled={!isClaimable || isProcessing || isMaxRoiReached || loadingStates.claim}
            className={`px-5 py-2 rounded-md w-[170px] text-sm font-medium transition-all duration-300 flex items-center space-x-2
              ${isClaimable && !isProcessing && !isMaxRoiReached
                ? 'bg-green-600 text-white shadow-lg shadow-green-600/30 border border-green-500 hover:bg-green-700' 
                : 'bg-gray-900 text-gray-400 border border-gray-800 cursor-not-allowed'}
            `}
            style={{
              boxShadow: isClaimable && !isMaxRoiReached ? '0 0 12px rgba(16, 185, 129, 0.4)' : 'none',
            }}
          >
            {isProcessing ? (
              <span>{loadingStates.claim ? 'Claiming...' : 'Processing...'}</span>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1.5l-1.7-1.7A1 1 0 0012.2 2H7.8a1 1 0 00-.7.3L5.5 4H4zm7 5a1 1 0 10-2 0v1H8a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
                </svg>
                <span>{isMaxRoiReached ? <Link href="/wallet">Withdraw</Link> : "Claim Rewards"}</span>
              </>
            )}
          </button>
        </div>

        <div className="w-full flex mt-[10px]">
          <button
            onClick={() => {
              setShowSummaryModal(false);
              setShowInvestMoreModal(true);
            }}
            className="px-6 py-3 rounded-md w-full text-sm font-medium bg-green-600 text-white hover:bg-green-700 transition-colors border border-green-500"
            disabled={isProcessing}
          >
            Fuel the Zap
          </button>
        </div>
      </>
    );
  };
  
  return (
    <div className="max-w-[1000px] mx-auto pt-6 pb-10 relative">
      {
        loadingStates.investment ? (
          <div className="flex justify-center items-center h-screen">
            <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-green-500"></div>
          </div>
        ) : <div className="flex flex-col">
        {/* Main dashboard content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 mx-auto">
          {/* Left Column - Fan visualization */}
          <div className="lg:col-span-2">
            <div className="bg-black/30 backdrop-blur-md rounded-xl border border-gray-800/50 overflow-hidden p-4 md:p-6">
              {/* Authentication/Wallet Status */}
              {(!isAuthenticated ) && (
                <div className="mb-6 p-4 bg-black/50 rounded-lg border border-yellow-500/30">
                  <h3 className="text-lg font-medium text-white mb-2">Setup Required</h3>
                  {!isAuthenticated && (
                    <div className="text-sm text-yellow-400 mb-2">
                      Please log in to access investment features.
                    </div>
                  )}
                  {/* {isAuthenticated && !connected && (
                    <div className="text-sm text-yellow-400">
                      Please connect your wallet to invest.
                      <WalletConnectButton/>
                    </div>
                  )} */}
                </div>
              )}
              
     
              {/* Investment status indicator */}
              {isAuthenticated  && (
              <div className="flex flex-wrap justify-between items-center mb-4 md:mb-6 gap-y-2 mx-auto min-w-[300px]">
                <div>
                    <h3 className="text-lg font-medium text-white">Zapping Status</h3>
                  <div className="flex items-center mt-1">
                      <div className={`w-2 h-2 rounded-full ${isInvested ? 'bg-green-500' : 'bg-gray-500'} mr-2`}></div>
                      <span className={`text-sm ${isInvested ? 'text-green-500' : 'text-gray-500'}`}>
                        {isLoadingInvestment ? 'Loading...' : (isInvested ? 'Active Investment' : 'No Active Investment')}
                      </span>
                    </div>
                  </div>
                 <div className="flex gap-[10px] items-center justify-center">
                  <DailyStreak/> <Leaderboard/>
                 </div>
                </div>
              )}
              
              {/* Zapping status message */}
              {isInvested && (
                <div className="text-center mb-4">
                  <div className="inline-block px-6 py-1 bg-green-900/20 rounded-full border border-green-500/30">
                    <span className="text-green-400 text-xs font-medium">
                      {fanIsActive ? "Zapping in Progress..." : "Maximum Zap Level Reached!"}
                    </span>
                  </div>
                </div>
              )}
              
              {/* Fan zap visualization */}
              <div className="flex justify-center">
                <MovingFan 
                  isOn={fanIsActive} 
                  speed={fanSpeed} 
                  currentZapProgress={totalZapProgress}
                  maxZapProgress={getMaxPossibleROI()}
                  investmentAmount={investment?.initialAmount || 0}
                  totalEarned={totalEarned}
                  isInvested={isInvested}
                />
              </div>
              
              {/* AI Engine Zapping message */}
              {isInvested && (
                <div className="text-center mt-3 mb-2">
                  <span className="text-sm text-green-400/80 italic">
                    {fanIsActive 
                      ? `Your AI Engine is Zapping your ${investment?.currency.toUpperCase()} gains` 
                      : `Claim your final rewards and withdraw your ${investment?.currency.toUpperCase()} to invest more!`}
                  </span>
                </div>
              )}
              
              {/* Linear zap Meter or Invest Button based on investment status */}
              <div className="mt-8">
                {isInvested && investment ? (
                  // Linear zap Meter (1.5% claimable) for active investments
                  <>
                    {renderZapMeter()}
                  </>
                ) : (
                  // Invest Button for no investment state - only shown if authenticated and wallet connected
                  <div className="mt-4 flex justify-center">
                    {isAuthenticated && connected ? (
                      <button
                      onClick={() => setShowModal(true)}
                        className="px-6 py-3 rounded-md text-sm font-bold transition-all duration-300 flex items-center space-x-2 bg-green-600 text-white shadow-lg shadow-green-600/30 border border-green-500 hover:bg-green-700 hover:scale-[1.02] active:scale-[0.98]"
                      style={{
                        boxShadow: '0 0 15px rgba(16, 185, 129, 0.4)',
                      }}
                        disabled={loadingStates.investment || isProcessing}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                      </svg>
                        <span>Start AI-Powered Zapping</span>
                      </button>
                    ) : (
                      <div className="text-center text-gray-400">
                        {!isAuthenticated && "Log in to start Zapping"}
                        {isAuthenticated && !connected && "Connect your wallet to start Zapping"}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          
        </div>
      </div>
      }
      
      {/* Investment Modal */}
        {showModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
          <div 
              ref={modalRef}
              className="bg-black/80 backdrop-blur-xl rounded-xl border border-green-500/30 p-6 max-w-md w-full shadow-2xl"
            style={{ 
              opacity: 1,
              transform: 'scale(1)'
            }}
            >
              <div className="relative">
                {/* Close button */}
                <button 
                  onClick={() => setShowModal(false)}
                  className="absolute -right-2 -top-2 w-8 h-8 rounded-full bg-black border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                disabled={isProcessing}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
                
                {/* Header */}
              <h2 className="text-xl font-bold text-white mb-6">Start AI-Powered Zapping</h2>
                
                {/* Investment Form */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                    Wallet Address
                    </label>
                    <div className="relative">
                    <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between bg-blue-500/20 border-[0.3px] border-blue-400 p-3 rounded-xl">
            <div className="flex items-center gap-2">
              <span className="text-sm text-blue-300">
                {publicKey ? `${publicKey.slice(0, 7)}...${publicKey.slice(-4)}` : ''}
              </span>
            </div>
          </div>
        </div>
                      </div>
                    </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                    Investment Amount
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={investmentAmount}
                        onChange={(e) => setInvestmentAmount(e.target.value)}
                        placeholder="Enter amount"
                        className="w-full px-4 py-3 bg-black/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none transition-colors"
                        min="0"
                        step="0.01"
                      disabled={isProcessing}
                      />
                      </div>
                    </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Currency
                  </label>
                  </div>
                  
                  <div className="bg-green-900/20 border border-green-900/50 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm font-medium text-green-400">Investment Details</span>
                    </div>
                    <ul className="text-xs text-gray-300 space-y-1">
                    <li>• AI-powered 1% daily Zap rewards (up to 250% total)</li>
                    <li>• Claim 1.5% Zap rewards when ready (~36 hours)</li>
                    <li>• 250-day zapping period</li>
                    <li>• Claim 5 zap rewards before withdrawing</li>
                    </ul>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      onClick={handleInvest}
                    disabled={!investmentAmount || parseFloat(investmentAmount) <= 0 || isProcessing}
                      className={`px-6 py-3 rounded-md text-sm font-bold transition-all duration-300 
                      ${investmentAmount && parseFloat(investmentAmount) > 0 && !isProcessing
                          ? 'bg-green-600 text-white shadow-lg shadow-green-600/30 border border-green-500 hover:bg-green-700' 
                          : 'bg-gray-900 text-gray-400 border border-gray-800 cursor-not-allowed'}
                      `}
                    >
                    {isProcessing ? "Processing..." : "Start Zapping Engine"}
                    </button>
                  </div>
                </div>
              </div>
          </div>
          </div>
        )}
      
      {/* Investment Summary Modal */}
      {showSummaryModal && isInvested && investment && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 ">
          <div 
              ref={summaryModalRef}
            className="bg-black/80 max-h-[80vh] overflow-y-auto backdrop-blur-xl rounded-xl border border-green-500/30 p-6 max-w-md w-full shadow-2xl"
            style={{ 
              opacity: 1,
              transform: 'scale(1)'
            }}
            >
              <div className="relative">
                {/* Close button */}
                <button 
                  onClick={() => setShowSummaryModal(false)}
                  className="absolute -right-2 -top-2 w-8 h-8 rounded-full bg-black border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
                
                {/* Header */}
                <h2 className="text-xl font-bold text-white mb-6 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 3a1 1 0 000 2h10a1 1 0 100-2H3zm0 4a1 1 0 000 2h6a1 1 0 100-2H3zm0 4a1 1 0 100 2h4a1 1 0 100-2H3z" clipRule="evenodd" />
                  </svg>
                Zapping Summary
                </h2>
                
                {/* Summary Content */}
                <div className="space-y-5">
                  {/* Investment Card */}
                  <div className="bg-green-900/20 border border-green-900/50 rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm font-medium text-green-400">Investment Details</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-xs text-gray-400">Initial Investment</div>
                      <div className="text-lg font-semibold text-white">
                        {investment.initialAmount.toLocaleString()} {investment.currency.toUpperCase()}
                      </div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-400">Current Value</div>
                        <div className="text-lg font-semibold text-green-500">
                        {( investment.earnedAmount.toFixed(4))} {investment.currency.toUpperCase()}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* ROI Progress */}
                  <div className="bg-black/40 rounded-lg p-4 border border-gray-800/50">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                        </svg>
                      <span className="text-sm font-medium text-white">Zapping Progress</span>
                      </div>
                    <div className="text-green-500 font-semibold">{totalZapProgress.toFixed(1)}%</div>
                    </div>
                    
                  {/* Zapping Progress Bar */}
                    <div className="h-3 bg-black/70 rounded-full border border-gray-800 overflow-hidden relative mb-2">
                      <div 
                        className="h-full rounded-full"
                        style={{ 
                        width: `${Math.min(100, (totalZapProgress / (getMaxPossibleROI())) * 100)}%`,
                          background: 'linear-gradient(90deg, #22c55e 0%, #eab308 50%, #ef4444 100%)',
                        }}
                      ></div>
                    </div>
                    
                  {/* Zapping Progress Labels */}
                    <div className="flex justify-between text-xs text-gray-400">
                      <div>0%</div>
                      <div>125%</div>
                      <div>250%</div>
                    </div>
                  </div>
                
                {/* Investment Stats */}
                <div className="bg-black/40 rounded-lg p-4 border border-gray-800/50 mt-4">
                  <div className="flex items-center mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm font-medium text-white">Investment Stats</span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 mt-2">
                    <div>
                      <div className="text-xs text-gray-400">Initial Investment</div>
                      <div className="text-sm text-white font-medium">
                        {investment.initialAmount.toLocaleString()} {investment.currency.toUpperCase()}
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-400">Zap Claimed </div>
                      <div className="text-sm text-green-500 font-medium">
                        {investment.earnedAmount - investment.referralBonus} {investment.currency.toUpperCase()}
                      </div>
                    </div>

                    <div>
                      <div className="text-xs text-gray-400">Withdrawn</div>
                      <div className="text-sm text-blue-500 font-medium">
                        {investment.withdrawnAmount.toLocaleString()} {investment.currency.toUpperCase()}
                      </div>
                    </div>
                    {investment.referralBonus > 0 && (
                      <div>
                        <div className="text-xs text-gray-400">Referral Bonuses</div>
                        <div className="text-sm text-blue-500 font-medium">
                          {investment.referralBonus.toFixed(4)} {investment.currency.toUpperCase()}
                        </div>
                      </div>
                    )}
                    </div>
                  </div>
                  
                  {/* Timeline Information */}
                  <div className="bg-black/40 rounded-lg p-4 border border-gray-800/50">
                    <div className="flex items-center mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M6 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 100-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm font-medium text-white">Timeline</span>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <div className="text-xs text-gray-400">Started</div>
                        <div className="text-sm text-white">{formatDate(investment.startDate)}</div>
                      </div>
                      <div className="flex justify-between">
                      <div className="text-xs text-gray-400">Last Zap Claim</div>
                        <div className="text-sm text-white">
                        {investment.lastClaimTime ? formatDate(investment.lastClaimTime) : 'Never'}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Claim History */}
                {investment.claims && investment.claims.length > 0 && (
                    <div className="bg-black/40 rounded-lg p-4 border border-gray-800/50">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 100-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                          </svg>
                        <span className="text-sm font-medium text-white">Zap History</span>
                        </div>
                      <div className="text-green-500 text-xs">
                        Total: {investment.earnedAmount.toFixed(4)} {investment.currency.toUpperCase()}
                      </div>
                      </div>
                      
                      <div className="space-y-2 max-h-36 overflow-y-auto custom-scrollbar pr-1">
                      {investment.claims.slice().reverse().map((claim, i) => (
                          <div key={i} className="flex justify-between text-sm bg-black/30 rounded p-2">
                          <div className="text-gray-400">
                            {formatDate(claim.timestamp)}
                            {claim.type === 'referral' && (
                              <span className="ml-2 text-xs text-blue-400">(Referral)</span>
                            )}
                          </div>
                                 <div className={`${claim.type === 'referral' ? 'text-blue-500' : 'text-green-500'}`}>
                            +{claim.amount.toFixed(4)} {investment.currency.toUpperCase()}
                          </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
    </div>

              {/* Action Buttons */}
              <div className="mt-6 flex justify-center gap-3">
                  <button
                    onClick={() => setShowSummaryModal(false)}
                    className="px-6 py-3 rounded-md text-sm font-medium bg-gray-800 text-white hover:bg-gray-700 transition-colors border border-gray-700"
                  >
                    Close Summary
                  </button>
                 
                </div>
              </div>
          </div>
          </div>
        )}
      
      {/* Invest More Modal */}
      {showInvestMoreModal && isInvested && investment && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70">
          <div 
            ref={investMoreModalRef}
            className="bg-black/80 backdrop-blur-xl rounded-xl border border-green-500/30 p-6 max-w-md w-full shadow-2xl"
            style={{ 
              opacity: 1,
              transform: 'scale(1)'
            }}
          >
            <div className="relative">
              {/* Close button */}
              <button 
                onClick={() => setShowInvestMoreModal(false)}
                className="absolute -right-2 -top-2 w-8 h-8 rounded-full bg-black border border-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors"
                disabled={isProcessing}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
              
              {/* Header */}
              <h2 className="text-xl font-bold text-white mb-6">Boost Your Zapping Engine</h2>
              
              {/* Current Investment Info */}
              <div className="mb-4 bg-black/40 rounded-lg p-4 border border-gray-800/50">
                <div className="flex items-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm font-medium text-green-400">Current Investment</span>
                </div>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <div className="text-xs text-gray-400">Initial Investment</div>
                    <div className="text-sm font-semibold text-white">
                      {investment.initialAmount.toLocaleString()} {investment.currency.toUpperCase()}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-400">Earned So Far</div>
                    <div className="text-sm font-semibold text-green-500">
                      {investment.earnedAmount.toLocaleString()} {investment.currency.toUpperCase()}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-400">Current Zap Progress</div>
                    <div className="text-sm font-semibold text-yellow-500">
                      {totalZapProgress.toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Add More Form */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Additional Investment Amount
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      value={additionalInvestmentAmount}
                      onChange={(e) => setAdditionalInvestmentAmount(e.target.value)}
                      placeholder="Enter amount"
                      className="w-full px-4 py-3 bg-black/50 border border-gray-700 focus:border-green-500 rounded-lg text-white focus:outline-none transition-colors"
                      min="0"
                      step="0.01"
                      disabled={isProcessing}
                    />
                  </div>
                </div>
                
                <div className="bg-green-900/20 border border-green-900/50 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm font-medium text-green-400">Boost Benefits</span>
                  </div>
                  <ul className="text-xs text-gray-300 space-y-1">
                    <li>• Increases your total investment and potential earnings</li>
                    <li>• Preserves your current earned amount and progress</li>
                    <li>• Accelerates your daily zap rewards</li>
                    <li>• Maintains your 250% to Max zap output </li>
                  </ul>
                </div>
                
                <div className="flex justify-end">
                  <button
                    onClick={handleInvestMore}
                    disabled={!additionalInvestmentAmount || parseFloat(additionalInvestmentAmount) <= 0 || isProcessing}
                    className={`px-6 py-3 rounded-md text-sm font-bold transition-all duration-300 
                    ${additionalInvestmentAmount && parseFloat(additionalInvestmentAmount) > 0 && !isProcessing
                      ? 'bg-green-600 text-white shadow-lg shadow-green-600/30 border border-green-500 hover:bg-green-700' 
                      : 'bg-gray-900 text-gray-400 border border-gray-800 cursor-not-allowed'}
                    `}
                  >
                    {isProcessing ? "Processing..." : "Boost Zapping Engine"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="mt-[20px] px-3 pb-[60px]">
      <MemeOracle/>
      </div>
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(34, 197, 94, 0.5);
          border-radius: 4px;
        }
      `}</style>
    </div>
  );
} 