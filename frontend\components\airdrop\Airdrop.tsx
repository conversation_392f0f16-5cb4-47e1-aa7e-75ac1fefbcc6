"use client"
import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Gift, 
  Calendar, 
  Users, 
  CheckCircle, 
  Clock, 
  Star, 
  Zap,
  Target,
  TrendingUp,
  Wallet,
  ExternalLink,
  Copy,
  Check
} from 'lucide-react'
import { useLanguage } from '@/context/LanguageContext'
import { useAuth } from '@/context/AuthContext'

const Airdrop = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [copied, setCopied] = useState(false)
  const { t } = useLanguage()
  const { user } = useAuth()

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Gift },
    { id: 'eligibility', name: 'Eligibility', icon: CheckCircle },
    { id: 'timeline', name: 'Timeline', icon: Calendar }
  ]

  const airdropPhases = [
    {
      phase: 'Phase 1',
      title: 'Early Adopters',
      status: 'completed',
      date: 'January 2024',
      allocation: '10,000,000 FLOW',
      description: 'Rewards for early platform users and beta testers'
    },
    {
      phase: 'Phase 2',
      title: 'Community Growth',
      status: 'active',
      date: 'March 2024',
      allocation: '25,000,000 FLOW',
      description: 'Ongoing rewards for active community members and traders'
    },
    {
      phase: 'Phase 3',
      title: 'Mainnet Launch',
      status: 'upcoming',
      date: 'Q2 2024',
      allocation: '50,000,000 FLOW',
      description: 'Major airdrop for mainnet launch participants'
    }
  ]

  const eligibilityCriteria = [
    {
      icon: Wallet,
      title: 'Connect Wallet',
      description: 'Connect a supported Solana wallet to the platform',
      completed: !!user,
      points: 100
    },
    {
      icon: Target,
      title: 'Complete Missions',
      description: 'Complete at least 5 missions to earn eligibility points',
      completed: false,
      points: 500
    },
    {
      icon: TrendingUp,
      title: 'Trading Activity',
      description: 'Execute at least 3 successful trades on the platform',
      completed: false,
      points: 1000
    },
    {
      icon: Users,
      title: 'Refer Friends',
      description: 'Invite and onboard at least 2 friends to the platform',
      completed: false,
      points: 750
    },
    {
      icon: Star,
      title: 'Community Engagement',
      description: 'Active participation in community events and social media',
      completed: false,
      points: 300
    }
  ]

  const userStats = {
    totalPoints: 100,
    requiredPoints: 1000,
    currentTier: 'Bronze',
    estimatedReward: '250 FLOW'
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const renderOverview = () => (
    <div className="space-y-4">
      {/* Current Status */}
      <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-4 border border-green-500/20">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-white">Your Airdrop Status</h3>
          <div className="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded-full">
            {userStats.currentTier}
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-gray-400">Points Earned</p>
            <p className="text-lg font-bold text-white">{userStats.totalPoints}</p>
          </div>
          <div>
            <p className="text-xs text-gray-400">Estimated Reward</p>
            <p className="text-lg font-bold text-green-400">{userStats.estimatedReward}</p>
          </div>
        </div>
        <div className="mt-3">
          <div className="flex justify-between text-xs text-gray-400 mb-1">
            <span>Progress to next tier</span>
            <span>{userStats.totalPoints}/{userStats.requiredPoints}</span>
          </div>
          <div className="w-full bg-gray-800 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-green-400 to-blue-400 h-2 rounded-full transition-all duration-500"
              style={{ width: `${(userStats.totalPoints / userStats.requiredPoints) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Airdrop Phases */}
      <div className="space-y-3">
        <h3 className="text-sm font-semibold text-white">Airdrop Phases</h3>
        {airdropPhases.map((phase, index) => (
          <motion.div
            key={phase.phase}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-black/40 rounded-lg p-3 border border-gray-800/50"
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  phase.status === 'completed' ? 'bg-green-400' :
                  phase.status === 'active' ? 'bg-yellow-400' : 'bg-gray-400'
                }`}></div>
                <span className="text-sm font-medium text-white">{phase.title}</span>
              </div>
              <span className={`text-xs px-2 py-1 rounded-full ${
                phase.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                phase.status === 'active' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-gray-500/20 text-gray-400'
              }`}>
                {phase.status}
              </span>
            </div>
            <p className="text-xs text-gray-400 mb-2">{phase.description}</p>
            <div className="flex justify-between text-xs">
              <span className="text-gray-500">{phase.date}</span>
              <span className="text-blue-400 font-medium">{phase.allocation}</span>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )

  const renderEligibility = () => (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <h3 className="text-sm font-semibold text-white mb-2">Eligibility Criteria</h3>
        <p className="text-xs text-gray-400">Complete these tasks to maximize your airdrop allocation</p>
      </div>

      <div className="space-y-3">
        {eligibilityCriteria.map((criteria, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`bg-black/40 rounded-lg p-3 border ${
              criteria.completed ? 'border-green-500/30' : 'border-gray-800/50'
            }`}
          >
            <div className="flex items-center space-x-3">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                criteria.completed 
                  ? 'bg-green-500/20 text-green-400' 
                  : 'bg-gray-500/20 text-gray-400'
              }`}>
                {criteria.completed ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <criteria.icon className="w-4 h-4" />
                )}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-white">{criteria.title}</h4>
                  <span className="text-xs text-blue-400 font-medium">+{criteria.points} pts</span>
                </div>
                <p className="text-xs text-gray-400 mt-1">{criteria.description}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <div className="bg-blue-500/10 rounded-lg p-3 border border-blue-500/20">
        <div className="flex items-center space-x-2 mb-2">
          <Zap className="w-4 h-4 text-blue-400" />
          <span className="text-sm font-medium text-white">Pro Tip</span>
        </div>
        <p className="text-xs text-gray-400">
          Higher point totals result in larger airdrop allocations. Focus on trading activity and referrals for maximum rewards.
        </p>
      </div>
    </div>
  )

  const renderTimeline = () => (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <h3 className="text-sm font-semibold text-white mb-2">Airdrop Timeline</h3>
        <p className="text-xs text-gray-400">Key dates and milestones for the Flow Trade airdrop</p>
      </div>

      <div className="space-y-4">
        <div className="bg-black/40 rounded-lg p-4 border border-gray-800/50">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-4 h-4 text-green-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-white">Snapshot Taken</h4>
              <p className="text-xs text-gray-400">January 15, 2024</p>
            </div>
          </div>
          <p className="text-xs text-gray-300">Initial snapshot of eligible wallets and activity completed.</p>
        </div>

        <div className="bg-black/40 rounded-lg p-4 border border-yellow-500/30">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <Clock className="w-4 h-4 text-yellow-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-white">Ongoing Qualification</h4>
              <p className="text-xs text-gray-400">Now - March 31, 2024</p>
            </div>
          </div>
          <p className="text-xs text-gray-300">Continue earning points through platform activity and missions.</p>
        </div>

        <div className="bg-black/40 rounded-lg p-4 border border-gray-800/50">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-gray-500/20 rounded-lg flex items-center justify-center">
              <Gift className="w-4 h-4 text-gray-400" />
            </div>
            <div>
              <h4 className="text-sm font-medium text-white">Distribution</h4>
              <p className="text-xs text-gray-400">April 2024</p>
            </div>
          </div>
          <p className="text-xs text-gray-300">Airdrop tokens will be distributed to eligible wallets.</p>
        </div>
      </div>

      <div className="bg-purple-500/10 rounded-lg p-3 border border-purple-500/20">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-white">Share & Earn More</h4>
            <p className="text-xs text-gray-400">Invite friends to increase your allocation</p>
          </div>
          <button
            onClick={() => copyToClipboard('https://flowtrade.app/ref/your-code')}
            className="flex items-center space-x-1 bg-purple-500/20 text-purple-400 text-xs px-3 py-1.5 rounded-md hover:bg-purple-500/30 transition-colors"
          >
            {copied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
            <span>{copied ? 'Copied!' : 'Copy Link'}</span>
          </button>
        </div>
      </div>
    </div>
  )

  return (
    <div className="max-w-[500px] mx-auto">
      {/* Header */}
      <div className="relative mb-5 flex items-center">
        <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-gradient-to-b from-yellow-400 to-transparent rounded-full"></div>
        <h1 className="text-xl font-bold ml-3 text-white">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-orange-300">
            {t('wallet.airdropInfo')}
          </span>
        </h1>
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-20 h-[1px] bg-gradient-to-r from-yellow-500/50 to-transparent"></div>
        <div className="absolute right-5 -top-2 w-3 h-3 rounded-full bg-yellow-500/20 blur-xl"></div>
        <div className="absolute right-3 -bottom-2 w-2 h-2 rounded-full bg-yellow-500/30 blur-md"></div>
      </div>

      {/* Tab Navigation */}
      <div className="grid grid-cols-3 gap-3 mb-5">
        {tabs.map((tab) => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id
          
          return (
            <div
              key={tab.id}
              className={`
                relative cursor-pointer rounded-lg border p-3 flex flex-col items-center justify-center
                transition-all duration-200 backdrop-blur-sm hover:-translate-y-1
                ${isActive 
                  ? 'border-yellow-500/50 bg-black/70 shadow-lg shadow-yellow-500/10' 
                  : 'border-gray-800/50 bg-black/30 hover:bg-black/40'}
              `}
              onClick={() => setActiveTab(tab.id)}
            >
              {isActive && (
                <motion.div 
                  className="absolute inset-0 -z-10 rounded-lg opacity-30"
                  layoutId="airdropTabGlow"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  style={{ boxShadow: "0 0 15px 2px rgba(251, 191, 36, 0.2)" }}
                />
              )}

              <div className={`
                mb-1.5 p-1.5 rounded
                ${isActive ? 'text-yellow-400' : 'text-gray-400'}
              `}>
                <Icon className={`w-5 h-5 ${isActive ? 'stroke-[2.5px]' : 'stroke-[1.5px]'}`} />
              </div>
              <span className={`
                text-xs font-medium text-center
                ${isActive ? 'text-yellow-400' : 'text-gray-400'}
              `}>
                {tab.name}
              </span>
              
              {isActive && (
                <motion.div
                  layoutId="activeAirdropTabIndicator"
                  className="absolute bottom-0 w-10 h-[2px] bg-yellow-500/70 rounded-full"
                  transition={{ duration: 0.2 }}
                />
              )}
            </div>
          )
        })}
      </div>

      {/* Content Area */}
      <div className="relative bg-black/30 backdrop-blur-md rounded-xl border border-gray-800/50 overflow-hidden">
        <div className="absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-yellow-500/20 to-transparent"></div>
        <div className="absolute -right-10 -top-10 w-20 h-20 bg-yellow-500/5 rounded-full blur-xl"></div>
        <div className="absolute -left-5 bottom-10 w-10 h-10 bg-yellow-500/5 rounded-full blur-lg"></div>
        
        <div className="p-3">
          <AnimatePresence mode="wait">
            {activeTab === 'overview' && (
              <motion.div
                key="overview"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                {renderOverview()}
              </motion.div>
            )}
            {activeTab === 'eligibility' && (
              <motion.div
                key="eligibility"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                {renderEligibility()}
              </motion.div>
            )}
            {activeTab === 'timeline' && (
              <motion.div
                key="timeline"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                {renderTimeline()}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}

export default Airdrop
