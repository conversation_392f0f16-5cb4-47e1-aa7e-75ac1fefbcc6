{"app": {"name": "Flow Trade", "description": "Fortschrittlicher Krypto-Handelsbot mit algorithmischen Strategien"}, "language": {"saved": "Gespe<PERSON>rt"}, "nav": {"home": "Startseite", "wallet": "Wallet", "trading": "Trading", "referrals": "Empfehlungen", "profile": "Profil", "missions": "<PERSON><PERSON>", "news": "Neuigkeiten"}, "wallet": {"title": "Wallet", "terms": "Bedingungen", "totalAssets": "Gesamtvermögen", "connectWallet": "Wallet verbinden", "walletConnected": "Wallet verbunden", "invest": "Investieren", "withdraw": "<PERSON><PERSON><PERSON><PERSON>", "assets": "Vermögenswerte", "tutorial": "Tutorial", "airdropInfo": "Airdrop-Info", "faqs": "FAQs", "connecting": "Verbindung herstellen..."}, "withdraw": {"title": "Vermögenswerte abheben", "history": "Abhebungsverlauf", "backToWithdrawal": "Zurück zur Abhebung", "walletAddress": "Wall<PERSON><PERSON><PERSON><PERSON><PERSON>", "enterWalletAddress": "<PERSON><PERSON><PERSON> Sie Ihre Wallet-Adresse ein", "currency": "Währung", "availableBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "amountToWithdraw": "Abzuhebender Betrag", "max": "MAX", "withdrawButton": "<PERSON><PERSON><PERSON><PERSON>", "processing": "Wird bearbeitet...", "minimumWithdrawal": "Mindestabhebung", "viewHistory": "<PERSON><PERSON><PERSON><PERSON>", "noHistoryFound": "<PERSON><PERSON>bu<PERSON>sverlauf gefunden", "needWalletConnection": "<PERSON>e müssen eine Wallet verbinden, bevor Sie Vermögenswerte abheben können.", "successful": "Abhebung erfolgreich", "requestSubmitted": "Ihre Abhebungsanfrage wurde erfolgreich eingereicht."}, "refer": {"title": "Empfehlen & Verdienen", "creatorProgram": "Ersteller-Programm", "referrals": "EMPFEHLUNGEN", "solEarned": "SOL VERDIENT", "flowEarned": "FLOW VERDIENT", "bonkEarned": "BONK VERDIENT", "forInvitations": "<PERSON><PERSON><PERSON>", "yourReferralLink": "Ihr Empfehlungslink", "shareViaTelegram": "Über Telegram teilen", "shareTitle": "Tritt mir bei FLOW TRADE bei!", "shareText": "Nutze meinen Empfehlungslink zur Anmeldung:", "referralsTab": "Empfehlungen", "tasksTab": "Aufgaben", "yourReferrals": "Ihre Empfehlungen", "noReferralsYet": "Sie haben noch niemanden empfohlen", "shareToEarn": "Teilen Sie Ihren Empfehlungslink, um Belohnungen zu erhalten!", "noEarningsYet": "<PERSON>ch keine Einnahmen", "active": "Aktiv", "inactive": "Inaktiv"}, "referTasks": {"referralRewards": "Empfehlungsbelohnungen", "inviteFriends": "Laden Sie Freunde ein, um BONK-Token zu verdienen", "refresh": "Aktualisieren", "dataRefreshed": "Empfehlungsdaten aktualisiert", "refreshFailed": "Aktualisierung der Daten fehlgeschlagen", "loginRequired": "<PERSON><PERSON> müssen angemeldet sein, um Belohnungen zu beanspruchen", "claimFailed": "Beanspruchung der Belohnung fehlgeschlagen", "claimSuccess": "{amount} {currency} erfolgreich beansprucht!", "errorClaimingReward": "Fehler beim Beanspruchen der Belohnung. Bitte versuchen Sie es erneut.", "referralCount": "{current}/{required} Empfehlungen", "claimed": "Beansprucht", "claimReward": "Belohnung beanspruchen", "needMoreReferrals": "Mehr Empfehlungen benötigt", "noTasksAvailable": "<PERSON><PERSON> Empfehlungsaufgaben verfügbar", "checkBackLater": "Schauen Sie später wieder vorbei für neue Belohnungsmöglichkeiten"}, "referralTasks": {"rt-001": {"title": "5 Freunde einladen", "description": "Laden Sie 5 neue Benutzer ein, um 10.000 BONK zu erhalten"}, "rt-002": {"title": "10 Freunde einladen", "description": "Laden Sie 10 neue Benutzer ein, um 20.000 BONK zu erhalten"}, "rt-003": {"title": "25 Freunde einladen", "description": "Laden Sie 25 neue Benutzer ein, um 50.000 BONK zu erhalten"}, "rt-004": {"title": "50 Freunde einladen", "description": "Laden Sie 50 neue Benutzer ein, um 100.000 BONK zu erhalten"}, "rt-005": {"title": "100 Freunde einladen", "description": "Laden Sie 100 neue Benutzer ein, um 200.000 BONK zu erhalten"}, "rt-006": {"title": "250 Freunde einladen", "description": "Laden Sie 250 neue Benutzer ein, um 500.000 BONK zu erhalten"}, "rt-007": {"title": "500 Freunde einladen", "description": "Laden Sie 500 neue Benutzer ein, um 1.000.000 BONK zu erhalten"}, "rt-008": {"title": "1000 Freunde einladen", "description": "Laden Sie 1000 neue Benutzer ein, um 2.000.000 BONK zu erhalten"}}, "content": {"creators": "<PERSON><PERSON><PERSON>", "backToHome": "Zurück zur Startseite", "contentCreator": "Content-<PERSON><PERSON><PERSON>", "rewardsProgram": "Belohnungsprogramm", "about": "<PERSON><PERSON>", "aboutDescription": "<PERSON><PERSON><PERSON><PERSON> und teilen Sie Inhalte zu Flow Trade, wie Videos, Kurzfilme, Memes, Animationen, Diagramme und Tutorials. Ihre Kreativität kann in jedem Format glänzen.", "earnRewards": "Verdienen Sie Belohnungen für qualitativ hochwertige Inhalte, die Flow Trade bewerben", "submitYourContent": "Reichen Sie Ihren Inhalt ein", "linkLabel": "<PERSON> zu <PERSON> (Twitter, YouTube, Instagram usw.):", "linkPlaceholder": "https://twitter.com/ihrenutzername/status/123456789", "pendingSubmissionWarning": "Sie haben bereits eine ausstehende Einreichung. Bitte warten Sie auf die Überprüfung, bevor Sie erneut einreichen.", "submitting": "Wird eingereicht...", "submitContent": "Inhalt einreichen", "yourSubmissions": "Ihre Einreichungen", "submittedOn": "Eingereicht am", "reward": "<PERSON><PERSON><PERSON><PERSON>", "note": "<PERSON><PERSON><PERSON><PERSON>", "noSubmissionsYet": "Sie haben noch keine Inhalte eingereicht.", "guidelines": "<PERSON><PERSON><PERSON><PERSON>", "guideline1": {"title": "Inhaltsfokus", "description": "<PERSON><PERSON><PERSON>, dass Flow Trade prominent in Ihrer Kreation erwähnt oder dargestellt wird."}, "guideline2": {"title": "Originalität", "description": "Inhalte müssen originell und von I<PERSON>en erstellt sein. Plagiate führen zur Ablehnung."}, "guideline3": {"title": "Qualität", "description": "Hochwertige Inhalte mit klarem Audio/Video, korrekter Grammatik und professioneller Präsentation."}, "guideline4": {"title": "Genauigkeit", "description": "Informationen müssen korrekt und nicht irreführend sein. <PERSON>ine falschen Versprechungen oder Behauptungen."}, "status": {"pending": "ausstehend", "approved": "<PERSON><PERSON><PERSON><PERSON>", "rejected": "a<PERSON><PERSON><PERSON><PERSON>"}, "fetchFailed": "Fehler beim Abrufen der Einreichungen", "validLinkRequired": "Bitte geben Si<PERSON> einen gültigen Inhaltslink ein", "submitFailed": "Einreichung des Inhalts fehlgeschlagen", "submitSuccess": "Ihr Inhalt wurde erfolgreich eingereicht!", "connectWalletWarning": "Bitte verbinden Sie Ihre Wallet, bevor <PERSON> Ihren Inhalt einreichen können.", "walletConnected": "Wallet Verbunden", "walletAddressInfo": "Einreichungen werden mit Ihrer Wallet-Adresse verknüpft", "walletRequired": "Bitte verbinden Sie Ihre Wallet, um Inhalte einzureichen"}, "news": {"articles": "Artikel", "hotTopics": "Aktuelle Themen", "loadingArticles": "Neueste Artikel werden geladen...", "errorLoading": "Fehler beim Laden der Artikel. Bitte versuchen Sie es erneut.", "tryAgain": "<PERSON><PERSON><PERSON> versuchen", "noArticlesFound": "<PERSON><PERSON> gefunden", "loading": "Wird geladen...", "loadMore": "<PERSON><PERSON> <PERSON>", "fetchFailed": "Fehler beim Abrufen der Artikel", "backToArticles": "<PERSON><PERSON><PERSON> zu Artikeln", "unknownDate": "Unbekanntes Datum", "openInTelegramToInteract": "Bitte in Telegram öffnen, um zu interagieren", "waitForAuthentication": "<PERSON>te warten <PERSON>, während wir Sie authentifizieren", "failedToUpdateLikeStatus": "Fehler beim Aktualisieren des Like-Status", "alreadyLiked": "Sie haben diesen Artikel bereits geliked", "linkCopied": "<PERSON> kop<PERSON>t!", "processing": "Wird bearbeitet...", "liked": "Gefällt mir", "like": "Gefällt mir", "share": "Teilen"}, "missions": {"title": "<PERSON><PERSON>", "tasks": "Aufgaben", "partnerRewards": "Partner-<PERSON><PERSON><PERSON><PERSON><PERSON>", "special": "Spezial", "telegramRequired": "Bitte öffnen Sie die App über Telegram, um Missionen zu starten.", "telegramRequiredClaim": "Bitte öffnen Sie über die Telegram-App, um diese Belohnung zu beanspruchen", "loading": "Wird geladen...", "missionStarted": "Mission gestartet! Verifizierung ausstehend: {minutes}.", "partnerMissionStarted": "Partner-Mission gestartet! Verifizierung ausstehend.", "verificationInProgress": "Missionsverifizierung läuft. Bitte warten Sie bis zur Verifizierung.", "alreadyCompleted": "Sie haben diese Mission bereits abgeschlossen.", "failedToStart": "Fehler beim Starten der Mission", "somethingWentWrong": "Etwas ist schiefgelaufen", "networkError": "Netzwerkfehler", "failedToComplete": "Fehler beim Abschließen der Mission", "missionCompleted": "Mission abgeschlossen! Sie haben {amount} {type} verdient", "noMissionsAvailable": "Keine Missionen derzeit verfügbar.", "noPartnerRewardsAvailable": "<PERSON><PERSON> Partner-Belohnungen derzeit verfügbar.", "estimatedVerificationTime": "Geschätzte Verifizierungszeit", "completed": "Abgeschlossen", "claimReward": "Belohnung beanspruchen", "continueTask": "Aufgabe fortsetzen", "continuePartnerTask": "Partner-Aufgabe fortsetzen", "startTask": "Aufgabe starten", "visitPartner": "Partner besuchen", "enterPromoCode": "Bitte geben Sie einen Promo-Code ein", "promoCodeSubmitted": "Promo-Code eingereicht! 500 Token erhalten!", "enterPromoCodeTitle": "Promo-Code eingeben", "redeemSpecialRewards": "Spezielle Belohnungen und Token einlösen", "enterCodeHere": "Code hier eingeben", "submit": "<PERSON><PERSON><PERSON><PERSON>"}}