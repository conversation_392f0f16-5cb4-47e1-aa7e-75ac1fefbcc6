import { NextResponse } from 'next/server'
import { User } from '@/libs/model/user.schema'
import { Investment } from '@/libs/model/investment.schema'
import { connectToDatabase } from '@/libs/db'

export async function GET() {
  try {
    await connectToDatabase()

    // Fetch top 10 users by referrals
    const topReferrers = await User.aggregate([
      { $match: { 'referralStats.totalReferrals': { $gt: 0 } } },
      { 
        $project: {
          _id: 1,
          displayName: 1,
          telegramUsername: 1,
          telegramPhotoUrl: 1,
          referralCount: '$referralStats.totalReferrals',
          totalRewards: { 
            $add: [
              { $ifNull: ['$referralStats.rewardsFlow', 0] }, 
              { $ifNull: ['$referralStats.rewardsSol', 0] }
            ]
          }
        }
      },
      { $sort: { referralCount: -1 } },
      { $limit: 10 }
    ])

    // Fetch top 10 users by FLOW balance
    const topFlowHolders = await User.aggregate([
      { $match: { 'wallet.balance.flow': { $gt: 0 } } },
      {
        $project: {
          _id: 1,
          displayName: 1,
          telegramUsername: 1,
          telegramPhotoUrl: 1,
          flowBalance: '$wallet.balance.flow',
          dailyStreakDays: { $ifNull: ['$dailyStreak.currentStreak', 0] }
        }
      },
      { $sort: { flowBalance: -1 } },
      { $limit: 10 }
    ])

    // Fetch top 10 investors by total investment amount
    const topInvestors = await Investment.aggregate([
      { 
        $group: {
          _id: '$user',
          totalInvested: { $sum: '$initialAmount' },
          activeInvestments: { 
            $sum: { 
              $cond: [{ $eq: ['$status', 'active'] }, 1, 0]
            }
          },
          totalActiveAmount: {
            $sum: {
              $cond: [{ $eq: ['$status', 'active'] }, '$activeInvestment', 0]
            }
          }
        }
      },
      { $sort: { totalInvested: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      { $unwind: { path: '$userInfo', preserveNullAndEmptyArrays: true } },
      {
        $project: {
          _id: 1,
          displayName: '$userInfo.displayName',
          telegramUsername: '$userInfo.telegramUsername',
          telegramPhotoUrl: '$userInfo.telegramPhotoUrl',
          totalInvested: 1,
          activeInvestments: 1,
          totalActiveAmount: 1
        }
      }
    ])

    // Fetch general statistics
    const stats = await Promise.all([
      // Total users count
      User.countDocuments(),
      // Total investments
      Investment.countDocuments(),
      // Total active investments
      Investment.countDocuments({ status: 'active' }),
      // Total investment volume
      Investment.aggregate([
        { $group: { _id: null, total: { $sum: '$initialAmount' } } }
      ]),
      // Total FLOW in wallets
      User.aggregate([
        { $group: { _id: null, total: { $sum: '$wallet.balance.flow' } } }
      ])
    ])

    return NextResponse.json({
      success: true,
      leaderboards: {
        referrals: topReferrers,
        flow: topFlowHolders,
        investments: topInvestors
      },
      stats: {
        totalUsers: stats[0],
        totalInvestments: stats[1],
        activeInvestments: stats[2],
        totalInvestmentVolume: stats[3][0]?.total || 0,
        totalFlowInWallets: stats[4][0]?.total || 0
      }
    })
  } catch (error) {
    console.error('Error fetching leaderboard data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch leaderboard data' },
      { status: 500 }
    )
  }
} 