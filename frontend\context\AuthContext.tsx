"use client"

import React, { createContext, useState, useEffect, useContext, useCallback } from "react"
import { IUser } from "@/types/user"
import Cookies from "js-cookie"

// Import WebApp dynamically to avoid SSR issues
let WebApp: any = null;
if (typeof window !== 'undefined') {
  try {
    WebApp = require('@twa-dev/sdk').default;
  } catch (e) {
    console.error("Error importing WebApp SDK:", e);
  }
}

interface AuthContextType {
  user: IUser | null
  telegramUser: any
  telegramChatData: any
  isLoading: boolean
  isAuthenticated: boolean
  isTelegram: boolean
  logout: () => void
  registerOrLoginUser: (tgUser: any, chatData?: any) => Promise<IUser | null>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  telegramUser: null,
  telegramChatData: null,
  isLoading: true,
  isAuthenticated: false,
  isTelegram: false,
  logout: () => {},
  registerOrLoginUser: async () => null,
  refreshUser: async () => {}
})

export const useAuth = () => useContext(AuthContext)

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<IUser | null>(null)
  const [telegramUser, setTelegramUser] = useState<any>(null)
  const [telegramChatData, setTelegramChatData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isTelegram, setIsTelegram] = useState(false)


  // Logout function
  const logout = () => {
    setUser(null)
    Cookies.remove("user")
    
    fetch("/api/auth/logout", { method: "POST" })
      .catch(() => {})
  }

  // Register or login a user with Telegram data
  const registerOrLoginUser = async (tgUser: any, chatData?: any) => {
    try {
      if (!tgUser || !tgUser.id) {
        console.error("No valid Telegram user data provided")
        return null
      }
      
      const telegramId = tgUser.id.toString()
      
      console.log("Registering/logging in user with Telegram ID:", telegramId)
      
      // Make the API call to register/login
      const response = await fetch("/api/auth/telegram-auth", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          telegramId,
          telegramData: {
            id: telegramId,
            username: tgUser.username || '',
            first_name: tgUser.first_name || '',
            last_name: tgUser.last_name || '',
            photo_url: tgUser.photo_url || '',
            language_code: tgUser.language_code || '',
          },
          chatData: chatData || null
        }),
        cache: 'no-store',
        next: { revalidate: 0 }
      })
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('Authentication failed:', errorText)
        throw new Error('Authentication failed: ' + errorText)
      }
      
      const data = await response.json()
      
      if (data.success) {
        console.log("User authenticated successfully", data.user)
        // Set user state
        setUser(data.user)
        // Store in cookie for persistence
        Cookies.set("user", JSON.stringify(data.user), { expires: 7 })
        
        if (data.isNewUser) {
          // toast.success("Account created successfully!")
        }
        
        return data.user
      } else {
        console.error("Authentication response not successful:", data)
      }
      
      return null
    } catch (error) {
      console.error("Error in registerOrLoginUser:", error)
      return null
    }
  }

  // Extract full Telegram data including chat and user info
  const extractTelegramData = () => {
    if (typeof window === 'undefined') return { user: null, chat: null, isInTelegram: false }
    
    try {
      let tgUser = null
      let chatData = null
      let isInTelegram = false
      
      // Try WebApp SDK
      if (WebApp && WebApp.initData) {
        isInTelegram = true
        
        // Get user data
        if (WebApp.initDataUnsafe?.user) {
          tgUser = WebApp.initDataUnsafe.user
          console.log("Extracted user data from WebApp SDK:", tgUser)
        }
        
        // Get chat data
        if (WebApp.initDataUnsafe?.chat) {
          chatData = WebApp.initDataUnsafe.chat
          console.log("Extracted chat data from WebApp SDK:", chatData)
        } else if (WebApp.initDataUnsafe?.start_param?.startsWith('chat_')) {
          // Sometimes chat ID is in start_param
          const chatId = WebApp.initDataUnsafe.start_param.replace('chat_', '')
          chatData = { id: chatId }
          console.log("Extracted chat ID from start_param:", chatId)
        }
        
        // For debugging - log the full initData
        console.log("Full WebApp initDataUnsafe:", WebApp.initDataUnsafe)
      }
      
      // Fallback to window.Telegram
      if (!isInTelegram && window.Telegram?.WebApp) {
        isInTelegram = true
        
        // Get user data
        if (window.Telegram.WebApp.initDataUnsafe?.user) {
          tgUser = window.Telegram.WebApp.initDataUnsafe.user
          console.log("Extracted user data from window.Telegram:", tgUser)
        }
        
        // Get chat data
        if (window.Telegram.WebApp.initDataUnsafe?.chat) {
          chatData = window.Telegram.WebApp.initDataUnsafe.chat
          console.log("Extracted chat data from window.Telegram:", chatData)
        } else if (window.Telegram.WebApp.initDataUnsafe?.start_param?.startsWith('chat_')) {
          const chatId = window.Telegram.WebApp.initDataUnsafe.start_param.replace('chat_', '')
          chatData = { id: chatId }
          console.log("Extracted chat ID from window.Telegram start_param:", chatId)
        }
        
        // For debugging
        console.log("Full window.Telegram initDataUnsafe:", window.Telegram.WebApp.initDataUnsafe)
      }
      
      return { user: tgUser, chat: chatData, isInTelegram }
    } catch (error) {
      console.error("Error extracting Telegram data:", error)
      return { user: null, chat: null, isInTelegram: false }
    }
  }

  const initApp = useCallback(async () => {
    setIsLoading(true)
    
    try {
      console.log("Initializing AuthContext...")
      
      // Extract Telegram data
      const { user: tgUser, chat: chatData, isInTelegram } = extractTelegramData()
      
      setIsTelegram(isInTelegram)
      
      // Set Telegram user and chat data if available
      if (tgUser) {
        setTelegramUser(tgUser)
      }
      
      if (chatData) {
        setTelegramChatData(chatData)
      }
      
      // Expand webapp if we're in Telegram
      if (isInTelegram) {
        try {
          // Try WebApp SDK first
          if (WebApp.initData) {
            WebApp.ready()
            WebApp.expand()
            console.log("Expanded WebApp using SDK")
          } 
          // Fallback to window.Telegram
          else if (window.Telegram?.WebApp) {
            window.Telegram.WebApp.ready()
            window.Telegram.WebApp.expand()
            console.log("Expanded WebApp using window.Telegram")
          }
        } catch (e) {
          console.error("Error expanding WebApp:", e)
        }
        
        // Fix navigation issues in Telegram WebApp
        setupTelegramNavigation()
      }
      
      // Check for existing user session from cookie
      const userCookie = Cookies.get("user")
      
      if (userCookie) {
        console.log("Found existing user cookie")
        // We have a user cookie, use it
        try {
          const userData = JSON.parse(userCookie)
          setUser(userData)
          
          // Still register/login with Telegram if available to ensure user record is up to date
          if (tgUser) {
            console.log("Updating existing user with fresh Telegram data")
            await registerOrLoginUser(tgUser, chatData)
          }
        } catch (e) {
          console.error("Error parsing user cookie:", e)
          // If cookie is invalid, remove it
          Cookies.remove("user")
        }
        
        setIsLoading(false)
        return
      }
      
      // If we have Telegram user but no cookie, register/login immediately
      if (tgUser) {
        console.log("No user cookie found, registering with Telegram data")
        await registerOrLoginUser(tgUser, chatData)
        setIsLoading(false)
        return
      }
      
      // Last resort - try API
      console.log("No Telegram user or cookie, trying API /me endpoint")
      const response = await fetch("/api/auth/me",{
        cache: 'no-store',
        next: { revalidate: 0 }
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          console.log("Successfully retrieved user from API")
          setUser(data.data)
          Cookies.set("user", JSON.stringify(data.data), { expires: 7 })
        } else {
          console.log("API returned success:false")
        }
      } else {
        console.log("API /me request failed with status:", response.status)
      }
    } catch (e) {
      console.error("Error in init function:", e)
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    initApp()
  }, [initApp])

  const refreshUser = async () => {
    try {
      setIsLoading(true);
      const userResponse = await fetch('/api/auth/me', {
        cache: 'no-store',
        next: { revalidate: 0 }
      });
      const userData = await userResponse.json();
      
      if (userData.data) {
        setUser(userData.data);
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        telegramUser,
        telegramChatData,
        isLoading,
        isAuthenticated: !!user,
        isTelegram,
        logout,
        registerOrLoginUser,
        refreshUser
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

const setupTelegramNavigation = () => {
  if (!window.Telegram?.WebApp) return
  
  try {
    // Fix for links in Telegram WebApp
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      const linkElement = target.closest('a')
      
      if (linkElement && 
          linkElement.href && 
          linkElement.href.startsWith(window.location.origin) && 
          !linkElement.getAttribute('data-bypass-telegram')) {
        
        e.preventDefault()
        e.stopPropagation()
        
        const href = linkElement.getAttribute('href')
        if (href) {
          // Manual navigation
          window.history.pushState({}, '', href)
          // Trigger router update
          window.dispatchEvent(new Event('popstate'))
        }
      }
    }, true)
    
    console.log("Telegram navigation handler setup complete")
  } catch (error) {
    console.error("Error setting up Telegram navigation handler:", error)
  }
}

export default AuthContext;