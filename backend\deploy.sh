#!/bin/bash

# Exit on any error
set -e

echo "Deploying Tradematepro..."

# Create logs directory if it doesn't exist
mkdir -p logs

# Install dependencies
echo "Installing dependencies..."
npm install

# Build the project
echo "Building project..."
npm run build

# Start or restart the application using PM2
echo "Starting application with PM2..."
pm2 start ecosystem.config.js

echo "Deployment complete! The bot is now running."
echo "To check status, run: pm2 status"
echo "To view logs, run: pm2 logs discord-gacha-bot" 