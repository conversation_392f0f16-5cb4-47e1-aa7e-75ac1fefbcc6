import React, { useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Zap, ArrowDown } from 'lucide-react';

interface HeroSectionProps {
  scrollY: number;
}

const HeroSection: React.FC<HeroSectionProps> = ({ scrollY }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Matrix-like animation setup
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions
    const updateCanvasSize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    
    // Characters for the matrix effect - mix of binary, Japanese, and crypto symbols
    const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネ₿Ξ⚡🚀💎';
    const columns = Math.floor(canvas.width / 25); // Slightly wider spacing
    const drops: number[] = [];
    
    // Initialize all columns with random starting positions
    for (let i = 0; i < columns; i++) {
      drops[i] = Math.floor(Math.random() * -canvas.height);
    }
    
    // Draw the characters
    const draw = () => {
      // Add semi-transparent black background for trail effect
      ctx.fillStyle = 'rgba(0, 0, 0, 0.03)'; // More subtle trail
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Loop over drops
      for (let i = 0; i < drops.length; i++) {
        // Random character
        const text = chars[Math.floor(Math.random() * chars.length)];
        
        // Varying opacity and color intensity
        const opacity = Math.random() * 0.3 + 0.1; // Lower opacity for subtlety
        const brightness = Math.random() * 100 + 100; // Varying green brightness
        
        ctx.fillStyle = `rgba(74, 222, 128, ${opacity})`;
        ctx.font = '16px monospace';
        ctx.fillText(text, i * 25, drops[i]);
        
        // Add occasional brighter "lead" characters
        if (Math.random() > 0.98) {
          ctx.fillStyle = `rgba(74, 222, 128, 0.8)`;
          ctx.fillText(text, i * 25, drops[i]);
        }
        
        // Send the drop back to the top randomly after it crosses the screen
        if (drops[i] > canvas.height && Math.random() > 0.975) {
          drops[i] = Math.floor(Math.random() * -200); // Vary reset position
        }
        
        // Increment y coordinate with slight randomness
        drops[i] += Math.random() > 0.95 ? 2 : 1;
      }
    };
    
    const interval = setInterval(draw, 50); // Slightly slower for hero section
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('resize', updateCanvasSize);
    };
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center pt-16 ">
      {/* Matrix-style background canvas */}
      <canvas 
        ref={canvasRef} 
        className="absolute inset-0 opacity-15 pointer-events-none"
        style={{ transform: `translateY(${scrollY * 0.1}px)` }}
      />

      {/* Subtle background gradients */}
      <div 
        className="absolute top-20 left-1/4 w-96 h-96 bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-full blur-3xl"
        style={{ transform: `translateY(${scrollY * 0.3}px)` }}
      />
      <div 
        className="absolute bottom-20 right-1/4 w-80 h-80 bg-gradient-to-l from-green-400/3 to-emerald-400/3 rounded-full blur-3xl"
        style={{ transform: `translateY(${scrollY * -0.2}px)` }}
      />

      {/* Subtle scan line effect */}
      <div 
        className="absolute inset-0 bg-gradient-to-b from-transparent via-green-500/2 to-transparent pointer-events-none animate-pulse"
        style={{ 
          height: '200%',
          transform: `translateY(${(scrollY * 0.05) % 100}%)`,
          animationDuration: '8s'
        }}
      />

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <Badge className="mb-6 bg-gray-900/50 backdrop-blur-xl text-green-400 border border-green-500/20 hover:border-green-500/40 transition-all duration-300 font-medium animate-bounce-in">
          🚀 AI-Powered Solana Trading
        </Badge>
        
        <h2 
          className="text-[48px] md:text-[70px] font-bold mb-8 leading-[55px] md:leading-[85px] font-syne animate-slide-down"
          style={{ transform: `translateY(${scrollY * -0.1}px)` }}
        >
          <span className="text-white block mb-0 pb-0 animate-fade-in-left animation-delay-200">
            Your SOL, <br />
            <span className="text-green-400">Zapped by AI</span>
          </span>
        </h2>

        <p 
          className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto font-medium animate-slide-up animation-delay-600"
          style={{ transform: `translateY(${scrollY * -0.05}px)` }}
        >
          Passive Gains. Daily Flow. 
          <span className="block mt-2 text-green-400 font-semibold">
            Tap. Zap. Repeat. ⚡
          </span>
        </p>

        <p 
          className="text-lg text-gray-400 mb-12 max-w-4xl mx-auto animate-scale-in animation-delay-800"
          style={{ transform: `translateY(${scrollY * -0.03}px)` }}
        >
          FlowTrade lets you earn passive income by zapping your SOL into meme coin yield — 
          and rewarding you for inviting others.
        </p>

        <div 
          className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-bounce-in animation-delay-1000"
          style={{ transform: `translateY(${scrollY * -0.02}px)` }}
        >
          <Button 
            onClick={() => window.location.href = 'https://t.me/FlowTrade_bot'} 
            size="lg" 
            className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-400 hover:to-emerald-500 text-black font-semibold border-0 shadow-2xl shadow-green-500/30 hover:shadow-green-500/50 px-8 py-6 text-lg group transition-all duration-300 hover:scale-105 animate-gradient-x"
          >
            <Zap className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform duration-300 animate-rotate-in" />
            Start Zapping Now
          </Button>
          <Button 
            size="lg" 
            variant="outline" 
            className="border-green-500/30 bg-gray-900/30 backdrop-blur-xl text-green-400 hover:bg-green-500/10 hover:border-green-500/50 px-8 py-6 text-lg transition-all duration-300 hover:scale-105 animate-fade-in-right animation-delay-1200 hover:text-white"
          >
            Learn More
          </Button>
        </div>

        {/* Scroll indicator with enhanced glow */}
        <div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce"
          style={{ transform: `translate(-50%, ${scrollY * 0.1}px)` }}
        >
          <ArrowDown className="h-6 w-6 text-green-400 animate-float drop-shadow-[0_0_8px_rgba(74,222,128,0.6)]" />
        </div>
      </div>

      <style>{`
        .animation-delay-200 { animation-delay: 0.2s; }
        .animation-delay-400 { animation-delay: 0.4s; }
        .animation-delay-600 { animation-delay: 0.6s; }
        .animation-delay-800 { animation-delay: 0.8s; }
        .animation-delay-1000 { animation-delay: 1s; }
        .animation-delay-1200 { animation-delay: 1.2s; }
        
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </section>
  );
};

export default HeroSection;