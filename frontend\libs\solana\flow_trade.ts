/**
 * Program IDL in camelCase format in order to be used in JS/TS.
 *
 * Note that this is only a type helper and is not the actual IDL. The original
 * IDL can be found at `target/idl/flow_trade.json`.
 */
export type FlowTrade = {
  "address": "6UX6hhvGZDitWT1Tfr2iUo7BpCFNmcPUccXKgB4cA2J7",
  "metadata": {
    "name": "flowTrade",
    "version": "0.1.0",
    "spec": "0.1.0",
    "description": "Created with Anchor"
  },
  "instructions": [
    {
      "name": "addMoreFunds",
      "discriminator": [
        123,
        121,
        137,
        55,
        94,
        116,
        185,
        48
      ],
      "accounts": [
        {
          "name": "investment",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  105,
                  110,
                  118,
                  101,
                  115,
                  116,
                  109,
                  101,
                  110,
                  116
                ]
              },
              {
                "kind": "account",
                "path": "user"
              }
            ]
          }
        },
        {
          "name": "contractState",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  99,
                  111,
                  110,
                  116,
                  114,
                  97,
                  99,
                  116,
                  95,
                  115,
                  116,
                  97,
                  116,
                  101
                ]
              }
            ]
          }
        },
        {
          "name": "pool",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  112,
                  111,
                  111,
                  108
                ]
              }
            ]
          }
        },
        {
          "name": "user",
          "writable": true,
          "signer": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "amount",
          "type": "u64"
        },
        {
          "name": "referrerKeys",
          "type": {
            "vec": "pubkey"
          }
        },
        {
          "name": "referrerPercentages",
          "type": "bytes"
        }
      ]
    },
    {
      "name": "adminAddFundsToPool",
      "discriminator": [
        37,
        196,
        132,
        215,
        220,
        74,
        62,
        127
      ],
      "accounts": [
        {
          "name": "contractState",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  99,
                  111,
                  110,
                  116,
                  114,
                  97,
                  99,
                  116,
                  95,
                  115,
                  116,
                  97,
                  116,
                  101
                ]
              }
            ]
          }
        },
        {
          "name": "pool",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  112,
                  111,
                  111,
                  108
                ]
              }
            ]
          }
        },
        {
          "name": "admin",
          "writable": true,
          "signer": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "amount",
          "type": "u64"
        }
      ]
    },
    {
      "name": "adminWithdraw",
      "discriminator": [
        160,
        166,
        147,
        222,
        46,
        220,
        75,
        224
      ],
      "accounts": [
        {
          "name": "contractState",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  99,
                  111,
                  110,
                  116,
                  114,
                  97,
                  99,
                  116,
                  95,
                  115,
                  116,
                  97,
                  116,
                  101
                ]
              }
            ]
          }
        },
        {
          "name": "pool",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  112,
                  111,
                  111,
                  108
                ]
              }
            ]
          }
        },
        {
          "name": "admin",
          "writable": true,
          "signer": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "amount",
          "type": "u64"
        }
      ]
    },
    {
      "name": "claimRoi",
      "discriminator": [
        108,
        190,
        179,
        14,
        37,
        105,
        182,
        193
      ],
      "accounts": [
        {
          "name": "investment",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  105,
                  110,
                  118,
                  101,
                  115,
                  116,
                  109,
                  101,
                  110,
                  116
                ]
              },
              {
                "kind": "account",
                "path": "user"
              }
            ]
          }
        },
        {
          "name": "user",
          "writable": true,
          "signer": true
        }
      ],
      "args": []
    },
    {
      "name": "createInvestment",
      "discriminator": [
        54,
        240,
        181,
        138,
        116,
        58,
        134,
        175
      ],
      "accounts": [
        {
          "name": "investment",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  105,
                  110,
                  118,
                  101,
                  115,
                  116,
                  109,
                  101,
                  110,
                  116
                ]
              },
              {
                "kind": "account",
                "path": "user"
              }
            ]
          }
        },
        {
          "name": "contractState",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  99,
                  111,
                  110,
                  116,
                  114,
                  97,
                  99,
                  116,
                  95,
                  115,
                  116,
                  97,
                  116,
                  101
                ]
              }
            ]
          }
        },
        {
          "name": "pool",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  112,
                  111,
                  111,
                  108
                ]
              }
            ]
          }
        },
        {
          "name": "user",
          "writable": true,
          "signer": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "amount",
          "type": "u64"
        },
        {
          "name": "referrerKeys",
          "type": {
            "vec": "pubkey"
          }
        },
        {
          "name": "referrerPercentages",
          "type": "bytes"
        }
      ],
      "returns": "string"
    },
    {
      "name": "getInvestmentDetails",
      "discriminator": [
        8,
        91,
        141,
        35,
        186,
        98,
        116,
        38
      ],
      "accounts": [
        {
          "name": "investment",
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  105,
                  110,
                  118,
                  101,
                  115,
                  116,
                  109,
                  101,
                  110,
                  116
                ]
              },
              {
                "kind": "account",
                "path": "owner"
              }
            ]
          }
        },
        {
          "name": "owner",
          "signer": true
        }
      ],
      "args": [],
      "returns": {
        "defined": {
          "name": "investmentDetails"
        }
      }
    },
    {
      "name": "initialize",
      "discriminator": [
        175,
        175,
        109,
        31,
        13,
        152,
        155,
        237
      ],
      "accounts": [
        {
          "name": "contractState",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  99,
                  111,
                  110,
                  116,
                  114,
                  97,
                  99,
                  116,
                  95,
                  115,
                  116,
                  97,
                  116,
                  101
                ]
              }
            ]
          }
        },
        {
          "name": "pool",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  112,
                  111,
                  111,
                  108
                ]
              }
            ]
          }
        },
        {
          "name": "signer",
          "writable": true,
          "signer": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "admin",
          "type": "pubkey"
        }
      ]
    },
    {
      "name": "withdraw",
      "discriminator": [
        183,
        18,
        70,
        156,
        148,
        109,
        161,
        34
      ],
      "accounts": [
        {
          "name": "investment",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  105,
                  110,
                  118,
                  101,
                  115,
                  116,
                  109,
                  101,
                  110,
                  116
                ]
              },
              {
                "kind": "account",
                "path": "user"
              }
            ]
          }
        },
        {
          "name": "contractState",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  99,
                  111,
                  110,
                  116,
                  114,
                  97,
                  99,
                  116,
                  95,
                  115,
                  116,
                  97,
                  116,
                  101
                ]
              }
            ]
          }
        },
        {
          "name": "pool",
          "writable": true,
          "pda": {
            "seeds": [
              {
                "kind": "const",
                "value": [
                  112,
                  111,
                  111,
                  108
                ]
              }
            ]
          }
        },
        {
          "name": "user",
          "writable": true,
          "signer": true
        },
        {
          "name": "systemProgram",
          "address": "11111111111111111111111111111111"
        }
      ],
      "args": [
        {
          "name": "amount",
          "type": "u64"
        },
        {
          "name": "source",
          "type": {
            "defined": {
              "name": "withdrawalSource"
            }
          }
        }
      ]
    }
  ],
  "accounts": [
    {
      "name": "contractState",
      "discriminator": [
        190,
        138,
        10,
        223,
        189,
        116,
        222,
        115
      ]
    },
    {
      "name": "investment",
      "discriminator": [
        175,
        134,
        9,
        175,
        115,
        153,
        39,
        28
      ]
    },
    {
      "name": "pool",
      "discriminator": [
        241,
        154,
        109,
        4,
        17,
        177,
        109,
        188
      ]
    }
  ],
  "events": [
    {
      "name": "adminDepositEvent",
      "discriminator": [
        165,
        68,
        63,
        133,
        0,
        28,
        136,
        206
      ]
    },
    {
      "name": "adminWithdrawalEvent",
      "discriminator": [
        121,
        98,
        210,
        245,
        8,
        43,
        45,
        109
      ]
    },
    {
      "name": "fundsAddedEvent",
      "discriminator": [
        127,
        31,
        108,
        255,
        187,
        19,
        70,
        68
      ]
    },
    {
      "name": "fundsWithdrawnEvent",
      "discriminator": [
        86,
        232,
        194,
        4,
        211,
        69,
        172,
        202
      ]
    },
    {
      "name": "investmentCompletedEvent",
      "discriminator": [
        174,
        175,
        246,
        153,
        248,
        75,
        12,
        153
      ]
    },
    {
      "name": "investmentCreatedEvent",
      "discriminator": [
        205,
        116,
        89,
        98,
        240,
        106,
        132,
        38
      ]
    },
    {
      "name": "roiClaimedEvent",
      "discriminator": [
        56,
        204,
        107,
        40,
        16,
        84,
        124,
        50
      ]
    }
  ],
  "errors": [
    {
      "code": 6000,
      "name": "unauthorized",
      "msg": "Unauthorized: Only admin can perform this action"
    },
    {
      "code": 6001,
      "name": "minInvestment",
      "msg": "Investment amount below minimum (0.05 SOL)"
    },
    {
      "code": 6002,
      "name": "existingInvestment",
      "msg": "User already has an active investment"
    },
    {
      "code": 6003,
      "name": "arithmeticOverflow",
      "msg": "Arithmetic overflow occurred"
    },
    {
      "code": 6004,
      "name": "minWithdrawal",
      "msg": "Withdrawal amount below minimum (0.05 SOL)"
    },
    {
      "code": 6005,
      "name": "withdrawalCooldown",
      "msg": "24-hour withdrawal cooldown not met"
    },
    {
      "code": 6006,
      "name": "insufficientClaims",
      "msg": "Insufficient claims for withdrawal (need 5)"
    },
    {
      "code": 6007,
      "name": "insufficientBalance",
      "msg": "Insufficient balance for withdrawal"
    },
    {
      "code": 6008,
      "name": "insufficientPoolBalance",
      "msg": "Insufficient pool balance"
    },
    {
      "code": 6009,
      "name": "invalidReferralPercentage",
      "msg": "Invalid referral percentage"
    },
    {
      "code": 6010,
      "name": "referrerInactive",
      "msg": "Referrer does not have an active investment"
    },
    {
      "code": 6011,
      "name": "claimCooldown",
      "msg": "36-hour claim cooldown not met"
    },
    {
      "code": 6012,
      "name": "roiCapReached",
      "msg": "ROI cap of 250% reached"
    },
    {
      "code": 6013,
      "name": "invalidInvestmentStatus",
      "msg": "Investment is not active"
    },
    {
      "code": 6014,
      "name": "minDeposit",
      "msg": "Deposit amount below minimum (0.05 SOL)"
    },
    {
      "code": 6015,
      "name": "tooManyReferrals",
      "msg": "Too many referrals provided"
    },
    {
      "code": 6016,
      "name": "invalidReferrer",
      "msg": "Invalid referrer account"
    },
    {
      "code": 6017,
      "name": "invalidReferralData",
      "msg": "Referrer keys and percentages must have the same length"
    },
    {
      "code": 6018,
      "name": "insufficientReferrerAccounts",
      "msg": "Insufficient referrer accounts provided"
    }
  ],
  "types": [
    {
      "name": "adminDepositEvent",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "admin",
            "type": "pubkey"
          },
          {
            "name": "amount",
            "type": "u64"
          },
          {
            "name": "timestamp",
            "type": "i64"
          }
        ]
      }
    },
    {
      "name": "adminWithdrawalEvent",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "admin",
            "type": "pubkey"
          },
          {
            "name": "amount",
            "type": "u64"
          },
          {
            "name": "timestamp",
            "type": "i64"
          }
        ]
      }
    },
    {
      "name": "contractState",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "admin",
            "type": "pubkey"
          },
          {
            "name": "poolBalance",
            "type": "u64"
          }
        ]
      }
    },
    {
      "name": "fundsAddedEvent",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "investmentPda",
            "type": "pubkey"
          },
          {
            "name": "investmentId",
            "type": "string"
          },
          {
            "name": "owner",
            "type": "pubkey"
          },
          {
            "name": "amount",
            "type": "u64"
          },
          {
            "name": "newTotal",
            "type": "u64"
          },
          {
            "name": "timestamp",
            "type": "i64"
          }
        ]
      }
    },
    {
      "name": "fundsWithdrawnEvent",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "investmentPda",
            "type": "pubkey"
          },
          {
            "name": "investmentId",
            "type": "string"
          },
          {
            "name": "owner",
            "type": "pubkey"
          },
          {
            "name": "amount",
            "type": "u64"
          },
          {
            "name": "source",
            "type": "string"
          },
          {
            "name": "remainingBalance",
            "type": "u64"
          },
          {
            "name": "timestamp",
            "type": "i64"
          }
        ]
      }
    },
    {
      "name": "investment",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "owner",
            "type": "pubkey"
          },
          {
            "name": "initialInvestment",
            "type": "u64"
          },
          {
            "name": "earnedAmount",
            "type": "u64"
          },
          {
            "name": "referEarnedAmount",
            "type": "u64"
          },
          {
            "name": "percentageOfRoi",
            "type": "u64"
          },
          {
            "name": "claimsSinceWithdrawal",
            "type": "u64"
          },
          {
            "name": "lastClaimTime",
            "type": "i64"
          },
          {
            "name": "lastWithdrawalTime",
            "type": "i64"
          },
          {
            "name": "withdrawalAmount",
            "type": "u64"
          },
          {
            "name": "withdrawnReferAmount",
            "type": "u64"
          },
          {
            "name": "status",
            "type": {
              "defined": {
                "name": "investmentStatus"
              }
            }
          },
          {
            "name": "creationTime",
            "type": "i64"
          },
          {
            "name": "investmentId",
            "type": "string"
          },
          {
            "name": "totalClaimedAmount",
            "type": "u64"
          },
          {
            "name": "referralCount",
            "type": "u8"
          },
          {
            "name": "lastAction",
            "type": "string"
          },
          {
            "name": "totalLifetimeClaims",
            "type": "u64"
          }
        ]
      }
    },
    {
      "name": "investmentCompletedEvent",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "investmentPda",
            "type": "pubkey"
          },
          {
            "name": "investmentId",
            "type": "string"
          },
          {
            "name": "owner",
            "type": "pubkey"
          },
          {
            "name": "initialInvestment",
            "type": "u64"
          },
          {
            "name": "totalEarned",
            "type": "u64"
          },
          {
            "name": "totalWithdrawn",
            "type": "u64"
          },
          {
            "name": "lifetimeDays",
            "type": "u64"
          },
          {
            "name": "timestamp",
            "type": "i64"
          }
        ]
      }
    },
    {
      "name": "investmentCreatedEvent",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "investmentPda",
            "type": "pubkey"
          },
          {
            "name": "investmentId",
            "type": "string"
          },
          {
            "name": "owner",
            "type": "pubkey"
          },
          {
            "name": "amount",
            "type": "u64"
          },
          {
            "name": "timestamp",
            "type": "i64"
          },
          {
            "name": "referralCount",
            "type": "u8"
          }
        ]
      }
    },
    {
      "name": "investmentDetails",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "investmentId",
            "type": "string"
          },
          {
            "name": "owner",
            "type": "pubkey"
          },
          {
            "name": "initialInvestment",
            "type": "u64"
          },
          {
            "name": "earnedAmount",
            "type": "u64"
          },
          {
            "name": "referEarnedAmount",
            "type": "u64"
          },
          {
            "name": "percentageOfRoi",
            "type": "u64"
          },
          {
            "name": "claimsSinceWithdrawal",
            "type": "u64"
          },
          {
            "name": "lastClaimTime",
            "type": "i64"
          },
          {
            "name": "lastWithdrawalTime",
            "type": "i64"
          },
          {
            "name": "withdrawalAmount",
            "type": "u64"
          },
          {
            "name": "withdrawnReferAmount",
            "type": "u64"
          },
          {
            "name": "status",
            "type": {
              "defined": {
                "name": "investmentStatus"
              }
            }
          }
        ]
      }
    },
    {
      "name": "investmentStatus",
      "type": {
        "kind": "enum",
        "variants": [
          {
            "name": "active"
          },
          {
            "name": "completed"
          }
        ]
      }
    },
    {
      "name": "pool",
      "type": {
        "kind": "struct",
        "fields": []
      }
    },
    {
      "name": "roiClaimedEvent",
      "type": {
        "kind": "struct",
        "fields": [
          {
            "name": "investmentPda",
            "type": "pubkey"
          },
          {
            "name": "investmentId",
            "type": "string"
          },
          {
            "name": "owner",
            "type": "pubkey"
          },
          {
            "name": "amount",
            "type": "u64"
          },
          {
            "name": "timestamp",
            "type": "i64"
          },
          {
            "name": "totalClaimed",
            "type": "u64"
          },
          {
            "name": "claimsCount",
            "type": "u64"
          }
        ]
      }
    },
    {
      "name": "withdrawalSource",
      "type": {
        "kind": "enum",
        "variants": [
          {
            "name": "zap"
          },
          {
            "name": "referral"
          },
          {
            "name": "both"
          }
        ]
      }
    }
  ]
};
