// Claim the zapped rewards on particular investment by a user (1.5%) Zap claim feature

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment } from '@/libs/model/investment.schema';
import { cookies } from 'next/headers';
import { Types } from 'mongoose';
import { TIME_CONFIG } from '@/libs/config';
import { User } from '@/libs/model/user.schema';

// POST handler to claim Zapped rewards from an investment
export async function POST(req: NextRequest) {
  try {
    // Get the user ID from cookies
    const userId = (await cookies()).get('userId')?.value;
    // const userId = "68380e3e3b44aa85b374c1f0";
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Get request body
    const body = await req.json();
    const { investmentId } = body;
    
    if (!investmentId) {
      return NextResponse.json(
        { success: false, error: 'Investment ID is required' },
        { status: 400 }
      );
    }

    const user = await User.findOne({
      _id: new Types.ObjectId(userId),
    });

    if (!user) {
      return NextResponse.json({ success: false, error: 'User not found' }, { status: 404 });
    }
    
    // Find the investment
    let investment;
    try {
      investment = await Investment.findOne({
        _id: new Types.ObjectId(investmentId),
        user: new Types.ObjectId(userId),
        status: 'active'
      });
    } catch (e) {
      return NextResponse.json(
        { success: false, error: 'Invalid investment ID' },
        { status: 400 }
      );
    }
    
    if (!investment) {
      return NextResponse.json(
        { success: false, error: 'Investment not found or not active' },
        { status: 404 }
      );
    }

    // Calculate time elapsed since last claim or investment creation
    const now = new Date();
    const lastTime = investment.lastClaimTime || investment.createdAt;
    const elapsedSeconds = (now.getTime() - lastTime.getTime()) / 1000;

    // Check if enough time has passed (30 seconds for testing)
    if (elapsedSeconds < TIME_CONFIG.ROI_ACCUMULATION_SECONDS) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Not enough time has passed since last claim',
          timeElapsed: elapsedSeconds,
          requiredTime: TIME_CONFIG.ROI_ACCUMULATION_SECONDS,
          nextClaimAt: new Date(lastTime.getTime() + (TIME_CONFIG.ROI_ACCUMULATION_SECONDS * 1000))
        },
        { status: 400 }
      );
    }
    
    // Check if investment is valid for claiming
    const currentValue = investment.initialAmount + investment.earnedAmount - investment.withdrawnAmount;
    if (currentValue <= 0) {
      return NextResponse.json(
        { success: false, error: 'No active investment value available to claim Zapped rewards from' },
        { status: 400 }
      );
    }
    
    // Calculate current ROI percentage (earned amount as percentage of initial)
    // This is the percentage of the initial amount that has been earned
    investment.percentageOfROI = (investment.earnedAmount / investment.initialAmount) * 100;

    // Calculate maximum allowed claim based on ROI cap
    // Max possible earned amount is 250% of initial investment
    let claimAmount;
    const maxPossibleROI = TIME_CONFIG.TOTAL_ROI_CAP * 100; // 250%
    const remainingROIPercentage = maxPossibleROI - investment.percentageOfROI;
    
    console.log("investment.percentageOfROI", investment.percentageOfROI);
    console.log("remainingROIPercentage", remainingROIPercentage);
    
    if (remainingROIPercentage <= 0) {
      // ROI cap already reached, no more claims allowed
      return NextResponse.json(
        { success: false, error: 'Maximum zap rewards of 250% has been reached' },
        { status: 400 }
      );
    } else if (remainingROIPercentage < TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE * 100) {
      // Only claim what's left to reach 250% ROI
      claimAmount = (remainingROIPercentage / 100) * investment.initialAmount;
    } else {
      // Normal claim of 1.5%
      claimAmount = investment.initialAmount * TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE;
    }



    // Update investment
    investment.earnedAmount += claimAmount;
    investment.lastClaimTime = now;
    
    // Recalculate percentage of ROI after adding new claim
    investment.percentageOfROI = (investment.earnedAmount / investment.initialAmount) * 100;

    // Add to claims history
    investment.claims.push({
      timestamp: now,
      amount: claimAmount,
      type: 'zap'
    });
    
    // Check if total earned has reached the cap (250% of initial)
    const maxEarnings = investment.initialAmount * TIME_CONFIG.TOTAL_ROI_CAP;
    if (investment.earnedAmount >= maxEarnings) {
      investment.earnedAmount = maxEarnings;
      // Mark investment as complete if all funds withdrawn
      if (investment.withdrawnAmount >= investment.earnedAmount) {
        investment.status = 'completed';
      }
    }
    
    // Save changes
    await investment.save();    

    // Add claimed amount to user wallet and update user's stats
    user.wallet.balance.sol += claimAmount;
    user.wallet.lastUpdated = now;  
    user.claimsSinceWithdrawal += 1;
    await user.save();
    
    return NextResponse.json({
      success: true,
      data: {
        amountClaimed: claimAmount,
        newTotalClaimed: investment.earnedAmount,
        currentValue: investment.initialAmount + investment.earnedAmount - investment.withdrawnAmount,
        nextClaimAt: new Date(now.getTime() + (TIME_CONFIG.ROI_ACCUMULATION_SECONDS * 1000)),
        claimsSinceWithdrawal: investment.claimsSinceWithdrawal,
        percentageOfROI: investment.percentageOfROI // Send updated ROI percentage
      },
      message: 'Zapped rewards claimed successfully'
    });
    
  } catch (error: any) {
    console.error('Error claiming Zapped rewards:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to claim Zapped rewards' },
      { status: 500 }
    );
  }
}