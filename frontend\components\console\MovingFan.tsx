import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TIME_CONFIG } from '@/libs/config';

interface MovingFanProps {
  isOn?: boolean;
  speed?: number;
  currentZapProgress: number;
  maxZapProgress?: number;
  investmentAmount: number;
  totalEarned: number;
  isInvested?: boolean;
}

export default function MovingFan({ 
  isOn = true, 
  speed = 1, 
  currentZapProgress = 0,
  maxZapProgress = TIME_CONFIG.TOTAL_ROI_CAP * 100, // Full 250% as per config
  investmentAmount = 0,
  totalEarned = 0,
  isInvested = true
}: MovingFanProps) {
  const meterRef = useRef<SVGPathElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const [showMilestoneEffect, setShowMilestoneEffect] = useState(false);
  const [reachedMilestone, setReachedMilestone] = useState<number | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  
  // Mouse position handler for hover tooltip
  const handleMouseMove = (e: React.MouseEvent<SVGSVGElement>) => {
    if (svgRef.current) {
      const rect = svgRef.current.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };
  
  // Fan animation effect
  useEffect(() => {
    const fan = document.getElementById('fan-blades');
    if (fan) {
      if (isOn && isInvested) {
        fan.style.animationPlayState = 'running';
        fan.style.animationDuration = `${5 / speed}s`;
        
        // Apply CSS transform for smoother animation
        fan.style.willChange = 'transform';
        fan.style.backfaceVisibility = 'hidden';
        fan.style.transformStyle = 'preserve-3d';
      } else {
        fan.style.animationPlayState = 'paused';
      }
    }
    
    // Glow pulse effect when on
    const glowRings = document.querySelectorAll('.glow-ring');
    glowRings.forEach(ring => {
      if (isOn && isInvested) {
        ring.classList.add('animate-pulse');
      } else {
        ring.classList.remove('animate-pulse');
      }
    });
    
    // Update meter circle with current progress
    if (meterRef.current) {
      const newOffset = calculateStrokeDashOffset(currentZapProgress);
      meterRef.current.style.strokeDashoffset = `${newOffset}`;
      meterRef.current.style.stroke = getZapColor(currentZapProgress);
    }
  }, [isOn, speed, isInvested, currentZapProgress]);
  
  // Set up the SVG elements for better performance
  useEffect(() => {
    if (svgRef.current) {
      // Add hardware acceleration hints
      svgRef.current.style.transform = 'translateZ(0)';
      svgRef.current.style.willChange = 'transform';
    }
    
    // Set initial meter value
    if (meterRef.current) {
      const newOffset = calculateStrokeDashOffset(currentZapProgress);
      meterRef.current.style.strokeDashoffset = `${newOffset}`;
      meterRef.current.style.stroke = getZapColor(currentZapProgress);
    }
  }, []);
  
  // Milestone effects
  useEffect(() => {
    // Calculate milestone values based on config
    const totalRoiPercent = TIME_CONFIG.TOTAL_ROI_CAP * 100; // 250%
    const halfMilestone = totalRoiPercent / 2; // 125% for default 250% total
    
    // Define milestones at 100%, half of max, and max (e.g. 100%, 125%, 250%)
    const milestones = [100, halfMilestone, totalRoiPercent];
    
    // Find the highest milestone reached
    const highestMilestoneReached = milestones.filter(m => currentZapProgress >= m).pop();
    
    // If we've reached a new milestone and we weren't showing the effect already
    if (highestMilestoneReached && highestMilestoneReached !== reachedMilestone) {
      setReachedMilestone(highestMilestoneReached);
      setShowMilestoneEffect(true);
      
      // Hide the effect after 3 seconds
      const timer = setTimeout(() => {
        setShowMilestoneEffect(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [currentZapProgress, reachedMilestone]);
  
  // Calculate the meter's color based on zap percentage
  const getZapColor = (value: number) => {
    const percentage = value / maxZapProgress;
    
    if (percentage <= 0.25) return '#22c55e'; // Green for 0-25%
    if (percentage <= 0.5) return '#eab308'; // Yellow for 25-50%
    if (percentage <= 0.75) return '#f97316'; // Orange for 50-75%
    return '#ef4444'; // Red for 75-100%
  };
  
  // Calculate the stroke dash offset for the circular progress
  const calculateStrokeDashOffset = (value: number) => {
    const circumference = 2 * Math.PI * 114; // Radius 114 of the circle path
    // When no zap progress, return full circumference (completely unfilled)
    if (value <= 0) return circumference;
    const percentage = Math.min(value, maxZapProgress) / maxZapProgress;
    // Calculate offset for full circle
    const offset = circumference - (percentage * circumference); 
    return offset;
  };

  // Calculate current investment value with earned zaps
  const currentInvestmentValue = investmentAmount + totalEarned;
  const valueShow =  totalEarned;
  
  // Format currency display
  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(2)}M`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(2)}K`;
    } else {
      return amount.toLocaleString();
    }
  };
  
  // Get appropriate milestone message and color based on config
  const getMilestoneInfo = () => {
    if (!reachedMilestone) return { message: '', color: '#22c55e' };
    
    const totalRoiPercent = TIME_CONFIG.TOTAL_ROI_CAP * 100; // 250%
    const halfMilestone = totalRoiPercent / 2; // 125% for default 250% total
    
    switch(reachedMilestone) {
      case 100: 
        return { 
          message: 'Zap Level Doubled!', 
          color: '#eab308' 
        };
      case halfMilestone: 
        return { 
          message: 'Halfway to Max Zap!', 
          color: '#f97316' 
        };
      case totalRoiPercent: 
        return { 
          message: 'Maximum Zap Reached!', 
          color: '#ef4444' 
        };
      default: 
        return { 
          message: 'Zap Milestone Reached!', 
          color: '#22c55e' 
        };
    }
  };
  
  const milestoneInfo = getMilestoneInfo();
  
  return (
    <motion.div 
      className="flex flex-col items-center justify-center relative"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      
      {/* 3D perspective container */}
      <div className="relative w-64 h-64 sm:w-72 sm:h-72 md:w-80 md:h-80" style={{ perspective: '1000px' }}>
        {/* Milestone celebration effect overlay */}
        <AnimatedMilestoneEffect 
          show={showMilestoneEffect} 
          milestone={reachedMilestone} 
          message={milestoneInfo.message}
          color={milestoneInfo.color}
        />
        
        {/* Zap Meter SVG overlay - positioned above the fan */}
        <svg 
          className="absolute inset-0 z-50 w-full h-full group" 
          viewBox="0 0 300 300"
          style={{ 
            transform: 'rotateX(15deg)', 
            transformStyle: 'preserve-3d',
          }}
          ref={svgRef}
          onMouseMove={handleMouseMove}
          onMouseEnter={() => setIsHovering(true)}
          onMouseLeave={() => setIsHovering(false)}
        >
          {/* Background Track - Full circle */}
          <circle
            cx="150"
            cy="150"
            r="114"
            fill="none"
            stroke="rgba(0,0,0,0.7)"
            strokeWidth="20"
            strokeLinecap="round"
            style={{ filter: 'drop-shadow(0px 0px 1px rgba(0, 0, 0, 0.7))' }}
          />
          
          {/* Full circle path markers for better visibility */}
          {[...Array(4)].map((_, i) => {
            const angle = i * 90; // 0°, 90°, 180°, 270°
            const radians = angle * Math.PI / 180;
            const x = 150 + 114 * Math.cos(radians);
            const y = 150 + 114 * Math.sin(radians);
            return (
              <circle 
                key={`marker-${i}`} 
                cx={x} 
                cy={y} 
                r="2" 
                fill={i === 0 ? "rgba(34, 197, 94, 0.8)" : "rgba(255,255,255,0.5)"}
              />
            );
          })}
          
          {/* Zap Percentage Display on Hover */}
          {isInvested && isHovering && (
            <g className="tooltip" style={{ pointerEvents: 'none' }}>
              <rect
                x={mousePosition.x - 20}
                y={mousePosition.y - 25}
                width="40"
                height="20"
                rx="5"
                fill="rgba(0,0,0,0.9)"
                stroke={getZapColor(currentZapProgress)}
                strokeWidth="1"
              />
              <text
                x={mousePosition.x}
                y={mousePosition.y - 15}
                fontSize="10"
                fill="#ffffff"
                textAnchor="middle"
                dominantBaseline="middle"
              >
                {currentZapProgress.toFixed(1)}%
              </text>
            </g>
          )}
          
          {/* Tick marks for gauge - full 360° circle with 250% max */}
          {[...Array(6)].map((_, i) => {
            // Calculate tick marks based on TIME_CONFIG.TOTAL_ROI_CAP
            // For standard 250% ROI, these would be 0%, 50%, 100%, 150%, 200%, 250%
            const totalMarks = 6;
            const maxPercentage = TIME_CONFIG.TOTAL_ROI_CAP * 100; // Full 250% total
            const markIncrement = maxPercentage / (totalMarks - 1);
            const percentage = i * markIncrement; 
            
            // Map percentage to angle on full circle (0% at top, going clockwise)
            // 0% -> -90° (top), 250% -> 270° (full circle back to top)
            const angle = -90 + (percentage / maxPercentage) * 360;
            
            const tickLength = i % 2 === 0 ? 15 : 8; // Longer ticks at major milestones
            const labelRadius = 114 + 16; // Position for percentage labels
            const tickStartRadius = 114;
            const tickEndRadius = 114 - tickLength;
            
            // Calculate positions using trigonometry
            const radians = angle * Math.PI / 180;
            const x1 = 150 + tickStartRadius * Math.cos(radians);
            const y1 = 150 + tickStartRadius * Math.sin(radians);
            const x2 = 150 + tickEndRadius * Math.cos(radians);
            const y2 = 150 + tickEndRadius * Math.sin(radians);
            
            // Label position
            const labelX = 150 + labelRadius * Math.cos(radians);
            const labelY = 150 + labelRadius * Math.sin(radians);
            
            return (
              <g key={i}>
                <line
                  x1={x1}
                  y1={y1}
                  x2={x2}
                  y2={y2}
                  stroke="rgba(255,255,255,0.6)"
                  strokeWidth={i % 2 === 0 ? 2 : 1}
                />
                
                {/* Percentage labels */}
                <text
                  x={labelX}
                  y={labelY}
                  fontSize="8"
                  fill="rgba(255,255,255,0.8)"
                  textAnchor="middle"
                  dominantBaseline="middle"
                  style={{ 
                    filter: 'drop-shadow(0px 0px 1px rgba(0, 0, 0, 1))',
                    fontWeight: i % 2 === 0 ? 'bold' : 'normal'
                  }}
                >
                  {percentage.toFixed(0)}%
                </text>
              </g>
            );
          })}
          
          {/* Additional minor tick marks around full circle */}
          {[...Array(24)].map((_, i) => {
            // Skip positions where we already have major ticks
            if (i % 4 === 0) return null;
            
            // Distribute evenly around full 360° circle
            const angle = (-90 + (i * 360 / 24));
            const tickLength = 5; // Short ticks
            const tickStartRadius = 114;
            const tickEndRadius = 114 - tickLength;
            
            // Calculate positions
            const radians = angle * Math.PI / 180;
            const x1 = 150 + tickStartRadius * Math.cos(radians);
            const y1 = 150 + tickStartRadius * Math.sin(radians);
            const x2 = 150 + tickEndRadius * Math.cos(radians);
            const y2 = 150 + tickEndRadius * Math.sin(radians);
            
            return (
              <g key={`minor-${i}`}>
                <line
                  x1={x1}
                  y1={y1}
                  x2={x2}
                  y2={y2}
                  stroke="rgba(255,255,255,0.3)"
                  strokeWidth={0.5}
                />
              </g>
            );
          })}
          
          {/* Progress indicator - filled portion */}
          {isInvested && (
            <circle
            // @ts-ignore
              ref={meterRef}
              cx="150"
              cy="150"
              r="114"
              fill="none"
              stroke={getZapColor(currentZapProgress)}
              strokeWidth="20"
              strokeDasharray={2 * Math.PI * 114}
              strokeDashoffset={calculateStrokeDashOffset(currentZapProgress)}
              strokeLinecap="round"
              style={{ 
                filter: `drop-shadow(0px 0px 10px ${getZapColor(currentZapProgress)})`,
                transition: 'stroke 0.5s ease, stroke-dashoffset 0.5s ease',
                transformOrigin: 'center',
                transform: 'rotate(-90deg)' // Start from the top instead of right
              }}
            />
          )}
          
          {/* Gauge inner center display */}
          <circle cx="150" cy="150" r="62" fill="rgba(0,0,0,0.7)" />
          <circle 
            cx="150" 
            cy="150" 
            r="60" 
            fill="black" 
            stroke="rgba(34, 197, 94, 0.3)" 
            strokeWidth="1"
          />
          
          {/* Central display content based on investment status */}
          {isInvested ? (
            <>
              {/* Investment value display */}
              <text
                x="150"
                y="130"
                fontSize="24"
                fontWeight="bold"
                fill="#ffffff"
                textAnchor="middle"
                dominantBaseline="middle"
              >
                {formatCurrency(valueShow)} Sol
              </text>
              <text
                x="150"
                y="155"
                fontSize="12"
                fill="#22c55e"
                textAnchor="middle"
                dominantBaseline="middle"
              >
                Total Value
              </text>
              
              {/* Completion percentage display */}
              <text
                x="150"
                y="175"
                fontSize="14"
                fill={getZapColor(currentZapProgress)}
                textAnchor="middle"
                dominantBaseline="middle"
                fontWeight="medium"
              >
                {Math.min(currentZapProgress, maxZapProgress).toFixed(1)}% / 250%
              </text>

            </>
          ) : (
            /* No investment state */
            <>
              <text
                x="150"
                y="140"
                fontSize="20"
                fontWeight="bold"
                fill="#ffffff"
                textAnchor="middle"
                dominantBaseline="middle"
              >
                No Investment
              </text>
              <text
                x="150"
                y="165"
                fontSize="12"
                fill="#9ca3af"
                textAnchor="middle"
                dominantBaseline="middle"
              >
                Start Flow Engine to begin Zapping
              </text>
            </>
          )}
          
      
        </svg>
        
        {/* Fan Frame with 3D rotation */}
        <motion.div 
          className="absolute w-full h-full rounded-full bg-black shadow-2xl flex items-center justify-center overflow-hidden backdrop-blur-md"
          style={{ 
            transform: 'rotateX(15deg)', 
            transformStyle: 'preserve-3d',
            boxShadow: isOn && isInvested ? '0 0 40px rgba(34, 197, 94, 0.15)' : '0 10px 30px rgba(0,0,0,0.8)'
          }}
          animate={{
            boxShadow: isOn && isInvested
              ? ['0 0 40px rgba(34, 197, 94, 0.1)', '0 0 40px rgba(34, 197, 94, 0.2)', '0 0 40px rgba(34, 197, 94, 0.1)']
              : '0 10px 30px rgba(0,0,0,0.8)'
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          {/* Milestone indicator light */}
          {currentZapProgress >= 100 && (
            <motion.div 
              className={`absolute top-2 right-2 w-3 h-3 rounded-full`}
              style={{ backgroundColor: getZapColor(currentZapProgress) }}
              animate={{ 
                scale: [1, 1.2, 1],
                opacity: [0.7, 1, 0.7]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'easeInOut'
              }}
            />
          )}
          
          {/* Outer Frame with enhanced cyber glow */}
          <div className="absolute w-full h-full rounded-full border border-black z-10">
            {/* Cyber neon ring glow effect - bright green glow */}
            <div className={`absolute w-full h-full rounded-full glow-ring ${isOn && isInvested ? 'opacity-90' : 'opacity-30'} transition-opacity duration-500`}>
              <div className="w-full h-full rounded-full border-2 border-green-500 filter blur-md"></div>
            </div>
            
            {/* Secondary pulsing ring - cyberpunk style */}
            {isOn && isInvested && (
              <motion.div 
                className="absolute -inset-1 rounded-full opacity-30"
                animate={{
                  boxShadow: [
                    '0 0 5px 2px rgba(34, 197, 94, 0.3) inset', 
                    '0 0 10px 4px rgba(34, 197, 94, 0.5) inset', 
                    '0 0 5px 2px rgba(34, 197, 94, 0.3) inset'
                  ]
                }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
              />
            )}
          </div>
          
          {/* Background Base - pure black for cyberpunk contrast */}
          <div className="absolute w-full h-full bg-black rounded-full z-5">
 
          </div>
          
          {/* Cyberpunk circuit patterns - anime tech style */}
          <div className="absolute w-4/5 h-4/5 rounded-full z-8">
            <div className="absolute w-full h-full rounded-full">
              {/* Circuit lines */}
              {[...Array(5)].map((_, i) => (
                <div 
                  key={i} 
                  className="absolute bg-green-500/30"
                  style={{
                    height: '1px',
                    width: `${70 - i * 10}%`,
                    left: `${15 + i * 5}%`,
                    top: `${20 + i * 15}%`,
                    boxShadow: '0 0 5px rgba(34, 197, 94, 0.5)'
                  }}
                />
              ))}
              
              {/* Circuit nodes */}
              {[...Array(4)].map((_, i) => (
                <div 
                  key={i}
                  className="absolute w-1.5 h-1.5 rounded-full bg-green-500/60"
                  style={{
                    left: `${25 + i * 20}%`,
                    top: `${25 + (i % 2) * 40}%`,
                    boxShadow: '0 0 5px rgba(34, 197, 94, 0.7)'
                  }}
                />
              ))}
            </div>
          </div>
          
          {/* Inner Circle/Deep Base - pure black for contrast */}
          <div className="absolute w-3/4 h-3/4 rounded-full bg-black border border-black z-10" 
            style={{ boxShadow: 'inset 0 0 20px rgba(0,0,0,1), 0 0 5px rgba(34, 197, 94, 0.3)' }}>
            
            {/* Anime style geometric pattern overlays */}
            {[...Array(3)].map((_, i) => (
              <div 
                key={i}
                className="absolute inset-0 rounded-full border border-green-900/40"
                style={{ 
                  transform: `scale(${0.9 - i * 0.1})`,
                  boxShadow: isOn && isInvested ? 'inset 0 0 10px rgba(34, 197, 94, 0.05)' : 'none'
                }}
              />
            ))}
          </div>
          
          {/* Inner Glow Rings - cyberpunk neon aesthetics */}
          <div className="absolute w-2/3 h-2/3 rounded-full z-10">
            <div className={`absolute w-full h-full rounded-full glow-ring transition-opacity duration-500 ${isOn && isInvested ? 'opacity-80' : 'opacity-20'}`}>
              <div className="w-full h-full rounded-full border-2 border-green-500 filter blur-md"></div>
              
              {/* Animated inner ring - anime tech glow */}
              {isOn && isInvested && (
                <motion.div 
                  className="absolute inset-0 rounded-full opacity-40"
                  animate={{
                    boxShadow: [
                      'inset 0 0 15px 5px rgba(34, 197, 94, 0.3)', 
                      'inset 0 0 20px 10px rgba(34, 197, 94, 0.5)', 
                      'inset 0 0 15px 5px rgba(34, 197, 94, 0.3)'
                    ]
                  }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                />
              )}
            </div>
          </div>
          
          {/* Hub - cyberpunk core */}
          <div className="w-20 h-20 rounded-full bg-black flex items-center justify-center z-30 shadow-lg" 
            style={{ boxShadow: '0 0 15px rgba(0,0,0,1), 0 0 5px rgba(34, 197, 94, 0.2)' }}>
            
            <div className="w-16 h-16 rounded-full bg-black border border-green-900/50 shadow-lg flex items-center justify-center">
              <div className="w-12 h-12 rounded-full bg-black flex items-center justify-center relative overflow-hidden">
                {/* Core energy */}
                <motion.div 
                  className={`w-8 h-8 rounded-full ${isOn && isInvested ? 'bg-green-500' : 'bg-green-900'} shadow-lg relative z-10
                    ${isOn && isInvested ? 'shadow-green-500/70' : ''} transition-colors duration-500`}
                  animate={isOn && isInvested ? {
                    boxShadow: ['0 0 10px rgba(34, 197, 94, 0.5)', '0 0 20px rgba(34, 197, 94, 0.7)', '0 0 10px rgba(34, 197, 94, 0.5)']
                  } : {}}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                >
                  {/* Anime-style energy flare */}
                  {isOn && isInvested && (
                    <div className="absolute inset-0 overflow-hidden">
                      <div className="absolute top-1/2 left-0 w-full h-0.5 bg-white/90 -translate-y-1/2"></div>
                      <div className="absolute top-0 left-1/2 w-0.5 h-full bg-white/90 -translate-x-1/2"></div>
                    </div>
                  )}
                </motion.div>
                
                {/* Power core circular patterns */}
                {isOn && isInvested && (
                  <div className="absolute inset-0 w-full h-full rounded-full">
                    <motion.div
                      className="w-full h-full rounded-full border border-green-400/30"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                    />
                    <motion.div
                      className="absolute inset-0 w-full h-full rounded-full border border-green-400/20"
                      animate={{ rotate: -360 }}
                      transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                      style={{ transform: 'scale(0.85)' }}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Fan Blades Container */}
          <div 
            id="fan-blades" 
            className="absolute w-full h-full z-20"
            style={{
              animation: 'spin 5s linear infinite',
              animationPlayState: isOn && isInvested ? 'running' : 'paused',
            }}
          >
            {/* Create 6 large blades - sleek anime mecha style */}
            {[...Array(6)].map((_, i) => (
              <div 
                key={i} 
                className="absolute origin-center"
                style={{
                  width: '70px',
                  height: '180px',
                  position: 'absolute',
                  left: 'calc(50% - 35px)',
                  top: 'calc(50% - 90px)',
                  transform: `rotate(${i * (360 / 6)}deg)`,
                  transformStyle: 'preserve-3d',
                }}
              >
                {/* Main blade with anime tech style */}
                <div 
                  className="absolute h-full w-full"
                  style={{
                    background: 'linear-gradient(to right, #000000, #050505)',
                    clipPath: 'polygon(50% 0%, 70% 5%, 80% 10%, 65% 50%, 50% 100%, 35% 50%, 20% 10%, 30% 5%)',
                    boxShadow: isOn && isInvested ? '0 0 15px rgba(34, 197, 94, 0.3)' : 'none',
                    transform: 'translateZ(2px)',
                  }}
                >
                  {/* Sharp edge with neon glow accent - anime blade style */}
                  <div 
                    className={`absolute h-full w-1 ${isOn && isInvested ? 'bg-green-400' : 'bg-green-900'}`}
                    style={{
                      filter: isOn && isInvested ? 'blur(0.5px)' : 'none',
                      opacity: isOn && isInvested ? 1 : 0.3,
                      left: '50%',
                      boxShadow: isOn && isInvested ? '0 0 8px rgba(34, 197, 94, 0.7)' : 'none',
                    }}
                  ></div>
                  
                  {/* Enhanced anime-style shine line */}
                  <div 
                    className="absolute h-full w-1"
                    style={{
                      left: '70%',
                      background: isOn && isInvested ? 'linear-gradient(to bottom, #ffffff, transparent 80%)' : 'none',
                      opacity: isOn && isInvested ? 0.7 : 0,
                    }}
                  ></div>
                  
                  {/* Cyber detail lines */}
                  <div className="absolute h-full w-full overflow-hidden">
                    {[...Array(2)].map((_, j) => (
                      <div
                        key={j}
                        className={`absolute bg-green-500/50 opacity-10 ${isOn && isInvested ? 'shadow-md shadow-green-500/30' : ''}`}
                        style={{
                          height: '1px',
                          width: '60%',
                          left: '20%',
                          top: `${25 + j * 40}%`,
                          transform: 'rotate(-5deg)',
                        }}
                      ></div>
                    ))}
                  </div>
                </div>
                
                {/* Sharp shadow with deeper contrast - anime shading */}
                <div 
                  className="absolute h-full w-full"
                  style={{
                    background: 'linear-gradient(to right, #000000, #030303)',
                    clipPath: 'polygon(50% 0%, 62% 5%, 70% 10%, 60% 50%, 50% 100%, 40% 50%, 30% 10%, 38% 5%)',
                    transform: 'translateZ(-1px)',
                    opacity: 0.9,
                  }}
                ></div>
              </div>
            ))}
          </div>
          
          {/* Sharper Neon Outline - cyberpunk green glow */}
          <div className={`absolute w-full h-full rounded-full ${isOn && isInvested ? 'opacity-90' : 'opacity-20'} transition-opacity duration-500 z-15`}>
            <div className="w-full h-full rounded-full border border-green-500 filter blur-md"></div>
          </div>
          
          {/* Inner neon accent - cyberpunk aesthetic */}
          <div className={`absolute w-3/5 h-3/5 rounded-full ${isOn && isInvested ? 'opacity-80' : 'opacity-20'} transition-opacity duration-500 z-25`}>
            <div className="w-full h-full rounded-full border border-green-400 filter blur-sm"></div>
          </div>
          
          {/* Minimal protective grille - sleek anime tech */}
          <div className="absolute w-full h-full z-40 pointer-events-none">
            {/* Minimal circular frame */}
            <div className="absolute w-full h-full border border-black rounded-full opacity-50"></div>
            
            {/* Minimal cyberpunk spokes */}
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="absolute bg-green-900/50 origin-center"
                style={{
                  width: '1px',
                  height: '100%',
                  left: 'calc(50% - 0.5px)',
                  top: '0',
                  transform: `rotate(${i * (360 / 5)}deg)`,
                  opacity: 0.7,
                  boxShadow: isOn && isInvested ? '0 0 2px rgba(34, 197, 94, 0.5)' : 'none',
                }}
              ></div>
            ))}
          </div>
          
          {/* Cyberpunk vignette effect inside fan */}
          <div className="absolute inset-0 rounded-full z-35 pointer-events-none" style={{
            background: 'radial-gradient(circle, transparent 40%, rgba(0,0,0,0.9) 100%)',
          }}></div>
          
          {/* Additional cyber glow elements */}
          <div className="absolute inset-0 rounded-full overflow-hidden z-5">
            <div className="absolute -right-20 -top-20 w-40 h-40 bg-green-500/5 rounded-full blur-3xl"></div>
            <div className="absolute -left-10 -bottom-10 w-30 h-30 bg-green-500/10 rounded-full blur-3xl"></div>
            
            {/* Animated flare effect - anime energy */}
            {isOn && isInvested && (
              <motion.div 
                className="absolute -inset-10"
                animate={{
                  background: [
                    'radial-gradient(circle at 30% 70%, rgba(34, 197, 94, 0.05) 0%, transparent 40%)',
                    'radial-gradient(circle at 30% 70%, rgba(34, 197, 94, 0.1) 0%, transparent 40%)',
                    'radial-gradient(circle at 30% 70%, rgba(34, 197, 94, 0.05) 0%, transparent 40%)'
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
              />
            )}
        </div>
        </motion.div>
      </div>
      
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </motion.div>
  );
}

// Animated milestone effect component
function AnimatedMilestoneEffect({ 
  show, 
  milestone, 
  message,
  color 
}: { 
  show: boolean; 
  milestone: number | null;
  message: string;
  color: string;
}) {
  if (!show || !milestone) return null;
  
  return (
    <motion.div 
      className="absolute inset-0 z-60 flex items-center justify-center pointer-events-none"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ 
        opacity: [0, 1, 1, 0],
        scale: [0.8, 1.1, 1, 0.9]
      }}
      transition={{ 
        duration: 3,
        times: [0, 0.2, 0.8, 1],
        ease: "easeInOut" 
      }}
    >
      {/* Background radial glow */}
      <div 
        className="absolute inset-0 rounded-full" 
        style={{ 
          background: `radial-gradient(circle, ${color}20 0%, transparent 70%)`,
          boxShadow: `0 0 40px ${color}50`
        }}
      />
      
      {/* Central notification */}
      <motion.div
        className="bg-black/80 backdrop-blur-lg px-6 py-3 rounded-lg border-2 shadow-2xl flex flex-col items-center"
        style={{ borderColor: color }}
        animate={{
          boxShadow: [
            `0 0 20px ${color}30`,
            `0 0 40px ${color}50`,
            `0 0 20px ${color}30`
          ]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <motion.div 
          className="text-2xl font-bold mb-1" 
          style={{ color }}
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ 
            duration: 1.5, 
            repeat: Infinity,
            ease: "easeInOut" 
          }}
        >
          {milestone}% Zapped
        </motion.div>
        <div className="text-white text-lg font-medium">{message}</div>
      </motion.div>
      
      {/* Particle effects */}
      <motion.div
        className="absolute inset-0"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 rounded-full"
            style={{
              left: '50%',
              top: '50%',
              backgroundColor: color,
              boxShadow: `0 0 5px ${color}`
            }}
            animate={{
              x: [0, Math.cos(i * 30 * Math.PI / 180) * 150],
              y: [0, Math.sin(i * 30 * Math.PI / 180) * 150],
              opacity: [1, 0],
              scale: [1, 0]
            }}
            transition={{
              duration: 2,
              ease: "easeOut"
            }}
          />
        ))}
      </motion.div>
    </motion.div>
  );
}