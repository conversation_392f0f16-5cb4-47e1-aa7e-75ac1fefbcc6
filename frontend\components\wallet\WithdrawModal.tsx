"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import { useWallet } from '@/context/SolanaWalletContext'
import { 
  X, 
  ArrowDownLeft, 
  Wallet, 
  AlertCircle, 
  Check, 
  Loader2,
  History,
  ChevronLeft,
  ExternalLink,
  Clock,
  Copy
} from 'lucide-react'
       // Import the contract function dynamically
       const { withdrawFunds } = await import('@/libs/solana/contract')


// Minimum withdrawal amounts
const MIN_WITHDRAWAL = {
  sol: 0.05,
  flow: 50000,
  bonk: 100000
}

// Define the withdrawal currency type
type WithdrawalCurrency = 'sol' | 'flow' | 'bonk'

// Define withdrawal status type
type WithdrawalStatus = 'pending' | 'completed' | 'rejected'

// Define withdrawal source type - only applies to SOL
type WithdrawalSource = 'zap' | 'referral' | 'both'

// Define withdrawal eligibility type
interface WithdrawalEligibility {
  canWithdraw: boolean
  cooldownRemaining: number // in seconds
  claimsSinceWithdrawal?: number
  lastWithdrawalTime?: Date | null
  reasonCannotWithdraw?: string
}

// Define withdrawal history item type
interface WithdrawalHistoryItem {
  _id: string
  userId: string
  wallet: string
  amount: number
  currency: WithdrawalCurrency
  status: WithdrawalStatus
  createdAt: string
  updatedAt: string
}

interface WithdrawModalProps {
  isOpen: boolean
  onClose: () => void
}

const WithdrawModal = ({ isOpen, onClose }: WithdrawModalProps) => {
  const { user, isAuthenticated } = useAuth();
  const { t } = useLanguage()
  const { publicKey, connected, isLoading: walletLoading, wallet } = useWallet()
  
  // Form state
  const [amount, setAmount] = useState('')
  const [currency, setCurrency] = useState<WithdrawalCurrency>('sol')
  const [withdrawalSource, setWithdrawalSource] = useState<WithdrawalSource>('both')
  
  // UI state
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [copied, setCopied] = useState(false)
  
  // View state
  const [showHistory, setShowHistory] = useState(false)
  
  // History state
  const [withdrawalHistory, setWithdrawalHistory] = useState<WithdrawalHistoryItem[]>([])
  const [isHistoryLoading, setIsHistoryLoading] = useState(false)
  const [historyError, setHistoryError] = useState<string | null>(null)
  
  // Withdrawal eligibility
  const [withdrawalEligibility, setWithdrawalEligibility] = useState<WithdrawalEligibility>({
    canWithdraw: false,
    cooldownRemaining: 0
  })
  const [isEligibilityLoading, setIsEligibilityLoading] = useState(false)
  
  // When currency changes, manage source selection visibility
  useEffect(() => {
    if (currency !== 'sol') {
      setWithdrawalSource('both') // Always use 'both' for non-SOL currencies
    }
  }, [currency])
  
  // Get user balance safely
  const getUserBalance = (currencyType: WithdrawalCurrency): number => {
    if (!user?.wallet?.balance) return 0
    return user.wallet.balance[currencyType] || 0
  }
  
  // Get user referral earnings
  const getUserReferralEarnings = (currencyType: WithdrawalCurrency): number => {
    if (!user?.referralStats) return 0
    switch (currencyType) {
      case 'sol': return user.referralStats.rewardsSol || 0
      case 'flow': return user.referralStats.rewardsFlow || 0
      case 'bonk': return user.referralStats.rewardsBonk || 0
      default: return 0
    }
  }
  
  // Get claims since withdrawal
  const getClaimsSinceWithdrawal = (): number => {
    return user?.claimsSinceWithdrawal || 0
  }
  
  // Check if user needs claims validation
  const needsClaimsValidation = (): boolean => {
    if (currency !== 'sol') return false;
    if (withdrawalSource === 'referral') return false;
    return true;
  }
  
  // Check if user has enough claims
  const hasEnoughClaims = (): boolean => {
    return getClaimsSinceWithdrawal() >= 5;
  }
  
  // Format currency display
  const formatCurrency = (value: number, currencyType: WithdrawalCurrency): string => {
    if (currencyType === 'bonk') {
      return value.toLocaleString('en-US', { maximumFractionDigits: 0 })
    }
    return value.toLocaleString('en-US', { maximumFractionDigits: 4 })
  }

  // Format date
  const formatDate = (dateString: string | Date): string => {
    const date = dateString instanceof Date ? dateString : new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleCopyAddress = () => {
    if (publicKey) {
      navigator.clipboard.writeText(publicKey)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }
  
  // Fetch withdrawal history
  const fetchWithdrawalHistory = async () => {
    if (!isAuthenticated) return
    
    setIsHistoryLoading(true)
    setHistoryError(null)
    
    try {
      const response = await fetch('/api/withdrawal', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      
      const data = await response.json()
      
      if (data.success) {
        setWithdrawalHistory(data.data.recentWithdrawals || [])
      } else {
        setHistoryError(data.error || 'Failed to fetch withdrawal history')
      }
    } catch (error) {
      setHistoryError('Network error. Please try again later.')
    } finally {
      setIsHistoryLoading(false)
    }
  }
  
  // Load withdrawal history when view changes to history
  useEffect(() => {
    if (showHistory && isOpen) {
      fetchWithdrawalHistory()
    }
  }, [showHistory, isOpen, isAuthenticated])
  
  // Reset view state when modal closes
  useEffect(() => {
    if (!isOpen) {
      const timer = setTimeout(() => {
        setShowHistory(false)
        setAmount('')
        setCurrency('sol')
        setError(null)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [isOpen])
  
  
  // Get withdrawable amount based on source selection
  const getWithdrawableAmount = (): number => {
    if (currency !== 'sol') {
      return getUserBalance(currency)
    }
    
    switch (withdrawalSource) {
      case 'zap':
        return getUserBalance(currency)
      case 'referral':
        return getUserReferralEarnings(currency)
      case 'both':
      default:
        return getUserBalance(currency) + getUserReferralEarnings(currency)
    }
  }

  // Format time remaining
  const formatTimeRemaining = (seconds: number): string => {
    if (seconds <= 0) return 'Ready now'
    
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m left`
    } else if (minutes > 0) {
      return `${minutes}m left`
    } else {
      return `${Math.floor(seconds)}s left`
    }
  }

  // Check if user can withdraw based on cooldown period
  const canWithdrawAfterCooldown = (): boolean => {
    if (!user?.lastWithdrawal) return true
    
    const now = new Date().getTime()
    const lastWithdrawal = new Date(user.lastWithdrawal).getTime()
    const cooldownHours = 24
    const cooldownMs = cooldownHours * 60 * 60 * 1000
    
    return (now - lastWithdrawal) >= cooldownMs
  }
  
  // Get cooldown remaining time in seconds
  const getCooldownRemaining = (): number => {
    if (!user?.lastWithdrawal) return 0
    
    const now = new Date().getTime()
    const lastWithdrawal = new Date(user.lastWithdrawal).getTime()
    const cooldownHours = 24
    const cooldownMs = cooldownHours * 60 * 60 * 1000
    const remainingMs = Math.max(0, cooldownMs - (now - lastWithdrawal))
    
    return Math.floor(remainingMs / 1000)
  }
  
  // Check if user can withdraw based on all criteria
  const canWithdraw = (): boolean => {
    // Check cooldown for all currencies
    if (!canWithdrawAfterCooldown()) return false
    
    // For SOL, also check claim requirements (specific to zap earnings)
    if (needsClaimsValidation() && !hasEnoughClaims()) {
      return false
    }
    
    return true
  }
  
  // Validate the form
  const validateForm = (): boolean => {
    setError(null)
    
    if (!isAuthenticated) {
      setError('You must be logged in to withdraw')
      return false
    }
    
    if (!connected || !publicKey) {
      setError('Please connect your wallet first')
      return false
    }
    const strAmount = amount.toString()
    const parsedAmount = parseFloat(strAmount)
    if (isNaN(parsedAmount) || parsedAmount <= 0) {
      setError('Please enter a valid amount')
      return false
    }
    
    if (parsedAmount < MIN_WITHDRAWAL[currency]) {
      setError(`Minimum withdrawal is ${MIN_WITHDRAWAL[currency]} ${currency.toUpperCase()}`)
      return false
    }
    
    // Check withdrawal cooldown for all currencies
    if (!canWithdrawAfterCooldown()) {
      const cooldownRemaining = getCooldownRemaining()
      const hours = Math.floor(cooldownRemaining / 3600)
      const minutes = Math.floor((cooldownRemaining % 3600) / 60)
      setError(`Withdrawal cooldown active. You can withdraw again in ${hours}h ${minutes}m`)
      return false
    }
    
    // Check if user has enough claims for SOL withdrawals (zap earnings)
    // if (needsClaimsValidation() && !hasEnoughClaims()) {
    //   const claims = getClaimsSinceWithdrawal()
    //   setError(`You need at least 5 claims since your last withdrawal. Current: ${claims}`)
    //   return false
    // }
    
    // Check if user has enough balance to withdraw
    const withdrawableAmount = getWithdrawableAmount()
    if (parsedAmount > withdrawableAmount) {
      setError(`Insufficient balance. You have ${formatCurrency(withdrawableAmount, currency)} ${currency.toUpperCase()} available`)
      return false
    }
    
    // For SOL with 'referral' source, check if user has enough referral earnings
    if (currency === 'sol' && withdrawalSource === 'referral') {
      const referralEarnings = getUserReferralEarnings(currency)
      if (parsedAmount > referralEarnings) {
        setError(`Insufficient referral earnings. You have ${formatCurrency(referralEarnings, currency)} ${currency.toUpperCase()} available from referrals`)
        return false
      }
    }
    
    // For SOL with 'zap' source, check if user has enough zap earnings
    if (currency === 'sol' && withdrawalSource === 'zap') {
      const zapEarnings = getUserBalance(currency)
      if (parsedAmount > zapEarnings) {
        setError(`Insufficient zap earnings. You have ${formatCurrency(zapEarnings, currency)} ${currency.toUpperCase()} available from zaps`)
        return false
      }
    }
    
    return true
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !publicKey) return
    
    setIsLoading(true)
    setError(null)
    
    try {
      const amountToWithdraw = parseFloat(amount.toString())
    
      // Step 1: Validate the withdrawal first
      console.log('Validating withdrawal...');
      const validationResponse = await fetch('/api/investment/validate/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amountToWithdraw,
          walletAddress: publicKey,
          source: withdrawalSource,
        }),
      });
      
      if (!validationResponse.ok) {
        const errorText = await validationResponse.text().catch(() => 'Validation failed');
        setError(`Validation error: ${errorText}`);
        setIsLoading(false);
        return;
      }
      
      const validationData = await validationResponse.json();
      
      if (!validationData.success) {
        setError(`Validation failed: ${validationData.error || 'Unknown validation error'}`);
        setIsLoading(false);
        return;
      }
      
      if (!validationData.data.isValid) {
        setError(validationData.error || 'Withdrawal validation failed');
        setIsLoading(false);
        return;
      }
      
      console.log('Validation passed:', validationData.data);
    
      // Step 2: Proceed with the withdrawal based on currency
      if (currency === 'sol') {
        console.log('Processing SOL withdrawal through smart contract...');
                
        if (!wallet) {
          setError('Wallet not available for smart contract interaction')
          setIsLoading(false)
          return
        }
        
        // Step 3: Call the smart contract
        console.log('Proceeding with smart contract call...');
        const contractResult = await withdrawFunds(
          wallet, 
          amountToWithdraw, 
          withdrawalSource as 'zap' | 'referral' | 'both'
        )
        
        if (!contractResult.success) {
          setError(`Smart contract error: ${contractResult.error || 'Unknown error'}`)
          setIsLoading(false)
          return
        }
        
        console.log('Smart contract withdrawal successful:', contractResult.txId)
        
        // Step 4: Update the database with transaction ID
        const response = await fetch('/api/investment/withdraw', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            wallet: publicKey,
            amount: amountToWithdraw,
            currency,
            source: withdrawalSource,
            txId: contractResult.txId // Include transaction ID
          }),
        })
        
        const data = await response.json()
        
        if (data.success) {
          setSuccess(true)
          console.log('Database updated successfully for withdrawal')
          setTimeout(() => {
            onClose()
            setTimeout(() => {
              setAmount('')
              setCurrency('sol')
              setWithdrawalSource('both')
              setSuccess(false)
            }, 300)
          }, 2000)
        } else {
          setError(data.error || 'Failed to update database after successful contract withdrawal')
        }
        
      } else {
        // For non-SOL currencies, use the old endpoint directly
        const response = await fetch('/api/withdrawal/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            wallet: publicKey,
            amount: amountToWithdraw,
            currency,
            source: withdrawalSource
          }),
        })
        
        const data = await response.json()
        
        if (data.success) {
          setSuccess(true)
          setTimeout(() => {
            onClose()
            setTimeout(() => {
              setAmount('')
              setCurrency('sol')
              setWithdrawalSource('both')
              setSuccess(false)
            }, 300)
          }, 2000)
        } else {
          setError(data.error || 'Failed to process withdrawal')
        }
      }
      
    } catch (error: any) {
      console.error('Withdrawal error:', error)
      setError(error.message || 'Network error. Please try again later.')
    } finally {
      setIsLoading(false)
    }
  }
  
  // Select the entire input on focus
  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select()
  }
  
  // Set max amount based on user's balance
  const handleMaxAmount = () => {
    const withdrawableAmount = getWithdrawableAmount()
    setAmount(withdrawableAmount.toFixed(3))
  }
  
  // Get status color based on withdrawal status
  const getStatusColor = (status: WithdrawalStatus) => {
    switch(status) {
      case 'completed':
        return 'text-green-500 bg-green-500/10 border-green-500/20'
      case 'rejected':
        return 'text-red-500 bg-red-500/10 border-red-500/20'
      case 'pending':
      default:
        return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/20'
    }
  }
  
  // Switch to history view
  const handleShowHistory = () => {
    setShowHistory(true)
  }
  
  // Switch back to form view
  const handleShowForm = () => {
    setShowHistory(false)
  }

  const handleAmountChange = (e : any) => {
    const value = e.target.value;
  
    // Allow only numbers and a single decimal
    const regex = /^\d*(\.\d{0,3})?$/;
  
    if (value === '' || regex.test(value)) {
      setAmount(value);
    }
  };
  

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 "
          onClick={onClose}
        >
          <div className="absolute  inset-0 bg-black/80 backdrop-blur-md" />
          
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            transition={{ type: "spring", duration: 0.3 }}
            className="relative bg-gray-900/70 h-[80vh] scroll-y-auto overflow-scroll backdrop-blur-xl border border-gray-800/50 rounded-2xl w-full max-w-md  shadow-xl"
            style={{
              boxShadow: '0 0 30px rgba(0, 0, 0, 0.5), 0 0 15px rgba(20, 200, 100, 0.1)'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {success && (
              <motion.div 
                initial={{ opacity: 0 }} 
                animate={{ opacity: 1 }}
                className="absolute inset-0 z-10 flex flex-col items-center justify-center bg-gray-900/95 backdrop-blur-sm p-6"
              >
                <div className="rounded-full bg-green-500/20 p-3 mb-4">
                  <Check className="w-8 h-8 text-green-500" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{t('withdraw.successful')}</h3>
                <p className="text-gray-400 text-center">
                  {t('withdraw.requestSubmitted')}
                </p>
              </motion.div>
            )}
            
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-800/50">
              <div className="flex items-center">
                {showHistory ? (
                  <>
                    <History className="mr-2 text-green-500" />
                    <h2 className="text-lg font-bold text-white">{t('withdraw.history')}</h2>
                  </>
                ) : (
                  <>
                    <ArrowDownLeft className="mr-2 text-green-500" />
                    <h2 className="text-lg font-bold text-white">{t('withdraw.title')}</h2>
                  </>
                )}
              </div>
              <button 
                onClick={onClose} 
                className="rounded-full p-1 hover:bg-gray-800 transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>
            
            {/* Content */}
            <AnimatePresence mode="wait">
              {showHistory ? (
                /* History View */
                <motion.div
                  key="history"
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.2 }}
                  className="p-4"
                >
                  {/* Back Button */}
                  <button
                    onClick={handleShowForm}
                    className="flex items-center text-green-500 hover:text-green-400 mb-4"
                  >
                    <ChevronLeft className="w-4 h-4 mr-1" />
                    {t('withdraw.backToWithdrawal')}
                  </button>
                  
                  {isHistoryLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <Loader2 className="w-8 h-8 text-green-500 animate-spin" />
                    </div>
                  ) : historyError ? (
                    <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 flex items-start">
                      <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-red-200">{historyError}</p>
                    </div>
                  ) : withdrawalHistory.length === 0 ? (
                    <div className="text-center py-8">
                      <Clock className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                      <p className="text-gray-400">{t('withdraw.noHistoryFound')}</p>
                    </div>
                  ) : (
                    <div className="space-y-3 max-h-[400px] overflow-y-auto pr-1 custom-scrollbar">
                      {withdrawalHistory.map((item) => (
                        <motion.div
                          key={item._id}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-3"
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div className="font-medium text-white">
                              {formatCurrency(item.amount, item.currency)} {item.currency.toUpperCase()}
                            </div>
                            <div className={`text-xs px-2 py-1 rounded-full border ${getStatusColor(item.status)}`}>
                              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                            </div>
                          </div>
                          <div className="text-gray-400 text-sm mb-2 overflow-hidden text-ellipsis">
                            {item.wallet.length > 20 
                              ? `${item.wallet.substring(0, 10)}...${item.wallet.substring(item.wallet.length - 10)}`
                              : item.wallet
                            }
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="text-xs text-gray-500">
                              {formatDate(item.createdAt)}
                            </div>
                            {item.status === 'completed' && (
                              <a 
                                href={`https://solscan.io/tx/${item._id}`} 
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-green-500 hover:text-green-400 flex items-center"
                              >
                                View <ExternalLink className="w-3 h-3 ml-1" />
                              </a>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </motion.div>
              ) : (
                /* Form View */
                <motion.div
                  key="form"
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ duration: 0.2 }}
                  className="p-4 space-y-4"
                >
                  {/* Wallet connection status */}
                  {connected && publicKey ? (
                    <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Check className="w-5 h-5 text-green-500 mr-2" />
                          <span className="text-sm text-green-400">
                            {publicKey.slice(0, 4)}...{publicKey.slice(-4)}
                          </span>
                        </div>
                        <button
                          onClick={handleCopyAddress}
                          className="text-green-500 hover:text-green-400 transition-colors"
                          title="Copy address"
                        >
                          {copied ? (
                            <Check className="w-4 h-4" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3 flex items-start">
                      <AlertCircle className="w-5 h-5 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-yellow-200">
                        {t('withdraw.needWalletConnection')}
                      </p>
                    </div>
                  )}
                  
                  {/* Withdrawal cooldown status */}
                  {user?.lastWithdrawal && (
                    <div className={`rounded-lg p-3 flex items-start ${
                      canWithdrawAfterCooldown() 
                        ? 'bg-green-500/10 border border-green-500/20' 
                        : 'bg-yellow-500/10 border border-yellow-500/20'
                    }`}>
                      {canWithdrawAfterCooldown() ? (
                        <>
                          <Check className="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                          <div>
                            <p className="text-sm text-green-400">Ready to withdraw</p>
                            <p className="text-xs text-gray-400 mt-1">
                              Last withdrawal: {formatDate(user.lastWithdrawal)}
                            </p>
                          </div>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-5 h-5 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                          <div>
                            <p className="text-sm text-yellow-200">Withdrawal cooldown active</p>
                            <p className="text-xs text-gray-400 mt-1">
                              Time until next withdrawal: {formatTimeRemaining(getCooldownRemaining())}
                            </p>
                          </div>
                        </>
                      )}
                    </div>
                  )}

                  {/* Show history link */}
                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={handleShowHistory}
                      className="text-sm text-green-500 hover:text-green-400 flex items-center"
                    >
                      {t('withdraw.viewHistory')} <History className="w-4 h-4 ml-1" />
                    </button>
                  </div>
                  
                  {/* Currency selection */}
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-400">
                      {t('withdraw.currency')}
                    </label>
                    <div className="grid grid-cols-3 gap-2">
                      {(['sol', 'flow', 'bonk'] as const).map((curr) => (
                        <button
                          key={curr}
                          type="button"
                          className={`
                            py-2 px-4 rounded-lg border font-medium text-center text-sm transition-colors
                            ${currency === curr 
                              ? 'bg-green-500/20 border-green-500/50 text-green-400' 
                              : 'bg-gray-800/70 backdrop-blur-sm border-gray-700/50 text-gray-400 hover:bg-gray-700'
                            }
                          `}
                          onClick={() => setCurrency(curr)}
                          disabled={isLoading || !connected}
                        >
                          {curr.toUpperCase()}
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  {/* Withdrawal source selection - only for SOL */}
                  {currency === 'sol' && (
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-400">
                        Withdrawal Source
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        {(['zap', 'referral', 'both'] as const).map((source) => (
                          <button
                            key={source}
                            type="button"
                            className={`
                              py-2 px-4 rounded-lg border font-medium text-center text-sm transition-colors
                              ${withdrawalSource === source 
                                ? 'bg-green-500/20 border-green-500/50 text-green-400' 
                                : 'bg-gray-800/70 backdrop-blur-sm border-gray-700/50 text-gray-400 hover:bg-gray-700'
                              }
                            `}
                            onClick={() => setWithdrawalSource(source)}
                            disabled={isLoading || !connected}
                          >
                            {source === 'zap' && 'Zap Earnings'}
                            {source === 'referral' && 'Referral Bonus'}
                            {source === 'both' && 'Both Sources'}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Available balance */}
                  <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg p-3 border border-gray-700/40">
                    {currency === 'sol' ? (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-400">Zap Earnings:</span>
                          <span className="text-sm font-medium text-white">
                            {formatCurrency(getUserBalance("sol"), currency)} {currency.toUpperCase()}
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-400">Referral Earnings:</span>
                          <span className="text-sm font-medium text-white">
                            {formatCurrency(getUserReferralEarnings(currency), currency)} {currency.toUpperCase()}
                          </span>
                        </div>
                        <div className="border-t border-gray-700/40 pt-2 mt-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-400">Total Available:</span>
                            <span className="text-sm font-medium text-green-400">
                              {formatCurrency(getWithdrawableAmount(), currency)} {currency.toUpperCase()}
                            </span>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-400">{t('withdraw.availableBalance')}</span>
                        <span className="text-sm font-medium text-white">
                          {formatCurrency(getUserBalance(currency), currency)} {currency.toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Amount */}
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <label className="block text-sm font-medium text-gray-400">
                        {t('withdraw.amountToWithdraw')}
                      </label>
                      <button
                        type="button"
                        onClick={handleMaxAmount}
                        className="text-xs text-green-500 hover:text-green-400"
                        disabled={isLoading || !connected || !canWithdrawAfterCooldown()}
                      >
                        {t('withdraw.max')}
                      </button>
                    </div>
                    <div className="relative">
                      <input
                        type="text"
                        value={amount}
                        onChange={(e) => handleAmountChange(e)}
                        onFocus={handleFocus}
                        placeholder={`Min: ${MIN_WITHDRAWAL[currency]}`}
                        className="w-full bg-gray-800/70 backdrop-blur-sm border border-gray-700/50 rounded-lg p-3 text-white placeholder-gray-500 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                        disabled={isLoading || !connected || !canWithdrawAfterCooldown()}
                      />
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                        {currency.toUpperCase()}
                      </div>
                    </div>
                  </div>
                  
                  {/* Error message */}
                  {error && (
                    <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 flex items-start">
                      <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-red-200">{error}</p>
                    </div>
                  )}
                  
                  {/* Submit button */}
                  <button
                    onClick={handleSubmit}
                    className={`
                      w-full py-3 px-4 rounded-xl font-medium flex items-center justify-center transition-colors
                      ${isLoading || walletLoading || !canWithdrawAfterCooldown()
                        ? 'bg-gray-700 cursor-not-allowed' 
                        : connected 
                          ? 'bg-green-500 hover:bg-green-600 text-black' 
                          : 'bg-gray-700 cursor-not-allowed text-gray-300'
                      }
                    `}
                    disabled={isLoading || walletLoading || !connected || !canWithdrawAfterCooldown()}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                        {t('withdraw.processing')}
                      </>
                    ) : (
                      <>
                        <Wallet className="w-5 h-5 mr-2" />
                        {t('withdraw.withdrawButton')}
                      </>
                    )}
                  </button>
                  
                  {/* Minimum withdrawal notice */}
                  <p className="text-xs text-gray-500 text-center">
                    {t('withdraw.minimumWithdrawal')}: {MIN_WITHDRAWAL.sol} SOL / {MIN_WITHDRAWAL.flow} FLOW / {MIN_WITHDRAWAL.bonk} BONK
                  </p>
                  
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default WithdrawModal 