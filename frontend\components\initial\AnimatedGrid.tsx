"use client"

import { useState, useEffect, useRef } from "react"

export default function AnimatedGrid() {
  const [isLoaded, setIsLoaded] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    // Set loaded state after component mounts
    setIsLoaded(true)
  }, [])

  // Animated grid background effect
  useEffect(() => {
    if (!isLoaded) return
    
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const resize = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
    
    resize()
    window.addEventListener('resize', resize)
    
    // Grid configuration
    const gridSize = 40
    const lineWidth = 0.5
    const primaryColor = 'rgba(74, 222, 128, 0.08)'
    const secondaryColor = 'rgba(74, 222, 128, 0.02)'
    
    let frameCount = 0
    
    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      
      // Dynamic grid offset for subtle animation
      const offsetX = Math.sin(frameCount * 0.01) * 5
      const offsetY = Math.cos(frameCount * 0.01) * 5
      
      // Draw vertical lines
      for (let x = 0; x <= canvas.width; x += gridSize) {
        const xPos = x + offsetX
        ctx.beginPath()
        ctx.strokeStyle = x % (gridSize * 4) === 0 ? primaryColor : secondaryColor
        ctx.lineWidth = lineWidth
        ctx.moveTo(xPos, 0)
        ctx.lineTo(xPos, canvas.height)
        ctx.stroke()
      }
      
      // Draw horizontal lines
      for (let y = 0; y <= canvas.height; y += gridSize) {
        const yPos = y + offsetY
        ctx.beginPath()
        ctx.strokeStyle = y % (gridSize * 4) === 0 ? primaryColor : secondaryColor
        ctx.lineWidth = lineWidth
        ctx.moveTo(0, yPos)
        ctx.lineTo(canvas.width, yPos)
        ctx.stroke()
      }
      
      // Add subtle glow points at intersections
      for (let x = 0; x <= canvas.width; x += gridSize * 4) {
        for (let y = 0; y <= canvas.height; y += gridSize * 4) {
          const pulseIntensity = (Math.sin(frameCount * 0.05 + x * 0.01 + y * 0.01) + 1) * 0.5
          
          ctx.beginPath()
          const gradient = ctx.createRadialGradient(
            x + offsetX, y + offsetY, 0,
            x + offsetX, y + offsetY, gridSize
          )
          gradient.addColorStop(0, `rgba(74, 222, 128, ${0.05 * pulseIntensity})`)
          gradient.addColorStop(1, 'rgba(74, 222, 128, 0)')
          
          ctx.fillStyle = gradient
          ctx.arc(x + offsetX, y + offsetY, gridSize, 0, Math.PI * 2)
          ctx.fill()
        }
      }
      
      frameCount++
      requestAnimationFrame(draw)
    }
    
    draw()
    
    return () => {
      window.removeEventListener('resize', resize)
    }
  }, [isLoaded])

  return (
    <canvas 
      ref={canvasRef} 
      className="fixed inset-0 z-0 opacity-70"
      style={{ filter: 'blur(0.5px)' }}
    />
  )
} 