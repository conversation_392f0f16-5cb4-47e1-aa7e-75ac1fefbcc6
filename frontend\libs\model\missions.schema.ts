import mongoose, { Schema } from 'mongoose';
import {
  IMission, 
  MissionType, 
  MissionStatus, 
  RewardType, 
  MissionProgressStatus,
  MissionProgress
} from "../../types/mission"

// Mission schema
const MissionSchema = new Schema<IMission>(
  {
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    type: { 
      type: String, 
      enum: Object.values(MissionType), 
      required: true, 
      index: true 
    },
    status: { 
      type: String, 
      enum: Object.values(MissionStatus), 
      default: MissionStatus.ACTIVE, 
      index: true 
    },
    image: { type: String, required: true },
    taskLink: { type: String },
    reward: {
      type: { 
        type: String, 
        enum: Object.values(RewardType), 
        required: true 
      },
      amount: { type: Number, required: true },
    },
    // Min and max time to complete (in minutes)
    minTimeToComplete: { type: Number, default: 5 }, // Default 10 minutes
    maxTimeToComplete: { type: Number, default: 20 }, // Default 20 minutes
    currentParticipants: { type: Number, default: 0 },
    redeemedBy: [{
      userId: { type: Schema.Types.ObjectId, required: true, ref: 'User' },
      redeemedAt: { type: Date, default: Date.now }
    }],
    startedBy: [{
      userId: { type: Schema.Types.ObjectId, required: true, ref: 'User' },
      startedAt: { type: Date, default: Date.now },
      status: { 
        type: String, 
        enum: Object.values(MissionProgressStatus),
        default: MissionProgressStatus.IN_PROGRESS
      },
      estimatedCompletionAt: { type: Date, required: true }
    }],
    isHighlighted: { type: Boolean, default: false, index: true }
  },
  {
    timestamps: true,
    versionKey: false
  }
);

// Create indexes for faster queries
MissionSchema.index({ "redeemedBy.userId": 1 });
MissionSchema.index({ "startedBy.userId": 1 });
MissionSchema.index({ "status": 1, "startDate": 1 });
MissionSchema.index({ "type": 1, "status": 1 });

// Helper method to check if a user has redeemed this mission
MissionSchema.methods.isRedeemedByUser = function(userId: string): boolean {
  if (!userId) return false;
  return this.redeemedBy.some((redemption: any) => 
    redemption.userId.toString() === userId.toString()
  );
};

// Helper method to check if a user has started this mission
MissionSchema.methods.isStartedByUser = function(userId: string): boolean {
  if (!userId) return false;
  return this.startedBy && this.startedBy.some((progress: any) => 
    progress.userId.toString() === userId.toString()
  );
};

// Helper method to get a user's progress on this mission
MissionSchema.methods.getUserProgress = function(userId: string): MissionProgress | null {
  if (!userId || !this.startedBy) return null;
  
  const progress = this.startedBy.find((p: any) => 
    p.userId.toString() === userId.toString()
  );
  
  return progress || null;
};

// Helper method to add a user to the redeemedBy array
MissionSchema.methods.addRedemption = function(userId: string) {
  if (!userId) return this;
  
  if (!this.isRedeemedByUser(userId)) {
    this.redeemedBy.push({
      userId,
      redeemedAt: new Date()
    });
    this.currentParticipants += 1;
    
    // Update progress status if user has started the mission
    if (this.isStartedByUser(userId)) {
      const progressIndex = this.startedBy.findIndex((p: any) => 
        p.userId.toString() === userId.toString()
      );
      
      if (progressIndex >= 0) {
        this.startedBy[progressIndex].status = MissionProgressStatus.CLAIMED;
      }
    }
  }
  return this;
};

// Helper method to start a mission for a user with random timer
MissionSchema.methods.startMission = function(userId: string) {
  if (!userId) return this;
  
  // If the user hasn't started the mission yet
  if (!this.isStartedByUser(userId)) {
    const now = new Date();
    
    // Generate random time between min and max minutes
    const minTimeMs = (this.minTimeToComplete || 10) * 60 * 1000; // Default 10 minutes
    const maxTimeMs = (this.maxTimeToComplete || 20) * 60 * 1000; // Default 20 minutes
    const randomTimeMs = Math.floor(Math.random() * (maxTimeMs - minTimeMs + 1)) + minTimeMs;
    
    this.startedBy = this.startedBy || [];
    this.startedBy.push({
      userId,
      startedAt: now,
      status: MissionProgressStatus.IN_PROGRESS,
      estimatedCompletionAt: new Date(now.getTime() + randomTimeMs)
    });
  }
  
  return this;
};

// Helper to check if mission is completable by user (timer expired)
MissionSchema.methods.isCompletableByUser = function(userId: string): boolean {
  if (!userId) return false;
  
  const progress = this.getUserProgress(userId);
  if (!progress) return false;
  
  if (progress.status !== MissionProgressStatus.IN_PROGRESS) {
    return false;
  }
  
  const now = new Date();
  return now >= new Date(progress.estimatedCompletionAt);
};

// Helper to get remaining time for mission completion in minutes
MissionSchema.methods.getRemainingTimeInMinutes = function(userId: string): number {
  if (!userId) return 0;
  
  const progress = this.getUserProgress(userId);
  if (!progress) return 0;
  
  if (progress.status !== MissionProgressStatus.IN_PROGRESS) {
    return 0;
  }
  
  const now = new Date();
  const completionTime = new Date(progress.estimatedCompletionAt);
  
  if (now >= completionTime) return 0;
  
  const remainingMs = completionTime.getTime() - now.getTime();
  return Math.ceil(remainingMs / (1000 * 60));
};

// Create model if it doesn't exist
export const Mission = mongoose.models.Mission || 
  mongoose.model<IMission>('Mission', MissionSchema);

export default Mission;
