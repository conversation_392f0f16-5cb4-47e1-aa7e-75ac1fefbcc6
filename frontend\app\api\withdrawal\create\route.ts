import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { User } from '@/libs/model/user.schema';
import { Withdrawal, WithdrawalStatus, WithdrawalCurrency } from '@/libs/model/withdrawal.schema';
import { revalidatePath } from 'next/cache';

export async function POST(req: NextRequest) {
  try {
    // Get user ID from cookie or header
    const userId = req.cookies.get('userId')?.value;
    const telegramId = req.headers.get('x-telegram-id') || req.cookies.get('telegramId')?.value;
    // const userId = "68380e3e3b44aa85b374c1f0";
    // const telegramId = 5195131141;
    
    
    if (!userId && !telegramId) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find user
    let user;
    if (userId) {
      user = await User.findById(userId);
    } else if (telegramId) {
      user = await User.findOne({ telegramId });
    }
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Get withdrawal details from request body
    const body = await req.json();
    const { wallet, amount, currency } = body;
    
    // Validate required fields
    if (!wallet || !amount || !currency) {
      return NextResponse.json(
        { success: false, error: 'Wallet address, amount, and currency are required' },
        { status: 400 }
      );
    }
    
    // Validate amount
    if (amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Amount must be greater than 0' },
        { status: 400 }
      );
    }
    
    // Validate currency
    if (!Object.values(WithdrawalCurrency).includes(currency as WithdrawalCurrency)) {
      return NextResponse.json(
        { success: false, error: 'Invalid currency' },
        { status: 400 }
      );
    }
    
    // Check user balance
    // Get the specific currency balance from user's wallet
    const userBalance = user.wallet.balance[currency];
    
    // Check if user has enough balance for the specific currency
    if (!userBalance || userBalance < amount) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Insufficient ${currency.toUpperCase()} balance. Available: ${userBalance || 0} ${currency.toUpperCase()}` 
        },
        { status: 400 }
      );
    }
    
    try {
      // Process withdrawal (simulate blockchain transaction)
      // In the future, this will be replaced with actual Solana integration
      
      // Deduct amount from user's balance
      user.wallet.balance[currency as keyof typeof user.wallet.balance] -= amount;
      user.wallet.lastUpdated = new Date();
      await user.save();
      
      // Create completed withdrawal request
      const withdrawal = new Withdrawal({
        user: user._id,
        wallet,
        amount,
        currency,
        status: WithdrawalStatus.COMPLETED, // Mark as completed immediately
      });
      
      // Save withdrawal request
      await withdrawal.save();
      
      // Revalidate pages to update UI with new balance
      revalidatePath('/wallet');
      revalidatePath('/');
      
      // Return success response
      return NextResponse.json({
        success: true,
        data: withdrawal
      });
      
    } catch (processingError) {
      console.error('Error processing withdrawal:', processingError);
      
      // Create failed withdrawal record without deducting balance
      const failedWithdrawal = new Withdrawal({
        user: user._id,
        wallet,
        amount,
        currency,
        status: WithdrawalStatus.FAILED,
        notes: 'System error during processing'
      });
      
      await failedWithdrawal.save();
      
      return NextResponse.json(
        { success: false, error: 'Failed to process withdrawal', withdrawal: failedWithdrawal },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Error creating withdrawal:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to create withdrawal request' },
      { status: 500 }
    );
  }
}
