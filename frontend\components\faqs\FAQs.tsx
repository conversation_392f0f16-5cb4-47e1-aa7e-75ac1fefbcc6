"use client"
import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  HelpCircle, 
  ChevronDown, 
  Search, 
  Wallet, 
  TrendingUp, 
  Users, 
  Shield,
  Zap,
  Gift,
  X
} from 'lucide-react'
import { useLanguage } from '@/context/LanguageContext'

const FAQs = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState('all')
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)
  const { t } = useLanguage()

  const categories = [
    { id: 'all', name: 'All', icon: HelpCircle },
    { id: 'wallet', name: 'Wallet', icon: Wallet },
    { id: 'trading', name: 'Trading', icon: TrendingUp },
    { id: 'missions', name: 'Missions', icon: Gift },
    { id: 'referrals', name: 'Referrals', icon: Users },
    { id: 'security', name: 'Security', icon: Shield }
  ]

  const faqs = [
    {
      id: 'wallet-1',
      category: 'wallet',
      question: 'How do I connect my wallet to Flow Trade?',
      answer: 'To connect your wallet, click the "Connect Wallet" button in the wallet section. We support popular Solana wallets like Phantom, Solflare, and others. Make sure you have a compatible wallet installed in your browser.'
    },
    {
      id: 'wallet-2',
      category: 'wallet',
      question: 'Is my wallet secure with Flow Trade?',
      answer: 'Yes, your wallet security is our top priority. We never store your private keys and all transactions are signed directly by your wallet. We use industry-standard security practices and audited smart contracts.'
    },
    {
      id: 'trading-1',
      category: 'trading',
      question: 'What trading strategies does Flow Trade offer?',
      answer: 'Flow Trade offers various algorithmic trading strategies including DCA (Dollar Cost Averaging), momentum trading, and arbitrage opportunities. Our AI-powered system analyzes market conditions to optimize your trading performance.'
    },
    {
      id: 'trading-2',
      category: 'trading',
      question: 'What are the minimum trading amounts?',
      answer: 'The minimum trading amount varies by strategy and market conditions. Generally, you can start with as little as 0.01 SOL. We recommend starting small to familiarize yourself with the platform.'
    },
    {
      id: 'missions-1',
      category: 'missions',
      question: 'How do missions work?',
      answer: 'Missions are tasks you can complete to earn FLOW tokens and SOL rewards. There are daily tasks, partner missions, and special events. Simply start a mission, complete the required action, and claim your reward after verification.'
    },
    {
      id: 'missions-2',
      category: 'missions',
      question: 'How long does mission verification take?',
      answer: 'Mission verification typically takes 1-5 minutes for social tasks and up to 30 minutes for trading-related missions. The verification time is displayed when you start each mission.'
    },
    {
      id: 'referrals-1',
      category: 'referrals',
      question: 'How does the referral system work?',
      answer: 'Share your unique referral link with friends. When they join Flow Trade and complete their first trade, you both earn bonus rewards. You can track your referral performance in the referrals section.'
    },
    {
      id: 'referrals-2',
      category: 'referrals',
      question: 'What rewards do I get for referrals?',
      answer: 'You earn a percentage of your referrals\' trading fees as rewards, plus bonus FLOW tokens for each successful referral. The exact amounts depend on your referral tier and current promotions.'
    },
    {
      id: 'security-1',
      category: 'security',
      question: 'How do I keep my account secure?',
      answer: 'Always access Flow Trade through official links, never share your private keys, enable 2FA if available, and be cautious of phishing attempts. We will never ask for your private keys or seed phrases.'
    },
    {
      id: 'security-2',
      category: 'security',
      question: 'What should I do if I suspect unauthorized access?',
      answer: 'Immediately disconnect your wallet, change your passwords, and contact our support team. Monitor your wallet transactions and report any suspicious activity to us and your wallet provider.'
    }
  ]

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId)
  }

  return (
    <div className="max-w-[500px] mx-auto">
      {/* Header */}
      <div className="relative mb-5 flex items-center">
        <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-gradient-to-b from-purple-400 to-transparent rounded-full"></div>
        <h1 className="text-xl font-bold ml-3 text-white">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-purple-300">
            {t('wallet.faqs')}
          </span>
        </h1>
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-20 h-[1px] bg-gradient-to-r from-purple-500/50 to-transparent"></div>
        <div className="absolute right-5 -top-2 w-3 h-3 rounded-full bg-purple-500/20 blur-xl"></div>
        <div className="absolute right-3 -bottom-2 w-2 h-2 rounded-full bg-purple-500/30 blur-md"></div>
      </div>

      {/* Search Bar */}
      <div className="relative mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search FAQs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full bg-black/40 border border-gray-800/50 rounded-lg pl-10 pr-10 py-3 text-sm text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 transition-colors"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Category Tabs */}
      <div className="grid grid-cols-3 gap-2 mb-5">
        {categories.map((category) => {
          const Icon = category.icon
          const isActive = activeCategory === category.id
          
          return (
            <div
              key={category.id}
              className={`
                relative cursor-pointer rounded-lg border p-2 flex flex-col items-center justify-center
                transition-all duration-200 backdrop-blur-sm hover:-translate-y-1
                ${isActive 
                  ? 'border-purple-500/50 bg-black/70 shadow-lg shadow-purple-500/10' 
                  : 'border-gray-800/50 bg-black/30 hover:bg-black/40'}
              `}
              onClick={() => setActiveCategory(category.id)}
            >
              {isActive && (
                <motion.div 
                  className="absolute inset-0 -z-10 rounded-lg opacity-30"
                  layoutId="categoryGlow"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  style={{ boxShadow: "0 0 15px 2px rgba(147, 51, 234, 0.2)" }}
                />
              )}

              <div className={`
                mb-1 p-1 rounded
                ${isActive ? 'text-purple-400' : 'text-gray-400'}
              `}>
                <Icon className={`w-4 h-4 ${isActive ? 'stroke-[2.5px]' : 'stroke-[1.5px]'}`} />
              </div>
              <span className={`
                text-xs font-medium text-center
                ${isActive ? 'text-purple-400' : 'text-gray-400'}
              `}>
                {category.name}
              </span>
              
              {isActive && (
                <motion.div
                  layoutId="activeCategoryIndicator"
                  className="absolute bottom-0 w-8 h-[2px] bg-purple-500/70 rounded-full"
                  transition={{ duration: 0.2 }}
                />
              )}
            </div>
          )
        })}
      </div>

      {/* FAQ List */}
      <div className="space-y-3">
        {filteredFAQs.length === 0 ? (
          <div className="text-center py-8">
            <HelpCircle className="w-12 h-12 text-gray-600 mx-auto mb-3" />
            <p className="text-gray-400">No FAQs found matching your search.</p>
          </div>
        ) : (
          filteredFAQs.map((faq, index) => (
            <motion.div
              key={faq.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="bg-black/40 rounded-lg border border-gray-800/50 overflow-hidden"
            >
              <button
                onClick={() => toggleFAQ(faq.id)}
                className="w-full p-4 text-left flex items-center justify-between hover:bg-black/20 transition-colors"
              >
                <span className="text-sm font-medium text-white pr-4">
                  {faq.question}
                </span>
                <motion.div
                  animate={{ rotate: expandedFAQ === faq.id ? 180 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <ChevronDown className="w-4 h-4 text-gray-400 flex-shrink-0" />
                </motion.div>
              </button>
              
              <AnimatePresence>
                {expandedFAQ === faq.id && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="overflow-hidden"
                  >
                    <div className="px-4 pb-4 border-t border-gray-800/50">
                      <p className="text-sm text-gray-300 mt-3 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))
        )}
      </div>

      {/* Contact Support */}
      <div className="mt-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-4 border border-purple-500/20">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
            <HelpCircle className="w-5 h-5 text-purple-400" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-white">Still need help?</h3>
            <p className="text-xs text-gray-400">Contact our support team for personalized assistance</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FAQs
