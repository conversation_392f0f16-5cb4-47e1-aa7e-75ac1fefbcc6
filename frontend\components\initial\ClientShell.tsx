"use client"

import { useState, useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import Loader from "@/components/initial/Loader"
import { setLoaderState } from "@/components/initial/NavigationWrapper"
import { showSuccessToast } from "@/components/initial/ToasterProvider"

export default function ClientShell({ children }: { children: React.ReactNode }) {
  const [loading, setLoading] = useState(true)
  const [initiated, setInitiated] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  // Handle navigation in Telegram environment
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    // Simplified direct navigation handler for Telegram
    const handlePopState = () => {
      const currentPath = window.location.pathname
      if (currentPath !== pathname) {
        router.push(currentPath)
      }
    }
    
    window.addEventListener('popstate', handlePopState)
    
    // Also handle back button properly in Telegram
    window.addEventListener('pageshow', (e) => {
      if (e.persisted) {
        // Page was restored from cache (user pressed back)
        const currentPath = window.location.pathname
        if (currentPath !== pathname) {
          router.push(currentPath)
        }
      }
    })
    
    return () => {
      window.removeEventListener('popstate', handlePopState)
      window.removeEventListener('pageshow', handlePopState)
    }
  }, [pathname, router])

  // Initialize client-side functionality
  useEffect(() => {
    if (typeof window === 'undefined') return
    
    // Mark as initiated to start loading process
    setInitiated(true)
    
    // Skip loader in development if needed
    const isDevEnv = process.env.NODE_ENV === 'development'
    const shouldSkipLoader = false // Set to true to skip loader in development
    
    if (isDevEnv && shouldSkipLoader) {
      setLoading(false)
      setLoaderState(false)
    }
  }, [])

  const handleLoaderComplete = () => {
    setLoading(false)
    setLoaderState(false)
  }

  return (
    <>
      {initiated && loading && <Loader onComplete={handleLoaderComplete} />}
      {children}
    </>
  )
} 