// Import all translation files
import enTranslation from '@/locales/en/translation.json'
import ruTranslation from '@/locales/ru/translation.json'
import hiTranslation from '@/locales/hi/translation.json'
import frTranslation from '@/locales/fr/translation.json'
import esTranslation from '@/locales/es/translation.json'
import deTranslation from '@/locales/de/translation.json'

export type Language = 'en' | 'ru' | 'hi' | 'fr' | 'es' | 'de'

export const languageNames: Record<Language, string> = {
  en: 'English',
  ru: 'Русский',
  hi: 'हिन्दी',
  fr: 'Français',
  es: 'Español',
  de: 'Deutsch',
}

export const languageFlags: Record<Language, string> = {
  en: '🇬🇧',
  ru: '🇷🇺',
  hi: '🇮🇳',
  fr: '🇫🇷',
  es: '🇪🇸',
  de: '🇩🇪',
}

// Export all translations
export const translations: Record<Language, any> = {
  en: enTranslation,
  ru: ruTranslation,
  hi: hiTranslation,
  fr: frTranslation,
  es: esTranslation,
  de: deTranslation,
}

// Function to get a translated value from a nested key (e.g., "app.name")
export const getTranslatedValue = (
  key: string, 
  language: Language, 
  defaultLanguage: Language = 'en', 
  params?: Record<string, string>
): string => {
  const translationSet = translations[language] || translations[defaultLanguage]
  
  // Handle nested keys (e.g., "app.name")
  const keys = key.split('.')
  let value: any = translationSet
  
  // Navigate through the nested objects
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k]
    } else {
      // If key not found, try in default language
      value = null
      break
    }
  }
  
  // If not found in current language, try in default language
  if (value === null && language !== defaultLanguage) {
    value = translations[defaultLanguage]
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        value = key // Last resort: return the key itself
        break
      }
    }
  }
  
  // If value is not a string, return the key
  if (typeof value !== 'string') {
    return key
  }
  
  let text = value
  
  // Replace parameters if provided
  if (params) {
    Object.entries(params).forEach(([paramKey, paramValue]) => {
      text = text.replace(`{${paramKey}}`, paramValue)
    })
  }

  return text
} 