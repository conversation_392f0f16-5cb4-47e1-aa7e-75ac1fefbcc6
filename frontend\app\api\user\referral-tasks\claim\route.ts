import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { User } from '@/libs/model/user.schema';
import { referralTasks } from '@/data';
import { cookies } from 'next/headers';

export async function POST(req: NextRequest) {
  try {
    // Get user ID from cookies
    const userId = (await cookies()).get('userId')?.value;
    
    if (!userId) {
      return NextResponse.json({ 
        error: 'Authentication required' 
      }, { status: 401 });
    }
    
    // Parse request body
    const body = await req.json();
    const { taskId } = body;
    
    if (!taskId) {
      return NextResponse.json({ 
        error: 'Task ID is required' 
      }, { status: 400 });
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ 
        error: 'User not found' 
      }, { status: 404 });
    }
    
    // Find the task in our predefined tasks
    const task = referralTasks.find(t => t.id === taskId);
    if (!task) {
      return NextResponse.json({ 
        error: 'Task not found' 
      }, { status: 404 });
    }
    
    // Check if task has already been claimed
    if (user.hasRedeemedReferralTask(taskId)) {
      return NextResponse.json({ 
        error: 'Task already claimed' 
      }, { status: 400 });
    }
    
    // Check if user has enough referrals
    const totalReferrals = user.referrals?.length || 0;
    if (totalReferrals < task.required) {
      return NextResponse.json({ 
        error: `Not enough referrals. You need ${task.required} referrals, but you have ${totalReferrals}.` 
      }, { status: 400 });
    }
    
    // All checks passed, update user wallet based on the task reward
    if (task.reward.currency === 'bonk') {
      user.wallet.balance.bonk += task.reward.amount;
      user.referralStats.rewardsBonk += task.reward.amount;
    } else if (task.reward.currency === 'sol') {
      user.wallet.balance.sol += task.reward.amount;
      user.referralStats.rewardsSol += task.reward.amount;
    } else if (task.reward.currency === 'flow') {
      user.wallet.balance.flow += task.reward.amount;
      user.referralStats.rewardsFlow += task.reward.amount;
    }
    
    // Mark task as redeemed
    user.redeemReferralTask(taskId);
    
    // Update wallet last updated timestamp
    user.wallet.lastUpdated = new Date();
    
    // Save user
    await user.save();
    
    // Return success response
    return NextResponse.json({
      message: 'Task reward claimed successfully',
      reward: {
        amount: task.reward.amount,
        currency: task.reward.currency
      }
    });
    
  } catch (error) {
    console.error('Error claiming referral task reward:', error);
    return NextResponse.json({ 
      error: 'Failed to claim reward' 
    }, { status: 500 });
  }
} 