import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { News } from '@/libs/model/news.schema';

export async function GET(req: NextRequest) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const category = searchParams.get('category');
    const query = searchParams.get('query');
    const highlighted = searchParams.get('highlighted');
    const published = searchParams.get('published');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    
    // Build query
    const filter: any = {};
    
    if (category) {
      filter.category = category;
    }
    
    if (query) {
      filter.$or = [
        { title: { $regex: query, $options: 'i' } },
        { summary: { $regex: query, $options: 'i' } },
        { content: { $regex: query, $options: 'i' } },
        { authorName: { $regex: query, $options: 'i' } },
      ];
    }
    
    if (highlighted !== null) {
      filter.isHighlighted = highlighted === 'true';
    }
    
    if (published !== null) {
      filter.isPublished = published === 'true';
    }
    
    // Calculate skip value for pagination
    const skip = (page - 1) * limit;
    
    // Fetch news articles
    const articles = await News.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Count total articles for pagination
    const total = await News.countDocuments(filter);
    
    // Return response
    return NextResponse.json({
      success: true,
      data: articles,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error) {
    console.error('Error fetching news articles:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch news articles' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Get news ID or slug from query params
    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get('id');
    const slug = searchParams.get('slug');
    
    if (!id && !slug) {
      return NextResponse.json(
        { error: 'Either news ID or slug is required' },
        { status: 400 }
      );
    }
    
    // Find news article by ID or slug
    let newsArticle;
    
    if (id) {
      newsArticle = await News.findById(id);
    } else if (slug) {
      newsArticle = await News.findOne({ slug });
    }
    
    if (!newsArticle) {
      return NextResponse.json(
        { error: 'News article not found' },
        { status: 404 }
      );
    }
    
    // Delete news article
    if (id) {
      await News.findByIdAndDelete(id);
    } else if (slug) {
      await News.findOneAndDelete({ slug });
    }
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'News article deleted successfully'
    });
    
  } catch (error) {
    console.error('Error deleting news article:', error);
    
    return NextResponse.json(
      { error: 'Failed to delete news article' },
      { status: 500 }
    );
  }
}
