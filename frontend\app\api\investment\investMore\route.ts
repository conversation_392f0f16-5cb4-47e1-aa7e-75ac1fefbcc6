// API endpoint to add more funds to an existing investment
// This preserves earned amount while increasing the initial investment

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment, InvestmentStatus } from '@/libs/model/investment.schema';
import { User } from '@/libs/model/user.schema';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';
import { TIME_CONFIG } from '@/libs/config';
import { getValidReferrers } from '../create/route';

// Helper function to process multi-level referral rewards using the new dynamic system
async function processReferralRewards(userId: Types.ObjectId, investmentAmount: number, currency: string = 'sol') {
  try {
    // Get valid referrers using our new function
    const validReferrers = await getValidReferrers(userId.toString());
    
    if (validReferrers.length === 0) {
      console.log('No valid referrers found for investMore');
      return;
    }
    
    console.log(`Processing investMore rewards for ${validReferrers.length} valid referrers`);
    
    // Process each valid referrer
    for (const referrerData of validReferrers) {
      try {
        // Find the referrer investment by wallet address
        const referrerInvestment = await Investment.findOne({
          walletAddress: referrerData.walletAddress,
          status: InvestmentStatus.ACTIVE
        });
        
        if (!referrerInvestment) {
          console.log(`Referrer investment not found for wallet: ${referrerData.walletAddress}`);
          continue;
        }
        
        // Find the referrer user
        const referrer = await User.findById(referrerInvestment.user);
        if (!referrer) {
          console.log(`Referrer user not found for investment: ${referrerInvestment._id}`);
          continue;
        }
        
        // Calculate reward amount based on referrer's level percentage
        const rewardAmount = investmentAmount * (referrerData.percentage / 100);
        
        // Initialize referral stats if they don't exist
        if (!referrer.referralStats) {
          referrer.referralStats = {
            totalReferrals: 0,
            rewardsSol: 0,
            rewardsFlow: 0,
            rewardsBonk: 0
          };
        }
        
        // Check ROI cap (250%)
        const maxEarnings = referrerInvestment.initialAmount * 2.5; // 250% of initial
        const currentEarnings = referrerInvestment.earnedAmount;
        
        // Calculate adjusted reward if it would exceed cap
        let adjustedRewardAmount = rewardAmount;
        if (currentEarnings + rewardAmount > maxEarnings) {
          adjustedRewardAmount = Math.max(0, maxEarnings - currentEarnings);
        }
        
        // Only proceed if there's reward to give
        if (adjustedRewardAmount > 0) {
          // Add referral bonus to referrer's investment
          await referrerInvestment.addReferralBonus(adjustedRewardAmount);
          
          // Update referral stats
          if (currency === 'sol') {
            referrer.referralStats.rewardsSol += adjustedRewardAmount;
          } else if (currency === 'flow') {
            referrer.referralStats.rewardsFlow += adjustedRewardAmount;
          } else if (currency === 'bonk') {
            referrer.referralStats.rewardsBonk += adjustedRewardAmount;
          }
          
          // Find and update the specific referral entry
          if (referrer.referrals && referrer.referrals.length > 0) {
            const referralIndex = referrer.referrals.findIndex(
              (ref: any) => ref.user && ref.user.toString() === userId.toString()
            );
            
            if (referralIndex >= 0) {
              if (currency === 'sol') {
                referrer.referrals[referralIndex].earnedSol += adjustedRewardAmount;
              } else if (currency === 'flow') {
                referrer.referrals[referralIndex].earnedFlow += adjustedRewardAmount;
              } else if (currency === 'bonk') {
                referrer.referrals[referralIndex].earnedBonk += adjustedRewardAmount;
              }
              
              referrer.referrals[referralIndex].lastEarned = new Date();
            }
          }
          
          // Save referrer changes
          await referrer.save();
          
          console.log(`Processed investMore referral reward: Level ${referrerData.level}, ${referrerData.percentage}%, Amount: ${adjustedRewardAmount} ${currency.toUpperCase()}`);
        } else {
          console.log(`Referrer ${referrerData.walletAddress} has reached ROI cap, skipping investMore reward`);
        }
        
      } catch (error) {
        console.error(`Error processing investMore referrer ${referrerData.walletAddress}:`, error);
        continue; // Continue with next referrer
      }
    }
    
  } catch (error) {
    console.error('Error processing investMore referral rewards:', error);
  }
}

// POST handler to add more funds to an existing investment
export async function POST(req: NextRequest) {
  try {
    // Get the user ID from cookies
  // const userId = (await cookies()).get('userId')?.value;
  const userId = "68380e3e3b44aa85b374c1f0";

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find the user
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { amount, investmentId, walletAddress, txId } = body;
    
    // Validate request
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return NextResponse.json(
        { success: false, error: 'Valid amount is required' },
        { status: 400 }
      );
    }
    
    if (!investmentId) {
      return NextResponse.json(
        { success: false, error: 'Investment ID is required' },
        { status: 400 }
      );
    }
    
    // Convert to number and fixed precision
    const additionalAmount = parseFloat(parseFloat(amount).toFixed(6));
    
    if (additionalAmount < 0.05) {
      return NextResponse.json(
        { success: false, error: "Minimum fuelling amount is 0.05 SOL" },
        { status: 400 }
      );
    }
    // Find the investment
    let investment;
    try {
      investment = await Investment.findOne({
        _id: new Types.ObjectId(investmentId),
        user: new Types.ObjectId(userId),
        status: 'active'
      });
    } catch (e) {
      return NextResponse.json(
        { success: false, error: 'Invalid investment ID' },
        { status: 400 }
      );
    }
    
    if (!investment) {
      return NextResponse.json(
        { success: false, error: 'Investment not found or not active' },
        { status: 404 }
      );
    }
    
    // Store original values for calculations
    const originalInitialAmount = investment.initialAmount;
    const originalEarnedAmount = investment.earnedAmount;
    const originalPercentageOfROI = investment.percentageOfROI || 0;
    
    // Calculate the current ROI progress as a percentage (0-250%)
    // This is the percentage of the initial amount that has been earned
    const currentROIPercentage = (originalEarnedAmount / originalInitialAmount) * 100;
    
    // Add the additional amount to the initial investment
    investment.initialAmount += additionalAmount;
    
    // Recalculate the percentage of ROI based on the new initial amount
    // Formula: (earnedAmount / newInitialAmount) * 100
    investment.percentageOfROI = (originalEarnedAmount / investment.initialAmount) * 100;
    
    // Calculate the new maximum potential earnings based on the new initial amount
    const newMaxEarnings = investment.initialAmount * (TIME_CONFIG.TOTAL_ROI_CAP - 1);
    
    // Calculate the new target for total earnings (including what's already earned)
    const newTotalEarningsPotential = investment.initialAmount * TIME_CONFIG.TOTAL_ROI_CAP;
    
    // Update the lastCalculationTime to reset the timer for zap progress
    investment.lastCalculationTime = new Date();
    
    // Save the updated investment
    await investment.save();
    
    // Process multi-level referral rewards for the additional amount using new system
    await processReferralRewards(user._id, additionalAmount, investment.currency);
    
    return NextResponse.json({
      success: true,
      data: {
        investment: {
          id: investment._id,
          initialAmount: investment.initialAmount,
          earnedAmount: investment.earnedAmount,
          currency: investment.currency,
          percentageOfROI: investment.percentageOfROI,
          potentialEarnings: newTotalEarningsPotential
        },
        txId // Include transaction ID in response
      },
      message: 'Investment increased successfully! Your AI-powered Zapping engine has been boosted!'
    });
    
  } catch (error: any) {
    console.error('Error adding to investment:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to add to investment' },
      { status: 500 }
    );
  }
}
