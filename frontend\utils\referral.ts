'use client';

// Only import WebApp in client environments
let WebApp: any = null;
if (typeof window !== 'undefined') {
  try {
    // Dynamic import to prevent server-side errors
    WebApp = require('@twa-dev/sdk').default;
  } catch (e) {
    console.error('Error importing WebApp SDK:', e);
  }
}

/**
 * Extracts the referral code from various sources
 * 1. From Telegram WebApp init data
 * 2. From URL parameters
 */
export const extractReferralCode = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    // Method 1: Check if in Telegram Mini App and try to get startParam
    if (WebApp && WebApp.initData) {
      // Try to get from Telegram startParam (the most reliable source)
      if (WebApp.initDataUnsafe?.start_param) {
        const startParam = WebApp.initDataUnsafe.start_param;
        
        if (startParam.startsWith('ref_')) {
          return startParam;
        }
        return `ref_${startParam}`;
      }

      // Try to parse the initData as a URL query string
      try {
        const initDataParams = new URLSearchParams(WebApp.initData);
        const startParamFromInitData = initDataParams.get('start_param');
        
        if (startParamFromInitData) {
          if (startParamFromInitData.startsWith('ref_')) {
            return startParamFromInitData;
          }
          return `ref_${startParamFromInitData}`;
        }
      } catch (error) {
        // Silent error
        console.log('Error parsing WebApp.initData as URLSearchParams:', error);
      }
    }
    
    // Method 2: Check URL parameters (for when launched as a web page)
    const urlParams = new URLSearchParams(window.location.search);
    
    // Check for start (Telegram standard)
    const startParam = urlParams.get('start');
    if (startParam) {
      if (startParam.startsWith('ref_')) {
        return startParam;
      }
      return `ref_${startParam}`;
    }
    
    // Check for tgWebAppStartParam (sometimes used)
    const tgWebAppStartParam = urlParams.get('tgWebAppStartParam');
    if (tgWebAppStartParam) {
      if (tgWebAppStartParam.startsWith('ref_')) {
        return tgWebAppStartParam;
      }
      return `ref_${tgWebAppStartParam}`;
    }
    
    // Check for custom ref parameter
    const refParam = urlParams.get('ref');
    if (refParam) {
      if (refParam.startsWith('ref_')) {
        return refParam;
      }
      return `ref_${refParam}`;
    }
    
    // No referral code found
    return null;
  } catch (error) {
    console.error('Error extracting referral code:', error);
    return null;
  }
};

/**
 * Validates a referral code with the backend
 */
export const validateReferralCode = async (code: string): Promise<{
  valid: boolean;
  referrerId?: string;
  message?: string;
}> => {
  if (!code) return { valid: false, message: 'No referral code provided' };
  
  try {
    // Use fetch API which works on both client and server
    const response = await fetch(`/api/check-referral?code=${encodeURIComponent(code)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      return {
        valid: false,
        message: errorData.error || 'Failed to validate referral code',
      };
    }
    
    const data = await response.json();
    return {
      valid: true,
      referrerId: data.referrerId,
      message: data.message,
    };
  } catch (error) {
    console.error('Error validating referral code:', error);
    return {
      valid: false,
      message: 'An error occurred while validating the referral code',
    };
  }
};

/**
 * Tracks a successful referral signup
 */
export const trackReferral = async (referrerId: string, userId: string): Promise<boolean> => {
  if (!referrerId || !userId) return false;
  
  try {
    // Use fetch API which works on both client and server
    const response = await fetch('/api/track-referral', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        referrerId,
        userId,
      }),
    });
    
    if (!response.ok) {
      console.error('Error tracking referral, status:', response.status);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error tracking referral:', error);
    return false;
  }
};

/**
 * Gets a user's referral link
 */
export const getUserReferralLink = (userId: string, baseUrl?: string): string => {
  if (!userId) return typeof window !== 'undefined' ? window.location.origin : (baseUrl || '');
  
  // Create a direct web link
  const origin = typeof window !== 'undefined' ? window.location.origin : (baseUrl || '');
  const webLink = `${origin}?ref=${userId}`;
  
  // If in Telegram and on client-side, try to create a Telegram app link
  if (typeof window !== 'undefined' && WebApp && WebApp.initData) {
    try {
      const botUsername = WebApp.initDataUnsafe?.user?.username || 'AlgoTradeBot';
      return `https://t.me/${botUsername}?start=ref_${userId}`;
    } catch (error) {
      // Fallback to web link
      return webLink;
    }
  }
  
  return webLink;
};

/**
 * Save referral info to local storage for later use
 */
export const saveReferralInfo = (referrerId: string): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('referrerId', referrerId);
  } catch (error) {
    console.error('Error saving referral info:', error);
  }
};

/**
 * Get saved referral info from local storage
 */
export const getSavedReferralInfo = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    return localStorage.getItem('referrerId');
  } catch (error) {
    console.error('Error getting saved referral info:', error);
    return null;
  }
}; 