import { Telegraf, Context, Markup } from 'telegraf';
import { Message } from 'telegraf/typings/core/types/typegram';
import { User } from '../models/user.model';
import { generateReferralCode } from './helpers';
import { TELEGRAM_TOKEN } from '../config/config';
import mongoose from 'mongoose';
import { InlineKeyboardButton } from 'telegraf/typings/core/types/typegram';

// Initialize bot
// const bot = new Telegraf(TELEGRAM_TOKEN);
const bot = new Telegraf("**********************************************");

// Web app URL
const WEB_APP_URL = 'https://master.dsysuwkq4oow9.amplifyapp.com/'; // Replace with your actual web app URL

// Middleware to log all interactions
bot.use(async (ctx, next) => {
  console.log(`👤 User ${ctx.from?.id} (${ctx.from?.username || 'Unknown'}) sent: ${
    ctx.message ? ('text' in ctx.message ? ctx.message.text : 'non-text message') : 
    // @ts-ignore
    ctx.callbackQuery ? ctx.callbackQuery.data : 'unknown action'
  }`);
  await next();
});

// Start command with user registration and referral tracking
bot.command('start', async (ctx): Promise<void> => {
  try {
    const telegramId = ctx.from?.id.toString();
    console.log(telegramId);
    if (!telegramId) {
      await ctx.reply('Error identifying user. Please try again.');
      return;
    }

    // Check if this is a referral link
    const startPayload = ctx.message?.text.split(' ')[1];
    let referrer = null;

    // Find or create user
    let user = await User.findOne({ telegramId });
    const isNewUser = !user;

    if (isNewUser) {
      // Register new user
      // Create new user from telegram data
      const newUser = new User({
        telegramId: ctx.from.id.toString(),
        telegramUsername: ctx.from.username,
        telegramName: `${ctx.from.first_name || ''} ${ctx.from.last_name || ''}`.trim(),
        telegramLanguage: ctx.from.language_code,
        telegramChatId: ctx.chat?.id.toString(),
        telegramChatType: ctx.chat?.type,
        displayName: ctx.from.username || ctx.from.first_name || 'User',
        wallet: {
          balance: {
            sol: 0,
            flow: 0,
            bonk: 0,
          }
        },
        referrals: [],
        referralStats: {
          totalReferrals: 0,
          rewardsSol: 0,
          rewardsFlow: 0,
          rewardsBonk: 0
        },
        redeemedReferralTasks: [],
        dailyStreak: {
          currentStreak: 0,
          highestStreak: 0,
          lastClaimDate: null,
          totalDaysVisited: 0,
          streakHistory: [],
          weeklyProgress: 0,
          totalRewardsEarned: 0
        },
        claimsSinceWithdrawal: 0
      });
      
      user = await newUser.save();
      
      // Process referral if present and valid
      if (startPayload) {
        try {
          referrer = await User.findOne({ telegramId: startPayload });
          if (referrer && referrer.telegramId !== telegramId) {
            // Add this user to referrer's referrals
            await referrer.addReferral(user._id.toString());
            await referrer.save();
            
            // Set referredBy for the new user
            user.referredBy = referrer._id;
            await user.save();
            
          
           await ctx.telegram.sendMessage(startPayload, `Woohoo! 👥 ${user.displayName} just joined your referral squad! 🎉`, {
             reply_markup: {
               inline_keyboard: [
                 [{ text: 'Close', callback_data: 'close' }]
               ]
             }
           });
          }
           console.log(`User ${telegramId} was referred by ${startPayload}`);
        } catch (error) {
          console.error('Error processing referral:', error);
        }
      }
    }

    // Create welcome message
    const welcomeMessage = `🚀 *Welcome to Flow Trade!*

Join our decentralized investment platform powered by Solana blockchain. Earn with our zapping algorithm and build your referral network!

📊 *Available Features:*
• Zapping Algorithm: 1% daily earnings (min 0.1 SOL)
• Referral System: Earn from your network
• $FLOW Token: Participate in our token economy

🔍 *What would you like to do?*`;

    // Create inline keyboard
    const keyboard = Markup.inlineKeyboard([
      [Markup.button.webApp('🚀 Start Zapping', WEB_APP_URL)],
      [Markup.button.callback('👥 My Referrals', 'show_referrals')],
      [Markup.button.callback('ℹ️ About Flow Trade', 'about')],
      [Markup.button.callback('❌ Close', 'close')]
    ]);

    // Send welcome message with inline keyboard
    await ctx.replyWithMarkdown(welcomeMessage, keyboard);

  } catch (error) {
    console.error('Error in start command:', error);
    await ctx.reply('Sorry, an error occurred. Please try again later.');
  }
});

// Referral command
bot.command('referral', async (ctx) => {
  await showReferrals(ctx);
});

// Callback for showing referrals
bot.action('show_referrals', async (ctx) => {
  await ctx.answerCbQuery();
  await showReferrals(ctx);
});

// About Flow Trade callback
bot.action('about', async (ctx) => {
  await ctx.answerCbQuery();
  
  const aboutMessage = `*About Flow Trade* 🌊

Flow Trade is a decentralized investment platform built on Solana blockchain.

*Our Features:*
• Advanced zapping algorithm for daily earnings
• Multi-level referral system
• $FLOW token economy
• Secure and transparent operations

Join our community and start earning today!`;
  
  await ctx.editMessageText(aboutMessage, {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [Markup.button.webApp('🚀 Start Zapping', WEB_APP_URL)],
        [Markup.button.callback('👥 My Referrals', 'show_referrals')],
        [Markup.button.callback('⬅️ Back', 'back_to_start')],
        [Markup.button.callback('❌ Close', 'close')]
      ]
    }
  });
});

// Back to start menu callback
bot.action('back_to_start', async (ctx) => {
  await ctx.answerCbQuery();
  
  const welcomeMessage = `🚀 *Welcome to Flow Trade!*

Join our decentralized investment platform powered by Solana blockchain. Earn with our zapping algorithm and build your referral network!

📊 *Available Features:*
• Zapping Algorithm: 1% daily earnings (min 0.1 SOL)
• Referral System: Earn from your network
• $FLOW Token: Participate in our token economy

🔍 *What would you like to do?*`;

  await ctx.editMessageText(welcomeMessage, {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [Markup.button.webApp('🚀 Start Zapping', WEB_APP_URL)],
        [Markup.button.callback('👥 My Referrals', 'show_referrals')],
        [Markup.button.callback('ℹ️ About Flow Trade', 'about')],
        [Markup.button.callback('❌ Close', 'close')]
      ]
    }
  });
});

// Close button callback
bot.action('close', async (ctx) => {
  await ctx.answerCbQuery();
  
  // Delete the message with the inline keyboard
  try {
    await ctx.deleteMessage();
  } catch (error) {
    console.error('Error deleting message:', error);
  }
});

// Helper function to collect all referrals with their levels
// @ts-ignore
interface ReferralData {
  user: {
    telegramUsername?: string;
    telegramName?: string;
    _id: string;
  };
  level: number;
  joinedAt: Date;
  earnedSol: number;
  earnedFlow: number;
  earnedBonk: number;
}

interface LevelCounts {
  [key: number]: number;
}

interface ReferralsByLevel {
  [key: number]: ReferralData[];
}

async function collectAllReferrals(userId: string, currentLevel: number = 1, maxLevel: number = 5): Promise<ReferralData[]> {
  if (currentLevel > maxLevel) return [];
  
  const user = await User.findById(userId).populate('referrals.user', 'telegramId telegramUsername telegramName createdAt');
  if (!user || !user.referrals || user.referrals.length === 0) return [];
  
  const allReferrals: ReferralData[] = [];
  
  // Add current level referrals
  for (const referral of user.referrals) {
    if (referral.user) {
      allReferrals.push({
        // @ts-ignore
        user: referral.user,
        level: currentLevel,
        joinedAt: referral.joinedAt,
        earnedSol: referral.earnedSol || 0,
        earnedFlow: referral.earnedFlow || 0,
        earnedBonk: referral.earnedBonk || 0
      });
      
      // Recursively get sub-referrals for next levels
      if (currentLevel < maxLevel) {
        // @ts-ignore
        const subReferrals = await collectAllReferrals(referral.user._id, currentLevel + 1, maxLevel);
        allReferrals.push(...subReferrals);
      }
    }
  }
  
  return allReferrals;
}

// Updated showReferrals function
async function showReferrals(ctx: any): Promise<void> {
  try {
    const telegramId = ctx.from?.id.toString();

    if (!telegramId) {
      await ctx.reply('Error identifying user. Please try again.');
      return;
    }

    // Find user with populated referredBy
    const user = await User.findOne({ telegramId })
      .populate('referredBy', 'telegramId telegramUsername telegramName createdAt');
    
    if (!user) {
      await ctx.reply('User not found. Please use /start to register.');
      return;
    }

    // Generate referral link
    const referralLink = `https://t.me/FlowTrade_bot?start=${telegramId}`;
    
    // Check if user was referred by someone
    let referredByInfo = '';
    if (user.referredBy) {
      const referrer = user.referredBy as any;
      const referrerName = referrer.telegramUsername || referrer.telegramName || 'Unknown';
      const joinDate = user.createdAt ? new Date(user.createdAt).toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' }) : 'unknown';
      referredByInfo = `\n\n🔗 <b>You were invited by:</b> ${referrerName} (joined: ${joinDate})`;
    }
    
    // Collect all referrals across all levels
    // @ts-ignore
    const allReferrals: ReferralData[] = await collectAllReferrals(user._id, 1, 5);
    
    // Prepare referrals list
    let referralsList = '';
    
    if (allReferrals.length === 0) {
      referralsList = 'You have no referrals yet. Share your referral link to start earning!';
    } else {
      // Sort by joinedAt date (newest first) and take only recent 10
      const sortedReferrals = allReferrals
        .sort((a, b) => new Date(b.joinedAt).getTime() - new Date(a.joinedAt).getTime())
        .slice(0, 10);
      
      referralsList = `<b>Your Referrals (${allReferrals.length} total):</b>\n\n`;
      
      // Display referrals with level after name
      sortedReferrals.forEach((ref, index) => {
        const username = ref.user.telegramUsername || ref.user.telegramName || 'Unknown';
        const levelEmoji = ['1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣'][ref.level - 1];
        referralsList += `${index + 1}. ${username} ${levelEmoji}\n`;
      });
      
      if (allReferrals.length > 10) {
        referralsList += `\n<i>Showing most recent 10 of ${allReferrals.length} total referrals</i>\n`;
      }
    }

    // Calculate total referrals by level for commission info
    const levelCounts: LevelCounts = {};
    allReferrals.forEach(ref => {
      levelCounts[ref.level] = (levelCounts[ref.level] || 0) + 1;
    });

    let levelSummary = '';
    for (let level = 1; level <= 5; level++) {
      if (levelCounts[level]) {
        const commission = level === 1 ? '5%' : level === 2 ? '2%' : '1%';
        levelSummary += `Level ${level}: ${levelCounts[level]} users (${commission})\n`;
      }
    }

    // Create referral message
    const referralMessage = `👥 <b>Referral Program</b>

${referralsList}${referredByInfo}

💰 <b>Your Referral Link:</b>
${referralLink}

📊 <b>Referral Summary:</b>
${levelSummary || 'No referrals yet'}

💎 <b>Commission Structure:</b>
  • Level 1: 5% of their investment
  • Level 2: 2% of their investment  
  • Levels 3-5: 1% of their investment each

💡 <b>Note:</b> To earn referral rewards, you must have an active investment in any Flow trading plan
`;

    // Create inline keyboard
    const keyboard = Markup.inlineKeyboard([
      [Markup.button.webApp('🚀 Start Zapping', WEB_APP_URL)],
      [Markup.button.callback('⬅️ Back to Menu', 'back_to_start')],
      [Markup.button.callback('❌ Close', 'close')]
    ]);

    // Send or edit message based on context
    if (ctx.callbackQuery) {
      await ctx.editMessageText(referralMessage, {
        parse_mode: 'HTML',
        reply_markup: {
          inline_keyboard: keyboard.reply_markup.inline_keyboard
        }
      });
    } else {
      await ctx.reply(referralMessage, {
        parse_mode: 'HTML',
        ...keyboard
      });
    }

  } catch (error) {
    console.error('Error showing referrals:', error);
    await ctx.reply('Sorry, an error occurred. Please try again later.');
  }
}

export default bot;