globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./components/initial/AnimatedGrid.tsx":{"*":{"id":"(ssr)/./components/initial/AnimatedGrid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/ClientShell.tsx":{"*":{"id":"(ssr)/./components/initial/ClientShell.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/NavigationWrapper.tsx":{"*":{"id":"(ssr)/./components/initial/NavigationWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/TickerBar.tsx":{"*":{"id":"(ssr)/./components/initial/TickerBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/ToasterProvider.tsx":{"*":{"id":"(ssr)/./components/initial/ToasterProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/AuthContext.tsx":{"*":{"id":"(ssr)/./context/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/LanguageContext.tsx":{"*":{"id":"(ssr)/./context/LanguageContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/SolanaWalletContext.tsx":{"*":{"id":"(ssr)/./context/SolanaWalletContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/wallet/Wallet.tsx":{"*":{"id":"(ssr)/./components/wallet/Wallet.tsx","name":"*","chunks":[],"async":true}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/console/ConsoleSection.tsx":{"*":{"id":"(ssr)/./components/console/ConsoleSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/missions/Missions.tsx":{"*":{"id":"(ssr)/./components/missions/Missions.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/refer/Refer.tsx":{"*":{"id":"(ssr)/./components/refer/Refer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/news/NewsSection.tsx":{"*":{"id":"(ssr)/./components/news/NewsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/tutorial/Tutorial.tsx":{"*":{"id":"(ssr)/./components/tutorial/Tutorial.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/airdrop/Airdrop.tsx":{"*":{"id":"(ssr)/./components/airdrop/Airdrop.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/faqs/FAQs.tsx":{"*":{"id":"(ssr)/./components/faqs/FAQs.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\initial\\AnimatedGrid.tsx":{"id":"(app-pages-browser)/./components/initial/AnimatedGrid.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\initial\\ClientShell.tsx":{"id":"(app-pages-browser)/./components/initial/ClientShell.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\initial\\NavigationWrapper.tsx":{"id":"(app-pages-browser)/./components/initial/NavigationWrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\initial\\TickerBar.tsx":{"id":"(app-pages-browser)/./components/initial/TickerBar.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\initial\\ToasterProvider.tsx":{"id":"(app-pages-browser)/./components/initial/ToasterProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\context\\AuthContext.tsx":{"id":"(app-pages-browser)/./context/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\context\\LanguageContext.tsx":{"id":"(app-pages-browser)/./context/LanguageContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\context\\SolanaWalletContext.tsx":{"id":"(app-pages-browser)/./context/SolanaWalletContext.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Syne\",\"arguments\":[{\"variable\":\"--font-syne\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"syne\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Syne\",\"arguments\":[{\"variable\":\"--font-syne\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"syne\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\wallet\\Wallet.tsx":{"id":"(app-pages-browser)/./components/wallet/Wallet.tsx","name":"*","chunks":[],"async":true},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\console\\ConsoleSection.tsx":{"id":"(app-pages-browser)/./components/console/ConsoleSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\missions\\Missions.tsx":{"id":"(app-pages-browser)/./components/missions/Missions.tsx","name":"*","chunks":[],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\refer\\Refer.tsx":{"id":"(app-pages-browser)/./components/refer/Refer.tsx","name":"*","chunks":[],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\news\\NewsSection.tsx":{"id":"(app-pages-browser)/./components/news/NewsSection.tsx","name":"*","chunks":[],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\tutorial\\Tutorial.tsx":{"id":"(app-pages-browser)/./components/tutorial/Tutorial.tsx","name":"*","chunks":[],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\airdrop\\Airdrop.tsx":{"id":"(app-pages-browser)/./components/airdrop/Airdrop.tsx","name":"*","chunks":[],"async":false},"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\components\\faqs\\FAQs.tsx":{"id":"(app-pages-browser)/./components/faqs/FAQs.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\":[],"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\app\\not-found":[],"E:\\Development\\May Session 2025\\flow-trade-meme\\frontend\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/AnimatedGrid.tsx":{"*":{"id":"(rsc)/./components/initial/AnimatedGrid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/ClientShell.tsx":{"*":{"id":"(rsc)/./components/initial/ClientShell.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/NavigationWrapper.tsx":{"*":{"id":"(rsc)/./components/initial/NavigationWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/TickerBar.tsx":{"*":{"id":"(rsc)/./components/initial/TickerBar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/initial/ToasterProvider.tsx":{"*":{"id":"(rsc)/./components/initial/ToasterProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/AuthContext.tsx":{"*":{"id":"(rsc)/./context/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/LanguageContext.tsx":{"*":{"id":"(rsc)/./context/LanguageContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./context/SolanaWalletContext.tsx":{"*":{"id":"(rsc)/./context/SolanaWalletContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/wallet/Wallet.tsx":{"*":{"id":"(rsc)/./components/wallet/Wallet.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/console/ConsoleSection.tsx":{"*":{"id":"(rsc)/./components/console/ConsoleSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/missions/Missions.tsx":{"*":{"id":"(rsc)/./components/missions/Missions.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/refer/Refer.tsx":{"*":{"id":"(rsc)/./components/refer/Refer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/news/NewsSection.tsx":{"*":{"id":"(rsc)/./components/news/NewsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/tutorial/Tutorial.tsx":{"*":{"id":"(rsc)/./components/tutorial/Tutorial.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/airdrop/Airdrop.tsx":{"*":{"id":"(rsc)/./components/airdrop/Airdrop.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/faqs/FAQs.tsx":{"*":{"id":"(rsc)/./components/faqs/FAQs.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}