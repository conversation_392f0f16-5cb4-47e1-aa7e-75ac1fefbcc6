import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment, InvestmentStatus } from '@/libs/model/investment.schema';
import { User } from '@/libs/model/user.schema';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';
import { TIME_CONFIG } from '@/libs/config';

// Validation response interface
interface ClaimValidationResponse {
  success: boolean;
  error?: string;
  data?: {
    isValid: boolean;
    investment?: any;
    validationDetails: {
      investmentExists: boolean;
      investmentActive: boolean;
      cooldownMet: boolean;
      hasZapProgress: boolean;
      canClaim: boolean;
      timeUntilNextClaim?: number;
      zapProgress?: number;
      roiPercentage?: number;
    };
    claimData?: {
      claimAmount: number;
      currentEarned: number;
      totalAfterClaim: number;
      roiAfterClaim: number;
    };
  };
}

export async function POST(req: NextRequest): Promise<NextResponse<ClaimValidationResponse>> {
  try {
    // Get the user ID from cookies
    const userId = "68380e3e3b44aa85b374c1f0";
    
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Parse request body (if any additional params needed)
    const body = await req.json().catch(() => ({}));
    
    // Initialize validation results
    let isValid = true;
    let errors: string[] = [];
    const validationDetails = {
      investmentExists: false,
      investmentActive: false,
      cooldownMet: false,
      hasZapProgress: false,
      canClaim: false,
      timeUntilNextClaim: undefined as number | undefined,
      zapProgress: undefined as number | undefined,
      roiPercentage: undefined as number | undefined
    };
    
    // Find the user's active investment
    const investment = await Investment.findOne({
      user: new Types.ObjectId(userId),
      status: InvestmentStatus.ACTIVE
    });
    
    if (!investment) {
      errors.push('No active investment found');
      isValid = false;
    } else {
      validationDetails.investmentExists = true;
      validationDetails.investmentActive = true;
    }
    
    let claimData = undefined;
    
    if (investment) {
      const now = new Date();
      
      // Calculate time elapsed since last claim or investment creation
      const lastClaimTime = investment.lastClaimTime || investment.createdAt || investment.startDate;
      const elapsedTime = now.getTime() - new Date(lastClaimTime).getTime();
      const elapsedSeconds = elapsedTime / 1000;
      
      // Check cooldown period (36 hours = 129,600 seconds)
      const cooldownRequired = TIME_CONFIG.ROI_ACCUMULATION_SECONDS; // 129,600 seconds
      const cooldownMet = elapsedSeconds >= cooldownRequired;
      
      validationDetails.cooldownMet = cooldownMet;
      validationDetails.timeUntilNextClaim = cooldownMet ? 0 : Math.ceil(cooldownRequired - elapsedSeconds);
      
      if (!cooldownMet) {
        const remainingTime = Math.ceil((cooldownRequired - elapsedSeconds) / 3600); // Convert to hours
        errors.push(`Claim cooldown not met. ${remainingTime} hours remaining`);
        isValid = false;
      }
      
      // Calculate current zap progress
      const progressRate = TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE / TIME_CONFIG.ROI_ACCUMULATION_SECONDS; // Per second
      let currentProgress = Math.min(elapsedSeconds * progressRate, TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE);
      
      // Ensure progress doesn't exceed claim percentage
      currentProgress = Math.min(currentProgress, TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE);
      
      validationDetails.zapProgress = currentProgress;
      validationDetails.hasZapProgress = currentProgress > 0;
      
      // Check if user can claim (reached threshold)
      const canClaim = currentProgress >= TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE;
      validationDetails.canClaim = canClaim;
      
      if (!canClaim && cooldownMet) {
        errors.push('Zap progress not sufficient for claim');
        isValid = false;
      }
      
      // Calculate ROI percentage
      const currentTotal = investment.earnedAmount + investment.referralBonus;
      const roiPercentage = (currentTotal / investment.initialAmount) * 100;
      validationDetails.roiPercentage = roiPercentage;
      
      // Check if ROI cap reached (250%)
      if (roiPercentage >= TIME_CONFIG.TOTAL_ROI_CAP * 100) {
        errors.push('Maximum ROI cap (250%) reached');
        isValid = false;
      }
      
      // Calculate claim amounts if valid
      if (isValid) {
        const claimAmount = investment.initialAmount * (TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE / 100);
        const maxEarnings = investment.initialAmount * TIME_CONFIG.TOTAL_ROI_CAP;
        const currentEarnings = investment.earnedAmount;
        
        // Adjust claim if it would exceed ROI cap
        const adjustedClaimAmount = Math.min(claimAmount, Math.max(0, maxEarnings - currentEarnings));
        
        claimData = {
          claimAmount: adjustedClaimAmount,
          currentEarned: currentEarnings,
          totalAfterClaim: currentEarnings + adjustedClaimAmount,
          roiAfterClaim: ((currentEarnings + adjustedClaimAmount) / investment.initialAmount) * 100
        };
      }
    }
    
    // Return validation results
    if (isValid) {
      return NextResponse.json({
        success: true,
        data: {
          isValid: true,
          investment: investment ? {
            id: investment._id,
            initialAmount: investment.initialAmount,
            earnedAmount: investment.earnedAmount,
            currentZapProgress: validationDetails.zapProgress,
            lastClaimTime: investment.lastClaimTime,
            roiPercentage: validationDetails.roiPercentage
          } : undefined,
          validationDetails,
          claimData
        }
      });
    } else {
      return NextResponse.json({
        success: true,
        data: {
          isValid: false,
          investment: investment ? {
            id: investment._id,
            initialAmount: investment.initialAmount,
            earnedAmount: investment.earnedAmount,
            currentZapProgress: validationDetails.zapProgress,
            lastClaimTime: investment.lastClaimTime,
            roiPercentage: validationDetails.roiPercentage
          } : undefined,
          validationDetails
        },
        error: errors.join('; ')
      });
    }
    
  } catch (error: any) {
    console.error('Error validating claim:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to validate claim'
    }, { status: 500 });
  }
} 