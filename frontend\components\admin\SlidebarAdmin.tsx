"use client"
import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  LayoutDashboard, 
  Newspaper, 
  Gift, 
  Users, 
  Wallet, 
  Settings, 
  LogOut, 
  ChevronRight, 
  Menu, 
  X,
  Bell,
  Search
} from 'lucide-react'
import Image from 'next/image'

type NavItem = {
  name: string;
  href: string;
  icon: React.ReactNode;
}

const SlidebarAdmin = () => {
  const [collapsed, setCollapsed] = useState(false)
  const pathname = usePathname()
  
  const mainNavItems: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: <LayoutDashboard className="w-5 h-5" />
    },
    {
      name: 'News',
      href: '/admin/news',
      icon: <Newspaper className="w-5 h-5" />
    },
    {
      name: 'Missions',
      href: '/admin/missions',
      icon: <Gift className="w-5 h-5" />
    },
    {
      name: 'Content Creators',
      href: '/admin/content',
      icon: <Users className="w-5 h-5" />
    },
    {
      name: 'Withdrawals',
      href: '/admin/withdrawals',
      icon: <Wallet className="w-5 h-5" />
    },
  ]
  
  const secondaryNavItems: NavItem[] = [
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: <Settings className="w-5 h-5" />
    },
    {
      name: 'Logout',
      href: '/admin/logout',
      icon: <LogOut className="w-5 h-5" />
    }
  ]
  
  const NavItem = ({ item }: { item: NavItem }) => {
    const isActive = pathname === item.href
    
    return (
      <Link
        href={item.href}
        className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
          isActive
            ? 'bg-blue-50 text-blue-700'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
      >
        <div className={isActive ? 'text-blue-600' : 'text-gray-500'}>
          {item.icon}
        </div>
        <span className={collapsed ? 'hidden' : 'block'}>
          {item.name}
        </span>
        {isActive && !collapsed && (
          <ChevronRight className="w-4 h-4 ml-auto text-blue-600" />
        )}
      </Link>
    )
  }
  
  return (
    <div className={`bg-white border-r border-gray-200 h-screen flex flex-col transition-all duration-300 ${
      collapsed ? 'w-[70px]' : 'w-[240px]'
    }`}>
      {/* Header with Logo */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="flex-shrink-0 h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold">
            A
          </div>
          {!collapsed && (
            <span className="text-gray-800 font-semibold">AlgoTrade</span>
          )}
        </div>
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="text-gray-500 hover:text-gray-700"
        >
          {collapsed ? <Menu size={18} /> : <X size={18} />}
        </button>
      </div>
      
      {/* User Profile */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="relative h-9 w-9 rounded-full bg-gray-200 flex-shrink-0 overflow-hidden">
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-gray-600 font-medium">AD</span>
            </div>
          </div>
          {!collapsed && (
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-gray-800 truncate">
                Admin User
              </h3>
              <p className="text-xs text-gray-500 truncate">
                <EMAIL>
              </p>
            </div>
          )}
        </div>
      </div>
      
      {/* Search (only when expanded) */}
      {!collapsed && (
        <div className="px-3 py-2">
          <div className="relative rounded-md shadow-sm">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full rounded-md border-0 py-1.5 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 text-sm"
              placeholder="Search..."
            />
          </div>
        </div>
      )}
      
      {/* Main Navigation */}
      <div className="flex-1 overflow-y-auto px-3 py-2">
        {/* Main Nav */}
        <nav className="space-y-1 mb-6">
          {mainNavItems.map((item, i) => (
            <NavItem key={i} item={item} />
          ))}
        </nav>
        
        {/* Secondary Nav */}
        <div className={collapsed ? '' : 'border-t border-gray-200 pt-2'}>
          <nav className="space-y-1 mt-2">
            {secondaryNavItems.map((item, i) => (
              <NavItem key={i} item={item} />
            ))}
          </nav>
        </div>
      </div>
      
      {/* Footer with Version */}
      {!collapsed && (
        <div className="p-3 border-t border-gray-200 text-xs text-gray-500">
          <div className="flex items-center justify-between">
            <span>v1.0.0</span>
            <div className="relative">
              <Bell className="h-4 w-4 text-gray-400 hover:text-gray-700 cursor-pointer" />
              <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-blue-600 border-2 border-white"></span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SlidebarAdmin