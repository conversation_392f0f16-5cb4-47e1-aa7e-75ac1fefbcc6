
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Zap } from 'lucide-react';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 animate-slide-down ${
        isScrolled 
          ? 'bg-black/70 backdrop-blur-xl border-b border-green-500/10 shadow-2xl shadow-green-500/5' 
          : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-3 animate-fade-in-left">
            <div className="relative group">
              <Zap className="h-8 w-8 text-green-400 transition-all duration-300 group-hover:scale-110 animate-rotate-in" />
            </div>
            <span className="text-2xl font-bold text-white font-syne">
              FlowTrade
            </span>
          </div>
          
          <nav className="hidden md:flex items-center space-x-8 animate-fade-in-right animation-delay-300">
            <a href="#features" className="text-gray-300 hover:text-green-400 transition-all duration-300 font-medium relative group">
              Features
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-green-400 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="#zapping" className="text-gray-300 hover:text-green-400 transition-all duration-300 font-medium relative group">
              Zapping
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-green-400 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="#referrals" className="text-gray-300 hover:text-green-400 transition-all duration-300 font-medium relative group">
              Referrals
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-green-400 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="#flow-points" className="text-gray-300 hover:text-green-400 transition-all duration-300 font-medium relative group">
              Flow Points
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-green-400 transition-all duration-300 group-hover:w-full"></span>
            </a>
          </nav>

          <Button onClick={() => window.location.href = 'https://t.me/FlowTrade_bot'} className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-400 hover:to-emerald-500 text-black font-semibold border-0 shadow-xl shadow-green-500/20 hover:shadow-green-500/40 transition-all duration-300 hover:scale-105 animate-gradient-x animate-scale-in animation-delay-500">
            Start Zapping
          </Button>
        </div>
      </div>

      <style>{`
        .animation-delay-300 { animation-delay: 0.3s; }
        .animation-delay-500 { animation-delay: 0.5s; }
      `}</style>
    </header>
  );
};

export default Header;
