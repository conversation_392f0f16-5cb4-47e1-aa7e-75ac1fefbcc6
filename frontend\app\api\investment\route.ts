import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment } from '@/libs/model/investment.schema';
import { cookies } from 'next/headers';
import User from '@/libs/model/user.schema';

// GET handler to get all investments (admin only) or current user's investment
export async function GET(req: NextRequest) {
  try {
    // Get user ID from cookies
    // const userId = (await cookies()).get('userId')?.value;
    const userId = "68380e3e3b44aa85b374c1f0"; // Hardcoded for testing
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();

    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    
    // Find active investment for user
    const investment = await Investment.findOne({
      user: userId,
      status: 'active'
    });
    
    if (!investment) {
      return NextResponse.json({
        success: true,
        data: null,
        message: 'No active investment found'
      });
    }
    
    // Calculate current Zap progress
    const currentProgress = investment.calculateCurrentZapProgress();
    
    // Save the calculation results
    await investment.save();
    
    // Prepare response with all needed data
    const responseData = {
      _id: investment._id,
      user: investment.user,
      initialAmount: investment.initialAmount,
      earnedAmount: investment.earnedAmount,
      withdrawnAmount: investment.withdrawnAmount,
      currency: investment.currency,
      startDate: investment.startDate,
      status: investment.status,
      claimsSinceWithdrawal: user.claimsSinceWithdrawal,
      lastWithdrawalTime: investment.lastWithdrawalTime,
      lastCalculationTime: investment.lastCalculationTime,
      currentZapProgress: investment.currentZapProgress,
      lastClaimTime: investment.lastClaimTime,
      claims: investment.claims,
      referralBonus: investment.referralBonus,
      createdAt: investment.createdAt,
      updatedAt: investment.updatedAt,
      
      // Added calculated fields
      currentValue: investment.initialAmount + investment.earnedAmount - investment.withdrawnAmount,
      canClaimZappedRewards: investment.canClaimZappedRewards(),
      canWithdraw: investment.canWithdraw(),
      calculatedZapProgress: currentProgress,
      roiPercentCompleted: Math.min(100, (investment.earnedAmount / (investment.initialAmount * 1.5)) * 100),
      
      // Add metrics for display
      zapMetrics: {
        initialInvestment: investment.initialAmount,
        totalEarned: investment.earnedAmount,
        currentBalance: investment.initialAmount + investment.earnedAmount - investment.withdrawnAmount,
        totalWithdrawn: investment.withdrawnAmount,
        claimsSinceWithdrawal: user.claimsSinceWithdrawal,
        claimsRemainingForWithdrawal: Math.max(0, 1 - user.claimsSinceWithdrawal), // 1 for testing, would be 5 in production
        referralBonus: investment.referralBonus,
        maxPotentialEarnings: investment.initialAmount * 2.5,
        progressTowardMaxROI: Math.min(100, (investment.earnedAmount / (investment.initialAmount * 1.5)) * 100)
      }
    };
    
    return NextResponse.json({
      success: true,
      data: responseData,
      message: 'Investment found'
    });
    
  } catch (error: any) {
    console.error('Error fetching investment:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to fetch investment' },
      { status: 500 }
    );
  }
} 