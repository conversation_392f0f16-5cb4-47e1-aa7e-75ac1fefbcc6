import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Mission } from '@/libs/model/missions.schema';
import { User } from '@/libs/model/user.schema';
import { MissionProgressStatus, RewardType } from '@/types/mission';
import { cookies } from 'next/headers';
import { headers } from 'next/headers';

export async function POST(req: NextRequest) {
  try {
    const userId = (await cookies()).get('userId')?.value;

    const telegramId = (await headers()).get('x-telegram-id') || '';
    
    if (!userId && !telegramId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { missionId } = body;
    
    if (!missionId) {
      return NextResponse.json({ error: 'Mission ID is required' }, { status: 400 });
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find mission
    const mission = await Mission.findById(missionId);
    if (!mission) {
      return NextResponse.json({ error: 'Mission not found' }, { status: 404 });
    }
    
    // Check if mission is active
    if (mission.status !== 'active') {
      return NextResponse.json({ error: 'Mission is not active' }, { status: 400 });
    }
    
    // Check if mission already redeemed by user
    if (mission.isRedeemedByUser(userId)) {
      return NextResponse.json({ error: 'Mission already redeemed' }, { status: 400 });
    }
    
    // Check if mission has been started by the user
    if (!mission.isStartedByUser(userId)) {
      return NextResponse.json({ error: 'Mission not started yet' }, { status: 400 });
    }
    
    // Get user progress
    const progress = mission.getUserProgress(userId);
    
    // Check if mission is still in progress 
    if (progress.status !== MissionProgressStatus.IN_PROGRESS) {
      return NextResponse.json({ 
        error: 'Mission is not in progress', 
        progress 
      }, { status: 400 });
    }
    
    // Check if estimated completion time has passed
    const now = new Date();
    if (now < new Date(progress.estimatedCompletionAt)) {
      const remainingTime = new Date(progress.estimatedCompletionAt).getTime() - now.getTime();
      const remainingMinutes = Math.ceil(remainingTime / (1000 * 60));
      
      return NextResponse.json({ 
        error: `Mission not completed yet. Please wait ${remainingMinutes} more minute(s).`,
        progress,
        remainingMinutes
      }, { status: 400 });
    }
    
    // Find user to update balance
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Update progress status to COMPLETED
    const progressIndex = mission.startedBy.findIndex((p: any) => 
      p.userId.toString() === userId?.toString()
    );
    
    if (progressIndex >= 0) {
      mission.startedBy[progressIndex].status = MissionProgressStatus.COMPLETED;
    }
    
    mission.addRedemption(userId);
    await mission.save();
    
    // Award the user with the reward
    if (mission.reward.type === RewardType.SOL) {
      user.wallet.balance.sol += mission.reward.amount;
    } else if (mission.reward.type === RewardType.FLOW) {
      user.wallet.balance.flow += mission.reward.amount;
    }
    
    if (!user.completedMissions.includes(mission._id)) {
      user.completedMissions.push(mission._id);
    }
    
    await user.save();
    
    return NextResponse.json({
      message: 'Mission completed successfully',
      reward: {
        type: mission.reward.type,
        amount: mission.reward.amount
      }
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('Error completing mission:', error);
    return NextResponse.json({ error: 'Failed to complete mission' }, { status: 500 });
  }
} 