
import React from 'react';
import { Zap, Twitter, MessageCircle, Github } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-950/50 backdrop-blur-xl border-t border-green-500/10 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Zap className="h-8 w-8 text-green-400" />
              <span className="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent font-syne">
                FlowTrade
              </span>
            </div>
            <p className="text-gray-400 text-sm">
              AI-powered Solana trading platform delivering passive income through automated meme coin zapping.
            </p>
          </div>

          {/* Product */}
          <div>
            <h3 className="text-white font-semibold mb-4 font-syne">Product</h3>
            <ul className="space-y-2 text-gray-400 text-sm">
              <li><a href="#features" className="hover:text-green-400 transition-colors">Features</a></li>
              <li><a href="#zapping" className="hover:text-green-400 transition-colors">How Zapping Works</a></li>
              <li><a href="#referrals" className="hover:text-green-400 transition-colors">Referral System</a></li>
              <li><a href="#flow-points" className="hover:text-green-400 transition-colors">Flow Points</a></li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-white font-semibold mb-4 font-syne">Resources</h3>
            <ul className="space-y-2 text-gray-400 text-sm">
              <li><a href="#" className="hover:text-green-400 transition-colors">Documentation</a></li>
              <li><a href="#" className="hover:text-green-400 transition-colors">Whitepaper</a></li>
              <li><a href="#" className="hover:text-green-400 transition-colors">FAQ</a></li>
              <li><a href="#" className="hover:text-green-400 transition-colors">Support</a></li>
            </ul>
          </div>

          {/* Community */}
          <div>
            <h3 className="text-white font-semibold mb-4 font-syne">Community</h3>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors p-2 rounded-lg hover:bg-gray-800/50">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors p-2 rounded-lg hover:bg-gray-800/50">
                <MessageCircle className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-green-400 transition-colors p-2 rounded-lg hover:bg-gray-800/50">
                <Github className="h-5 w-5" />
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-green-500/10 mt-8 pt-8 text-center">
          <p className="text-gray-400 text-sm font-syne">
            © 2025 FlowTrade. All rights reserved. Built on Solana.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
