{"app": {"name": "Flow Trade", "description": "Bot de trading crypto avancé avec des stratégies algorithmiques"}, "language": {"saved": "Enregistré"}, "nav": {"home": "Accueil", "wallet": "Portefeuille", "trading": "Trading", "referrals": "Parrainages", "profile": "Profil", "missions": "Missions", "news": "Actualités"}, "wallet": {"title": "Portefeuille", "terms": "Conditions", "totalAssets": "Act<PERSON>s <PERSON>", "connectWallet": "Con<PERSON><PERSON> le Portefeuille", "walletConnected": "Portefeuille Connecté", "invest": "Investir", "withdraw": "<PERSON><PERSON><PERSON>", "assets": "Actifs", "tutorial": "<PERSON><PERSON><PERSON>", "airdropInfo": "Infos Airdrop", "faqs": "FAQs", "connecting": "Connexion..."}, "withdraw": {"title": "Retirer des Actifs", "history": "Historique des Retraits", "backToWithdrawal": "Retour aux Retraits", "walletAddress": "<PERSON><PERSON>e du Portefeuille", "enterWalletAddress": "Entrez l'adresse de votre portefeuille", "currency": "<PERSON><PERSON>", "availableBalance": "Solde Disponible", "amountToWithdraw": "<PERSON><PERSON>tirer", "max": "MAX", "withdrawButton": "<PERSON><PERSON><PERSON>", "processing": "Traitement...", "minimumWithdrawal": "Retrait minimum", "viewHistory": "Voir l'Historique", "noHistoryFound": "Aucun historique de retrait trouvé", "needWalletConnection": "V<PERSON> devez connecter un portefeuille avant de pouvoir retirer des actifs.", "successful": "Retrait Réussi", "requestSubmitted": "Votre demande de retrait a été soumise avec succès."}, "refer": {"title": "Pa<PERSON>inez et Gagnez", "creatorProgram": "Programme Créateur", "referrals": "PARRAINAGES", "solEarned": "SOL GAGNÉS", "flowEarned": "FLOW GAGNÉS", "bonkEarned": "BONK GAGNÉS", "forInvitations": "Pour les Invitations", "yourReferralLink": "Votre Lien de Parra<PERSON>ge", "shareViaTelegram": "Partager via Telegram", "shareTitle": "Rejoignez-moi sur FLOW TRADE!", "shareText": "Utilisez mon lien de parrainage pour vous inscrire:", "referralsTab": "Parrainages", "tasksTab": "Tâches", "yourReferrals": "Vos <PERSON>", "noReferralsYet": "Vous n'avez encore parrainé personne", "shareToEarn": "Partagez votre lien de parrainage pour commencer à gagner des récompenses!", "noEarningsYet": "Pas encore de gains", "active": "Actif", "inactive": "Inactif"}, "referTasks": {"referralRewards": "Récompenses de Parrainage", "inviteFriends": "Invitez des amis pour gagner des tokens BONK", "refresh": "Actualiser", "dataRefreshed": "Données de parrainage actualisées", "refreshFailed": "Échec de l'actualisation des données", "loginRequired": "Vous devez être connecté pour réclamer des récompenses", "claimFailed": "Échec de la réclamation de récompense", "claimSuccess": "{amount} {currency} réclamés avec succès!", "errorClaimingReward": "Erreur lors de la réclamation de la récompense. Veuillez réessayer.", "referralCount": "{current}/{required} Parrainages", "claimed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "claimReward": "<PERSON><PERSON><PERSON><PERSON>er la Récompense", "needMoreReferrals": "Plus de Parrainages Nécessaires", "noTasksAvailable": "Aucune tâche de parrainage disponible", "checkBackLater": "Revenez plus tard pour de nouvelles opportunités de récompenses"}, "referralTasks": {"rt-001": {"title": "Invitez 5 Amis", "description": "Invitez 5 nouveaux utilisateurs pour réclamer 10,000 BONK"}, "rt-002": {"title": "Invitez 10 Amis", "description": "Invitez 10 nouveaux utilisateurs pour réclamer 20,000 BONK"}, "rt-003": {"title": "Invitez 25 Amis", "description": "Invitez 25 nouveaux utilisateurs pour réclamer 50,000 BONK"}, "rt-004": {"title": "Invitez 50 Amis", "description": "Invitez 50 nouveaux utilisateurs pour réclamer 100,000 BONK"}, "rt-005": {"title": "Invitez 100 Amis", "description": "Invitez 100 nouveaux utilisateurs pour réclamer 200,000 BONK"}, "rt-006": {"title": "Invitez 250 Amis", "description": "Invitez 250 nouveaux utilisateurs pour réclamer 500,000 BONK"}, "rt-007": {"title": "Invitez 500 Amis", "description": "Invitez 500 nouveaux utilisateurs pour réclamer 1,000,000 BONK"}, "rt-008": {"title": "Invitez 1000 Amis", "description": "Invitez 1000 nouveaux utilisateurs pour réclamer 2,000,000 BONK"}}, "content": {"creators": "Créateurs", "backToHome": "Retour à l'Accueil", "contentCreator": "Cré<PERSON><PERSON> de Contenu", "rewardsProgram": "Programme de Récompenses", "about": "À propos", "aboutDescription": "C<PERSON>ez et partagez du contenu lié à Flow Trade, comme des vidéos, des courts métrages, des mèmes, des animations, des graphiques et des tutoriels. Votre créativité peut s'exprimer dans n'importe quel format.", "earnRewards": "Gagnez des récompenses pour du contenu de qualité qui promeut Flow Trade", "submitYourContent": "Soumettez Votre Contenu", "linkLabel": "Lien vers votre contenu (Twitter, YouTube, Instagram, etc.) :", "linkPlaceholder": "https://twitter.com/votrenomdutilisateur/status/123456789", "pendingSubmissionWarning": "Vous avez déjà une soumission en attente. Veuillez attendre la revue avant de soumettre à nouveau.", "submitting": "Soumission en cours...", "submitContent": "Soumettre le Contenu", "yourSubmissions": "Vos Soumissions", "submittedOn": "<PERSON><PERSON><PERSON> le", "reward": "Récompense", "note": "Note", "noSubmissionsYet": "Vous n'avez pas encore soumis de contenu.", "guidelines": "Directives", "guideline1": {"title": "<PERSON><PERSON><PERSON>tenu", "description": "Assurez-vous que Flow Trade est mentionné ou mis en avant dans votre création."}, "guideline2": {"title": "Originalité", "description": "Le contenu doit être original et créé par vous. Le plagiat entraînera un rejet."}, "guideline3": {"title": "Qualité", "description": "Contenu de haute qualité avec audio/vidéo clair, grammaire correcte et présentation professionnelle."}, "guideline4": {"title": "Précision", "description": "Les informations doivent être précises et non trompeuses. Pas de promesses ou d'affirmations fausses."}, "status": {"pending": "en attente", "approved": "approuvé", "rejected": "rejet<PERSON>"}, "fetchFailed": "Échec de la récupération des soumissions", "validLinkRequired": "Veuillez entrer un lien de contenu valide", "submitFailed": "Échec de la soumission du contenu", "submitSuccess": "Votre contenu a été soumis avec succès !", "connectWalletWarning": "Veuillez connecter votre wallet avant de soumettre votre contenu.", "walletConnected": "Wallet Connecté", "walletAddressInfo": "Les soumissions seront liées à l'adresse de votre wallet", "walletRequired": "Veuillez connecter votre wallet pour soumettre du contenu"}, "news": {"articles": "Articles", "hotTopics": "Sujets Tendance", "loadingArticles": "Chargement des derniers articles...", "errorLoading": "Échec du chargement des articles. Veuillez réessayer.", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "noArticlesFound": "Aucun article trouvé", "loading": "Chargement...", "loadMore": "Charger Plus", "fetchFailed": "Échec de la récupération des articles", "backToArticles": "Retour aux Articles", "unknownDate": "Date inconnue", "openInTelegramToInteract": "Veuillez ouvrir dans Telegram pour interagir", "waitForAuthentication": "Veuillez patienter pendant que nous vous authentifions", "failedToUpdateLikeStatus": "Échec de la mise à jour du statut j'aime", "alreadyLiked": "Vous avez déjà aimé cet article", "linkCopied": "Lien copié !", "processing": "Traitement...", "liked": "<PERSON>'aime", "like": "<PERSON>'aime", "share": "Partager"}, "missions": {"title": "Missions", "tasks": "Tâches", "partnerRewards": "Récompenses Partenaires", "special": "Spécial", "telegramRequired": "Veuillez ouvrir l'application via Telegram pour démarrer les missions.", "telegramRequiredClaim": "Veuillez ouvrir via l'application Telegram pour réclamer cette récompense", "loading": "Chargement...", "missionStarted": "Mission commencée ! Vérification en attente : {minutes}.", "partnerMissionStarted": "Mission partenaire commencée ! Vérification en attente.", "verificationInProgress": "Vérification de mission en cours. Veuillez attendre jusqu'à la vérification.", "alreadyCompleted": "Vous avez déjà terminé cette mission.", "failedToStart": "Échec du démarrage de la mission", "somethingWentWrong": "<PERSON><PERSON><PERSON> chose s'est mal passé", "networkError": "<PERSON><PERSON><PERSON>", "failedToComplete": "Échec de l'achèvement de la mission", "missionCompleted": "Mission accomplie ! Vous avez gagné {amount} {type}", "noMissionsAvailable": "Aucune mission disponible pour le moment.", "noPartnerRewardsAvailable": "Aucune récompense partenaire disponible pour le moment.", "estimatedVerificationTime": "Temps de vérification estimé", "completed": "<PERSON><PERSON><PERSON><PERSON>", "claimReward": "<PERSON><PERSON><PERSON><PERSON>er la Récompense", "continueTask": "Continuer la Tâche", "continuePartnerTask": "Continuer la Tâche Partenaire", "startTask": "<PERSON><PERSON><PERSON><PERSON> la Tâche", "visitPartner": "Visiter le Partenaire", "enterPromoCode": "Veuillez entrer un code promo", "promoCodeSubmitted": "Code promo soumis ! 500 jetons gagnés !", "enterPromoCodeTitle": "Entrer le Code Promo", "redeemSpecialRewards": "Échangez des récompenses et des jetons spéciaux", "enterCodeHere": "Entrez le code ici", "submit": "So<PERSON><PERSON><PERSON>"}}