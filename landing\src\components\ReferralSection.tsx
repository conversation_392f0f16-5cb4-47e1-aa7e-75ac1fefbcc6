
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Link, TrendingUp, Infinity } from 'lucide-react';

interface ReferralSectionProps {
  scrollY: number;
}

const ReferralSection: React.FC<ReferralSectionProps> = ({ scrollY }) => {
  const levels = [
    { level: 1, reward: "5%", desc: "Direct invites" },
    { level: 2, reward: "2%", desc: "Second level" },
    { level: 3, reward: "1%", desc: "Third level" },
    { level: 4, reward: "1%", desc: "Fourth level" },
    { level: 5, reward: "1%", desc: "Fifth level" },
  ];

  return (
    <section id="referrals" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-gray-900/50 backdrop-blur-xl text-green-400 border border-green-500/20 font-medium">
            🫂 5-Level Referral System
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 font-syne">
            <span className="bg-gradient-to-r from-white to-green-400 bg-clip-text text-transparent">
              Refer and Earn
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto">
            FlowTrade rewards you every time you invite a friend — and their friends too. 
            Watch your network grow and earn passively.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            {levels.map((level, index) => (
              <Card 
                key={level.level}
                className="bg-gray-950/50 backdrop-blur-xl border border-green-500/10 p-6 hover:bg-gray-950/70 transition-all duration-300 hover:border-green-500/20 hover:shadow-xl hover:shadow-green-500/10 group"
                style={{ 
                  transform: `translateX(${Math.sin((scrollY + index * 100) * 0.002) * 10}px)`,
                  animationDelay: `${index * 0.1}s`
                }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-lg font-syne group-hover:scale-110 transition-transform duration-300">
                      {level.level}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white font-syne">Level {level.level}</h3>
                      <p className="text-gray-400 text-sm">{level.desc}</p>
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-green-400 font-syne group-hover:scale-110 transition-transform duration-300">
                    {level.reward}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="space-y-8">
            <Card className="bg-gray-950/30 backdrop-blur-xl border border-green-500/10 p-8 hover:border-green-500/20 transition-all duration-300">
              <div className="grid grid-cols-3 gap-6 text-center">
                <div className="group">
                  <Users className="h-12 w-12 text-green-400 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300" />
                  <div className="text-2xl font-bold text-green-400 font-syne">∞</div>
                  <div className="text-gray-300 text-sm">Network Size</div>
                </div>
                <div className="group">
                  <Link className="h-12 w-12 text-green-400 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300" />
                  <div className="text-2xl font-bold text-green-400 font-syne">∞</div>
                  <div className="text-gray-300 text-sm">Link Expiry</div>
                </div>
                <div className="group">
                  <TrendingUp className="h-12 w-12 text-green-400 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300" />
                  <div className="text-2xl font-bold text-green-400 font-syne">5</div>
                  <div className="text-gray-300 text-sm">Reward Levels</div>
                </div>
              </div>
            </Card>

            <div className="text-center space-y-4">
              <h3 className="text-2xl font-bold text-white font-syne">Share Your Legacy</h3>
              <p className="text-gray-300">
                Your invite link never expires. Share it once, earn forever as your network grows.
              </p>
              <div className="inline-flex items-center space-x-2 bg-gray-950/50 backdrop-blur-xl border border-green-500/20 rounded-lg px-4 py-2 hover:border-green-500/30 transition-all duration-300">
                <span className="text-gray-400 text-sm">https://t.me/FlowTrade_bot?start=</span>
                <span className="text-green-400 font-mono">your-unique-code</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ReferralSection;
