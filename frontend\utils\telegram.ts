/**
 * Telegram Web App Integration Utilities
 * This file contains helper functions for interacting with the Telegram Web App
 */

declare global {
  interface Window {
    Telegram?: {
      WebApp?: {
        initData: string;
        initDataUnsafe: any;
        ready: () => void;
        expand: () => void;
        close: () => void;
      };
    };
  }
}

/**
 * Initialize the Telegram Web App
 * This should be called when the app loads
 */
export function initializeTelegramWebApp() {
  if (typeof window === 'undefined') return;
  
  try {
    if (window.Telegram?.WebApp) {
      window.Telegram.WebApp.ready();
      window.Telegram.WebApp.expand();
      
      // Fix for navigation issues in Telegram WebApp
      setupTelegramNavigation();
      return true;
    }
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Setup special navigation handling for Telegram WebApp
 * This prevents navigation issues in the Telegram WebApp environment
 */
function setupTelegramNavigation() {
  if (typeof window === 'undefined') return;
  
  try {
    // Fix for Telegram WebApp navigation
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      // Find closest link if click is on child element
      const linkElement = target.closest('a');
      
      // Only handle internal links that don't have bypass attribute
      if (linkElement && 
          linkElement.href && 
          linkElement.href.startsWith(window.location.origin) && 
          !linkElement.getAttribute('data-bypass-telegram')) {
        
        // Prevent default navigation
        e.preventDefault();
        e.stopPropagation();
        
        // Get href and handle navigation manually
        const href = linkElement.getAttribute('href');
        if (href) {
          // Update URL without triggering navigation
          window.history.pushState({}, '', href);
          // Trigger a popstate event to notify router
          window.dispatchEvent(new Event('popstate'));
        }
      }
    }, true);
  } catch (error) {
    // Silent error
  }
}

/**
 * Get the current Telegram user data from the WebApp
 * @returns User data or null if not in Telegram or error
 */
export function getTelegramUser() {
  if (typeof window === 'undefined') return null;
  
  try {
    if (window.Telegram?.WebApp?.initDataUnsafe?.user) {
      return window.Telegram.WebApp.initDataUnsafe.user;
    }
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Get the Telegram chat ID if available
 * @returns Chat ID or null
 */
export function getChatId() {
  if (typeof window === 'undefined') return null;
  
  try {
    const initData = window.Telegram?.WebApp?.initDataUnsafe;
    
    if (initData && initData.start_param) {
      // Try to parse start_param as chat ID if it's in the expected format
      if (initData.start_param.startsWith('chat_')) {
        return initData.start_param.replace('chat_', '');
      }
    }
    
    if (initData && initData.chat_instance) {
      return initData.chat_instance;
    }
    
    if (initData && initData.chat) {
      return initData.chat.id;
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Close the Telegram Web App
 */
export function closeTelegramWebApp() {
  if (typeof window === 'undefined') return;
  
  try {
    if (window.Telegram?.WebApp) {
      window.Telegram.WebApp.close();
    }
  } catch (error) {
    // Silent error
  }
}

/**
 * Get query parameters from the current URL
 * @returns Object with query parameters
 */
export function getTelegramQueryParams() {
  if (typeof window === 'undefined') return {};
  
  try {
    const params = new URLSearchParams(window.location.search);
    const result: Record<string, string> = {};
    
    // Common Telegram parameters
    const keys = ['tgWebAppData', 'tgWebAppVersion', 'start_param', 'ref'];
    
    for (const key of keys) {
      const value = params.get(key);
      if (value) {
        result[key] = value;
      }
    }
    
    return result;
  } catch (error) {
    return {};
  }
}
