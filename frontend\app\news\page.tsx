import React from 'react'
import NewsSection from '@/components/news/NewsSection'
import { NewsArticle, NewsResponse } from '@/types/news'

async function getNews(page = 1, limit = 5) {
  try {
    // Use relative URL for API calls in production, or absolute URL in development
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const url = `${baseUrl}/api/news?page=${page}&limit=${limit}&isPublished=true`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error('Failed to fetch news');
    }
    
    const data: NewsResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching news:', error);
    return {
      success: false,
      data: [],
      pagination: { page: 1, limit, total: 0, pages: 0 }
    };
  }
}

async function NewsPage() {
  // Fetch initial news data server-side
  const newsData = await getNews();
  
  return (
    <div className="pt-4 pb-24 px-3">
      <NewsSection initialData={newsData} />
    </div>
  )
}

export default NewsPage

// Export these types for reuse
export type { NewsArticle, NewsResponse }