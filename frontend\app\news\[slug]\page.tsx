import React from 'react'
import { notFound } from 'next/navigation'
import NewsArticlePage from '@/components/news/NewsArticlePage'

// Define the page parameters type
type PageParams = {
  slug: string;
};

// Generate static params for known articles
export async function generateStaticParams() {
  try {
    // Use the API route to get the top articles
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/news?limit=10&isPublished=true`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch news for static params');
    }
    
    const data = await response.json();
    
    return data.data.map((article: any) => ({
      slug: article.slug,
    }));
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

// Fetch article data from API route
async function getArticle(slug: any) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/news/${slug}`, {
      next: { revalidate: 3600 } // Revalidate every hour
    });
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    return data.success ? data.data : null;
  } catch (error) {
    console.error('Error fetching article:', error);
    return null;
  }
}

// Define the page component
export default async function Page({ params }: any) {
  // Get the slug from params
  const { slug } = params;
  
  // Fetch the article data
  const article = await getArticle(slug);
  
  // If article not found, show 404 page
  if (!article) {
    notFound();
  }
  
  // Render the article page
    return (
    <div className="pt-4 pb-24 px-3">
      <NewsArticlePage article={article} />
    </div>
  );
}