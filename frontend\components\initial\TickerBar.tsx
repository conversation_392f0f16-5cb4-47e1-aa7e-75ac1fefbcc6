"use client"

import React from 'react';
import { Award, Check, ArrowUpRight, Zap } from 'lucide-react';

// Mock data for the ticker
const tickerItems = [
  { 
    type: 'transaction', 
    text: 'User0x4f2 invested 0.5 SOL in SOL/USD',
    time: '2 mins ago'
  },
  { 
    type: 'claim', 
    text: 'User0x7a1 claimed 0.015 SOL daily ROI',
    time: '5 mins ago'
  },
  { 
    type: 'mission', 
    text: 'User0x3b9 completed Partner Mission',
    time: '7 mins ago'
  },
  { 
    type: 'transaction', 
    text: 'User0x6c2 invested 0.8 SOL in BTC/USD',
    time: '12 mins ago'
  },
  { 
    type: 'claim', 
    text: 'User0x2d5 claimed 0.022 SOL daily ROI',
    time: '15 mins ago'
  },
  { 
    type: 'mission', 
    text: 'User0x9e7 completed Daily Task',
    time: '18 mins ago'
  },
  { 
    type: 'transaction', 
    text: 'User0x1a3 invested 1.2 SOL in ETH/USD',
    time: '22 mins ago'
  },
  { 
    type: 'claim', 
    text: 'User0x8f4 claimed 0.018 SOL daily ROI',
    time: '25 mins ago'
  }
];

const TickerBar = () => {

  // Render icon based on item type
  const getIcon = (type: string) => {
    switch (type) {
      case 'transaction':
        return <ArrowUpRight className="w-3.5 h-3.5 text-blue-400" />;
      case 'claim':
        return <Award className="w-3.5 h-3.5 text-green-400" />;
      case 'mission':
        return <Check className="w-3.5 h-3.5 text-purple-400" />;
      default:
        return <Zap className="w-3.5 h-3.5 text-yellow-400" />;
    }
  };

  return (
    <div className="w-full bg-black/80 backdrop-blur-sm border-b border-gray-800/50 overflow-hidden fixed top-0 z-20">
      <div className="py-1.5 overflow-hidden relative">
        <div className="ticker-container">
          <div className="ticker-wrapper">
            {/* First set of items */}
            {tickerItems.map((item, index) => (
              <div key={`item-${index}`} className="ticker-item">
                <div className="bg-gray-900/80 rounded-full p-1">
                  {getIcon(item.type)}
                </div>
                <span className="text-xs text-gray-300">{item.text}</span>
                <span className="text-xs text-gray-500">{item.time}</span>
                <span className="mx-2 text-gray-700">•</span>
              </div>
            ))}
            {/* Duplicate set for seamless loop */}
            {tickerItems.map((item, index) => (
              <div key={`item-dup-${index}`} className="ticker-item">
                <div className="bg-gray-900/80 rounded-full p-1">
                  {getIcon(item.type)}
                </div>
                <span className="text-xs text-gray-300">{item.text}</span>
                <span className="text-xs text-gray-500">{item.time}</span>
                <span className="mx-2 text-gray-700">•</span>
              </div>
            ))}
          </div>
        </div>
        
        <style jsx>{`
          .ticker-container {
            overflow: hidden;
            width: 100%;
          }
          .ticker-wrapper {
            display: inline-flex;
            white-space: nowrap;
            animation: ticker 60s linear infinite;
            will-change: transform;
          }
          .ticker-item {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            margin-right: 1.5rem;
          }
          @keyframes ticker {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-50%);
            }
          }
          @media (prefers-reduced-motion: reduce) {
            .ticker-wrapper {
              animation-duration: 40s;
            }
          }
          @media (max-width: 768px) {
            .ticker-wrapper {
              animation-duration: 30s;
            }
          }
        `}</style>
      </div>
    </div>
  );
};

export default TickerBar; 