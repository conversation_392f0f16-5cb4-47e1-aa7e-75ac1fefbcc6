/**
 * Truncates a wallet address for display purposes
 * @param address The full wallet address
 * @param startChars Number of characters to show at the start
 * @param endChars Number of characters to show at the end
 * @returns Truncated address string
 */
export const truncateAddress = (
  address: string,
  startChars: number = 4,
  endChars: number = 4
): string => {
  if (!address) return '';
  if (address.length <= startChars + endChars) return address;
  
  const start = address.slice(0, startChars);
  const end = address.slice(-endChars);
  
  return `${start}...${end}`;
};

/**
 * Formats a SOL balance with appropriate decimals
 * @param balance Balance in lamports
 * @returns Formatted balance string
 */
export const formatSolBalance = (balance: number): string => {
  if (balance === null || balance === undefined) return '0';
  
  // Convert from lamports to SOL (1 SOL = 10^9 lamports)
  const solBalance = balance / 1_000_000_000;
  
  return solBalance.toLocaleString(undefined, { 
    minimumFractionDigits: 2,
    maximumFractionDigits: 5 
  });
}; 