{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-tabs": "^1.1.7", "@solana/wallet-adapter-backpack": "^0.1.14", "@solana/wallet-adapter-base": "^0.9.26", "@solana/wallet-adapter-phantom": "^0.9.27", "@solana/wallet-adapter-react": "^0.15.38", "@solana/wallet-adapter-react-ui": "^0.9.38", "@solana/wallet-adapter-solflare": "^0.6.31", "@solana/web3.js": "^1.98.2", "@tailwindcss/typography": "^0.5.16", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-placeholder": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "@twa-dev/sdk": "^8.0.2", "@walletconnect/solana-adapter": "^0.0.8", "apexcharts": "^4.5.0", "axios": "^1.9.0", "cloudinary": "^2.6.0", "clsx": "^2.1.1", "framer-motion": "^12.7.4", "js-cookie": "^3.0.5", "lucide-react": "^0.488.0", "mongoose": "^8.13.2", "next": "15.3.0", "next-cloudinary": "^6.16.0", "next-intl": "^4.1.0", "pino-pretty": "^13.0.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-confetti": "^6.4.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "recharts": "^2.15.3", "slugify": "^1.6.6", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/mongoose": "^5.11.97", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}