import { NextRequest, NextResponse } from 'next/server';
import ContentSubmission from '@/libs/model/contents.schema';
import { connectToDatabase } from '@/libs/db';
import User from '@/libs/model/user.schema';
import { Types } from 'mongoose';

// Get all submissions with pagination and filters
export async function GET(req: NextRequest) {
  try {
    await connectToDatabase();

    // Parse query parameters
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const statusFilter = url.searchParams.get('status') || null;
    const query = url.searchParams.get('query') || null;
    
    // Build query
    const filterQuery: any = {};
    
    if (statusFilter) {
      filterQuery.status = statusFilter;
    }
    
    if (query) {
      filterQuery.$or = [
        { contentLink: { $regex: query, $options: 'i' } },
        { platform: { $regex: query, $options: 'i' } },
        { reviewNote: { $regex: query, $options: 'i' } },
      ];
    }
    
    const skip = (page - 1) * limit;
    
    // Get submissions with proper serialization
    const submissions = await ContentSubmission.find(filterQuery)
      .sort({ submittedAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user', 'displayName telegramUsername telegramId wallet')
      .lean()
      .then(docs => docs.map(doc => {
        // Handle null user case
        const user = doc.user ? {
          ...doc.user,
          _id: doc.user._id.toString(),
          wallet: doc.user.wallet ? {
            ...doc.user.wallet,
            balance: doc.user.wallet.balance || {
              sol: 0,
              flow: 0,
              bonk: 0
            }
          } : null
        } : null;

        return {
          ...doc,
          // @ts-ignore
          _id: doc._id.toString(),
          user,
          submittedAt: doc.submittedAt?.toISOString(),
          updatedAt: doc.updatedAt?.toISOString(),
          reviewedAt: doc.reviewedAt?.toISOString()
        };
      }));
      
    const total = await ContentSubmission.countDocuments(filterQuery);
    
    return NextResponse.json({
      success: true,
      data: {
        submissions,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      }
    });
    
  } catch (error: any) {
    console.error('Error fetching admin content submissions:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'An error occurred while fetching submissions' 
      },
      { status: 500 }
    );
  }
}

// Update submission status and reward
export async function PUT(req: NextRequest) {
  try {
    await connectToDatabase();
    
    const body = await req.json();
    const { 
      submissionId, 
      status, 
      rewardAmount, 
      rewardCurrency, 
      reviewNote, 
      txHash,
      manualPayment = false // Flag to indicate if payment was made manually
    } = body;
    
    // console.log('Received data:', {
    //   submissionId,
    //   status,
    //   rewardAmount,
    //   rewardCurrency,
    //   reviewNote,
    //   txHash
    // });

    // Validate submission ID
    if (!submissionId || !Types.ObjectId.isValid(submissionId)) {
      return NextResponse.json(
        { error: 'Valid submission ID is required' },
        { status: 400 }
      );
    }
    
    // Validate status
    if (!status || !['approved', 'rejected', 'pending'].includes(status)) {
      return NextResponse.json(
        { error: 'Valid status is required (approved, rejected, or pending)' },
        { status: 400 }
      );
    }
    
    // Validate reward currency
    const validCurrencies = ['sol', 'flow', 'bonk'];
    const normalizedCurrency = rewardCurrency?.toLowerCase();
    
    if (status === 'approved' && !validCurrencies.includes(normalizedCurrency)) {
      return NextResponse.json(
        { error: 'Valid reward currency is required (sol, flow, or bonk)' },
        { status: 400 }
      );
    }
    
    // Find submission
    const submission = await ContentSubmission.findById(submissionId);
    if (!submission) {
      return NextResponse.json(
        { error: 'Submission not found' },
        { status: 404 }
      );
    }
    
    // Get old status for comparison
    const oldStatus = submission.status;
    
    // Update submission
    submission.status = status;
    submission.reviewedAt = new Date();
    
    // Update optional fields if provided
    if (rewardAmount !== undefined) {
      submission.rewardAmount = rewardAmount;
    }
    
    if (rewardCurrency) {
      submission.rewardCurrency = normalizedCurrency;
    }
    
    if (reviewNote) {
      submission.reviewNote = reviewNote;
    }
    
    // Add transaction hash if provided
    if (txHash) {
      submission.txHash = txHash;
    }
    
    // Save submission changes
    await submission.save();
    
    return NextResponse.json({
      success: true,
      data: {
        id: submission._id,
        status: submission.status,
        reviewedAt: submission.reviewedAt,
        rewardAmount: submission.rewardAmount,
        rewardCurrency: submission.rewardCurrency,
        txHash: submission.txHash,
        manualPayment: manualPayment
      }
    });
    
  } catch (error: any) {
    console.error('Error updating content submission:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'An error occurred while updating submission' 
      },
      { status: 500 }
    );
  }
} 