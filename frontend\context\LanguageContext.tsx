"use client"

import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react'
import { 
  Language,
  languageNames,
  languageFlags,
  getTranslatedValue
} from '@/utils/i18n'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string, params?: Record<string, string>) => string
}

const DEFAULT_LANGUAGE: Language = 'en'
const STORAGE_KEY = 'language'

// Helper to safely check if we're in a browser environment
const isBrowser = typeof window !== 'undefined'

// Safe localStorage access function
const getSavedLanguage = (): Language | null => {
  if (!isBrowser) return null
  
  try {
    return localStorage.getItem(STORAGE_KEY) as Language || null
  } catch (e) {
    console.warn('Failed to access localStorage:', e)
    return null
  }
}

// Safe localStorage setter
const saveLanguage = (lang: Language): void => {
  if (!isBrowser) return
  
  try {
    localStorage.setItem(STORAGE_KEY, lang)
  } catch (e) {
    console.warn('Failed to save to localStorage:', e)
  }
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Initialize with default to avoid hydration mismatch
  const [language, setLanguageState] = useState<Language>(DEFAULT_LANGUAGE)

  // Simple effect to get the language from localStorage/browser on mount
  useEffect(() => {
    // Try to get from localStorage first
    const savedLang = getSavedLanguage()
    
    if (savedLang && Object.keys(languageNames).includes(savedLang)) {
      setLanguageState(savedLang)
      return
    }
    
    // If no valid language in localStorage, try browser language
    try {
      if (isBrowser) {
        const browserLang = navigator.language.split('-')[0] as Language
        if (browserLang && Object.keys(languageNames).includes(browserLang)) {
          setLanguageState(browserLang)
          saveLanguage(browserLang) // Save the detected language
        }
      }
    } catch (e) {
      console.warn('Error detecting browser language:', e)
    }
  }, [])

  // Wrapper to save language to localStorage when it changes
  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    saveLanguage(lang)
  }

  // Translation function
  const t = (key: string, params?: Record<string, string>): string => {
    return getTranslatedValue(key, language, DEFAULT_LANGUAGE, params)
  }

  // Create consistent context value object
  const contextValue = {
    language,
    setLanguage,
    t
  }

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  )
}

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

export { languageNames, languageFlags }
export type { Language }