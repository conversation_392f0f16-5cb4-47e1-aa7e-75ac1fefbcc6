// Create investment for users to start the Zapping engine (Only one at a time)

import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment, InvestmentStatus } from '@/libs/model/investment.schema';
import { User } from '@/libs/model/user.schema';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';

// Define referral level percentages
const REFERRAL_PERCENTAGES = [5, 2, 1, 1, 1]; // Level 1: 5%, Level 2: 2%, etc.

// Interface for referrer data
interface ReferrerData {
  walletAddress: string;
  percentage: number;
  level: number;
}

// Function to get valid referrers with active investments
export async function getValidReferrers(userId: string): Promise<ReferrerData[]> {
  try {
    await connectToDatabase();
    
    const validReferrers: ReferrerData[] = [];
    let currentUserId = new Types.ObjectId(userId);
    let level = 1;
    
    // Find the current user
    let currentUser = await User.findById(currentUserId);
    
    if (!currentUser || !currentUser.referredBy) {
      return validReferrers; // No referral chain
    }
    
    // Traverse up the referral chain (max 5 levels)
    while (level <= REFERRAL_PERCENTAGES.length && currentUser?.referredBy) {
      const referrerId = currentUser.referredBy;
      
      // Find the referrer
      const referrer = await User.findById(referrerId);
      if (!referrer) {
        break; // Referrer doesn't exist, break chain
      }
      
      // Check if referrer has an active investment
      const referrerInvestment = await Investment.findOne({
        user: referrer._id,
        status: InvestmentStatus.ACTIVE
      });
      
      if (referrerInvestment && referrerInvestment.walletAddress) {
        // Add this referrer to valid list with their appropriate percentage
        validReferrers.push({
          walletAddress: referrerInvestment.walletAddress,
          percentage: REFERRAL_PERCENTAGES[level - 1], // level 1 gets index 0 (5%)
          level: level
        });
      }
      // Note: We continue even if this referrer doesn't have an investment
      // to check the next level, but we don't add them to the valid list
      
      // Move to next level
      currentUser = referrer;
      level++;
    }
    
    return validReferrers;
  } catch (error) {
    console.error('Error getting valid referrers:', error);
    return [];
  }
}

// POST handler to create a new investment
// Helper function to process multi-level referral rewards
async function processReferralRewards(userId: Types.ObjectId, investmentAmount: number, currency: string = 'sol') {
  try {
    // Get valid referrers using our new function
    const validReferrers = await getValidReferrers(userId.toString());
    
    if (validReferrers.length === 0) {
      console.log('No valid referrers found');
      return;
    }
    
    console.log(`Processing rewards for ${validReferrers.length} valid referrers`);
    
    // Process each valid referrer
    for (const referrerData of validReferrers) {
      try {
        // Find the referrer investment by wallet address
        const referrerInvestment = await Investment.findOne({
          walletAddress: referrerData.walletAddress,
          status: InvestmentStatus.ACTIVE
        });
        
        if (!referrerInvestment) {
          console.log(`Referrer investment not found for wallet: ${referrerData.walletAddress}`);
          continue;
        }
        
        // Find the referrer user
        const referrer = await User.findById(referrerInvestment.user);
        if (!referrer) {
          console.log(`Referrer user not found for investment: ${referrerInvestment._id}`);
          continue;
        }
        
        // Calculate reward amount based on referrer's level percentage
        const rewardAmount = investmentAmount * (referrerData.percentage / 100);
        
        // Initialize referral stats if they don't exist
        if (!referrer.referralStats) {
          referrer.referralStats = {
            totalReferrals: 0,
            rewardsSol: 0,
            rewardsFlow: 0,
            rewardsBonk: 0
          };
        }
        
        // Check ROI cap (250%)
        const maxEarnings = referrerInvestment.initialAmount * 2.5; // 250% of initial
        const currentEarnings = referrerInvestment.earnedAmount;
        
        // Calculate adjusted reward if it would exceed cap
        let adjustedRewardAmount = rewardAmount;
        if (currentEarnings + rewardAmount > maxEarnings) {
          adjustedRewardAmount = Math.max(0, maxEarnings - currentEarnings);
        }
        
        // Only proceed if there's reward to give
        if (adjustedRewardAmount > 0) {
          // Add referral bonus to referrer's investment
          await referrerInvestment.addReferralBonus(adjustedRewardAmount);
          
          // Update referral stats
          if (currency === 'sol') {
            referrer.referralStats.rewardsSol += adjustedRewardAmount;
          } else if (currency === 'flow') {
            referrer.referralStats.rewardsFlow += adjustedRewardAmount;
          } else if (currency === 'bonk') {
            referrer.referralStats.rewardsBonk += adjustedRewardAmount;
          }
          
          // Find and update the specific referral entry
          if (referrer.referrals && referrer.referrals.length > 0) {
            const referralIndex = referrer.referrals.findIndex(
              (ref: any) => ref.user && ref.user.toString() === userId.toString()
            );
            
            if (referralIndex >= 0) {
              if (currency === 'sol') {
                referrer.referrals[referralIndex].earnedSol += adjustedRewardAmount;
              } else if (currency === 'flow') {
                referrer.referrals[referralIndex].earnedFlow += adjustedRewardAmount;
              } else if (currency === 'bonk') {
                referrer.referrals[referralIndex].earnedBonk += adjustedRewardAmount;
              }
              
              referrer.referrals[referralIndex].lastEarned = new Date();
            }
          }
          
          // Save referrer changes
          await referrer.save();
          
          console.log(`Processed referral reward: Level ${referrerData.level}, ${referrerData.percentage}%, Amount: ${adjustedRewardAmount} ${currency.toUpperCase()}`);
        } else {
          console.log(`Referrer ${referrerData.walletAddress} has reached ROI cap, skipping reward`);
        }
        
      } catch (error) {
        console.error(`Error processing referrer ${referrerData.walletAddress}:`, error);
        continue; // Continue with next referrer
      }
    }
    
  } catch (error) {
    console.error('Error processing referral rewards:', error);
  }
}

export async function POST(req: NextRequest) {
  try {
    // Get the user ID from cookies
    // const userId = (await cookies()).get('userId')?.value;
    const userId = "68380e3e3b44aa85b374c1f0";
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find the user
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { amount, currency = 'sol', walletAddress } = body;
    
    // Validate request
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return NextResponse.json(
        { success: false, error: 'Valid amount is required' },
        { status: 400 }
      );
    }
    
    // Convert to number and fixed precision
    const investmentAmount = parseFloat(parseFloat(amount).toFixed(6));
    

    if (investmentAmount < 0.05) {
      return NextResponse.json(
        { success: false, error: "Minimum zapping amount is 0.05 SOL" },
        { status: 400 }
      );
    }

    // Check if user already has an active investment
    const existingInvestment = await Investment.findOne({
      user: new Types.ObjectId(userId),
      status: 'active'
    });
    
    if (existingInvestment) {
      return NextResponse.json(
        { success: false, error: 'You already have an active investment' },
        { status: 400 }
      );
    }
   
    try {
      // Create new investment manually since we can't type the static method properly
      const now = new Date();
      
      const newInvestment = new Investment({
        walletAddress,
        user: new Types.ObjectId(userId),
        initialAmount: investmentAmount,
        earnedAmount: 0,
        withdrawnAmount: 0,
        currency,
        startDate: now,
        status: 'active',
        // claimsSinceWithdrawal: 0, // Set to 0 for standard behavior
        lastWithdrawalTime: null,
        lastCalculationTime: now,
        currentZapProgress: 0,
        lastClaimTime: null,
        claims: [],
        referralBonus: 0,
        percentageOfROI: 0
      });
      
      await newInvestment.save();
      
      // Process multi-level referral rewards
      await processReferralRewards(user._id, investmentAmount, currency);
      
      return NextResponse.json({
        success: true,
        data: {
          investment: {
            id: newInvestment._id,
            initialAmount: newInvestment.initialAmount,
            earnedAmount: 0,
            currency: newInvestment.currency,
            startDate: newInvestment.startDate,
            status: newInvestment.status
          }
        },
        message: 'Investment created successfully. Your AI-powered Zapping engine has been activated!'
      });
      
    } catch (error) {
      throw error;
    }
    
  } catch (error: any) {
    console.error('Error creating investment:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to create investment' },
      { status: 500 }
    );
  }
}