import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Mission } from '@/libs/model/missions.schema';
import { cookies, headers } from 'next/headers';

export async function GET() {
  try {
    await connectToDatabase();

    // Fetch all active missions
    const missions = await Mission.find({ status: 'active' }).lean();
    
    return NextResponse.json({ missions }, { status: 200 });
  } catch (error) {
    console.error('Error fetching missions:', error);
    return NextResponse.json({ error: 'Failed to fetch missions' }, { status: 500 });
  }
}
