import { NextRequest, NextResponse } from 'next/server';

// This API endpoint allows the Mini App to communicate with your backend
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { chatId, action, data } = body;
    
    // Validate the request
    if (!chatId) {
      return NextResponse.json({ 
        success: false, 
        message: 'Missing chat ID' 
      }, { status: 400 });
    }
    
    // Here you would typically:
    // 1. Validate the user's data from Telegram (verify hash)
    // 2. Process the action based on what the user is trying to do
    // 3. Possibly communicate with your Telegram bot via Bot API
    
    console.log('Received action from Telegram Mini App:', { chatId, action, data });
    
    // For now, just return success
    return NextResponse.json({ 
      success: true, 
      message: 'Action registered successfully',
      chatId,
      // You can include additional data here to be returned to the client
    });
  } catch (error) {
    console.error('Error processing Telegram Mini App request:', error);
    return NextResponse.json({ 
      success: false, 
      message: 'Internal server error' 
    }, { status: 500 });
  }
}

// Optional: Add a GET endpoint for testing
export async function GET() {
  return NextResponse.json({ 
    message: 'Telegram Mini App API is running' 
  });
} 