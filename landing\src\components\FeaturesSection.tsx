
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Zap, RefreshCw, TrendingUp, Shield, Rocket, Clock } from 'lucide-react';

interface FeaturesSectionProps {
  scrollY: number;
}

const FeaturesSection: React.FC<FeaturesSectionProps> = ({ scrollY }) => {
  const features = [
    {
      icon: Zap,
      title: "AI-Driven Meme Trading Engine",
      desc: "Advanced algorithms automatically trade trending Solana meme coins for optimal yields",
      gradient: "from-green-400/10 to-emerald-500/10"
    },
    {
      icon: Clock,
      title: "36-Hour Reward Cycles",
      desc: "Consistent, predictable returns every 36 hours with automated yield generation",
      gradient: "from-emerald-400/10 to-teal-500/10"
    },
    {
      icon: TrendingUp,
      title: "Streak Tracking & Flow Points",
      desc: "Daily login rewards and point accumulation for enhanced benefits and upgrades",
      gradient: "from-teal-400/10 to-cyan-500/10"
    },
    {
      icon: Shield,
      title: "Fully Non-Custodial",
      desc: "Your funds remain secure in your wallet while our AI optimizes your returns",
      gradient: "from-cyan-400/10 to-blue-500/10"
    },
    {
      icon: Rocket,
      title: "Weekly Meme Incubator",
      desc: "Q4 2025: Community-selected meme token launches every week",
      gradient: "from-blue-400/10 to-purple-500/10"
    },
    {
      icon: RefreshCw,
      title: "Auto-Reinvestment",
      desc: "Seamlessly compound your gains for exponential growth potential",
      gradient: "from-purple-400/10 to-pink-500/10"
    }
  ];

  return (
    <section id="features" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-gray-900/50 backdrop-blur-xl text-green-400 border border-green-500/20 font-medium animate-bounce-in">
            🚀 Platform Features
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 font-syne animate-slide-down">
            <span className="text-white">
              Why FlowTrade?
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto animate-scale-in animation-delay-300">
            Because the meme coin space is chaotic, and most people miss out on real opportunities. 
            FlowTrade solves that by letting AI do the work.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index}
              className={`bg-gray-950/50 backdrop-blur-xl border border-green-500/10 p-6 hover:bg-gray-950/70 transition-all duration-500 hover:border-green-500/20 hover:shadow-2xl hover:shadow-green-500/10 group relative overflow-hidden animate-fade-in-left`}
              style={{ 
                transform: `translateY(${Math.sin((scrollY + index * 300) * 0.002) * 15}px)`,
                animationDelay: `${index * 0.15 + 0.5}s`
              }}
            >
              <div className="relative z-10">
                <div className="relative mb-6">
                  <feature.icon className={`h-12 w-12 text-green-400 group-hover:scale-125 transition-all duration-500 group-hover:rotate-6 animate-rotate-in`} style={{ animationDelay: `${index * 0.1 + 0.8}s` }} />
                </div>
                
                <h3 className={`text-xl font-bold mb-3 text-white group-hover:text-green-300 transition-colors duration-300 font-syne animate-slide-up`} style={{ animationDelay: `${index * 0.1 + 1}s` }}>
                  {feature.title}
                </h3>
                
                <p className={`text-gray-400 group-hover:text-gray-300 transition-colors duration-300 animate-fade-in-right`} style={{ animationDelay: `${index * 0.1 + 1.2}s` }}>
                  {feature.desc}
                </p>
              </div>
            </Card>
          ))}
        </div>
      </div>

      <style>{`
        .animation-delay-300 { animation-delay: 0.3s; }
      `}</style>
    </section>
  );
};

export default FeaturesSection;
