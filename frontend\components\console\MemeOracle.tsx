"use client"
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import { ArrowRight, Loader2, DollarSign, TrendingUp, BadgeDollarSign, ExternalLink } from 'lucide-react';

interface TokenData {
  id: string;
  symbol: string;
  name: string;
  currentPrice: number;
  priceChange24h: number;
  marketCap: number;
  address: string;
}

const MemeOracle = () => {
  const [tokens, setTokens] = useState<TokenData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTokenData = async () => {
      try {
        setLoading(true);
        // Birdeye API call for trending tokens on Solana
        const response = await axios.get('https://public-api.birdeye.so/defi/tokens', {
          params: {
            sort: 'marketCap',
            includeNulls: false,
            limit: 5, 
            type: 'meme' // Filter for meme tokens
          },
          headers: {
            'X-API-KEY': process.env.NEXT_PUBLIC_BIRDEYE_API_KEY || 'YOUR_BIRDEYE_API_KEY', // Replace with your API key
          }
        });

        // Map response data
        const mappedTokens = response.data.data.map((token: any) => ({
          id: token.address,
          symbol: token.symbol,
          name: token.name,
          currentPrice: token.price,
          priceChange24h: token.priceChange24h,
          marketCap: token.marketCap,
          address: token.address
        }));

        setTokens(mappedTokens);
      } catch (err) {
        console.error('Error fetching token data:', err);
        setError('Failed to fetch token data');
        
        // Fallback to sample data for development
        setTokens(sampleTokenData);
      } finally {
        setLoading(false);
      }
    };

    fetchTokenData();

    // Refresh every 5 minutes
    const intervalId = setInterval(fetchTokenData, 5 * 60 * 1000);
    return () => clearInterval(intervalId);
  }, []);

  const formatNumber = (num: number) => {
    if (num >= 1_000_000_000) {
      return `$${(num / 1_000_000_000).toFixed(2)}B`;
    } else if (num >= 1_000_000) {
      return `$${(num / 1_000_000).toFixed(2)}M`;
    } else if (num >= 1_000) {
      return `$${(num / 1_000).toFixed(2)}K`;
    } else {
      return `$${num.toFixed(2)}`;
    }
  };

  const formatPrice = (price: number) => {
    if (price < 0.000001) {
      return `$${price.toExponential(2)}`;
    } else if (price < 0.01) {
      return `$${price.toFixed(6)}`;
    } else {
      return `$${price.toFixed(4)}`;
    }
  };

  const handleTradeClick = (tokenAddress: string) => {
    window.open(`https://birdeye.so/token/${tokenAddress}?chain=solana`, '_blank');
  };

  return (
    <div className="max-w-full">
      {/* Fancy Header */}
      <motion.div 
        className="relative mb-5 flex items-center"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-gradient-to-b from-green-400 to-transparent rounded-full"></div>
        <h1 className="text-xl font-bold ml-3 text-white">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-green-300">Meme Oracle</span>
        </h1>
        {/* Decorative elements */}
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-20 h-[1px] bg-gradient-to-r from-green-500/50 to-transparent"></div>
        <div className="absolute right-5 -top-2 w-3 h-3 rounded-full bg-green-500/20 blur-xl"></div>
        <div className="absolute right-3 -bottom-2 w-2 h-2 rounded-full bg-green-500/30 blur-md"></div>
      </motion.div>

      <div className="relative bg-black/30 backdrop-blur-md rounded-xl border border-gray-800/50 overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-green-500/20 to-transparent"></div>
        <div className="absolute -right-10 -top-10 w-20 h-20 bg-green-500/5 rounded-full blur-xl"></div>
        <div className="absolute -left-5 bottom-10 w-10 h-10 bg-green-500/5 rounded-full blur-lg"></div>
        
        <div className="p-3">
          {loading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="w-8 h-8 text-green-500 animate-spin" />
            </div>
          ) : error && tokens.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-red-400">Failed to load tokens. Please try again later.</p>
            </div>
          ) : (
            <div className="space-y-2">
              {/* Column headers */}
              <div className="grid grid-cols-12 text-xs text-gray-500 pb-1 px-2 border-b border-gray-800/50">
                <div className="col-span-4">Token</div>
                <div className="col-span-3 text-right">Price</div>
                <div className="col-span-2 text-right">24h</div>
                <div className="col-span-3 text-right">Market Cap</div>
              </div>
              
              {/* Token list */}
              <div className="space-y-1 max-h-64 overflow-y-auto">
                {tokens.map((token, index) => (
                  <motion.div 
                    key={token.id}
                    className="grid grid-cols-12 items-center p-2 bg-black/40 rounded-md border border-gray-800/50 hover:border-green-500/30 hover:bg-black/60 cursor-pointer"
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    whileHover={{ scale: 1.01 }}
                    onClick={() => handleTradeClick(token.address)}
                  >
                    {/* Token Symbol/Name */}
                    <div className="col-span-4 flex items-center">
                      <div className="w-7 h-7 bg-green-900/20 rounded-full flex items-center justify-center mr-2">
                        <BadgeDollarSign className="w-3.5 h-3.5 text-green-400" />
                      </div>
                      <div className="overflow-hidden">
                        <div className="font-medium text-sm text-white truncate">{token.symbol}</div>
                        <div className="text-xs text-gray-400 truncate">{token.name}</div>
                      </div>
                    </div>
                    
                    {/* Price */}
                    <div className="col-span-3 text-right font-medium text-sm text-white">
                      {formatPrice(token.currentPrice)}
                    </div>
                    
                    {/* 24h Change */}
                    <div 
                      className={`col-span-2 text-right text-xs font-medium ${
                        token.priceChange24h >= 0 ? 'text-green-500' : 'text-red-500'
                      }`}
                    >
                      <div className="flex items-center justify-end">
                        {token.priceChange24h >= 0 ? '+' : ''}
                        {token.priceChange24h.toFixed(2)}%
                      </div>
                    </div>
                    
                    {/* Market Cap */}
                    <div className="col-span-3 text-right text-xs text-gray-300 flex items-center justify-end">
                      {formatNumber(token.marketCap)}
                      <ExternalLink className="w-3 h-3 ml-1 text-green-500/70" />
                    </div>
                  </motion.div>
                ))}
              </div>
              
              {/* View all button */}
              <div className="flex justify-center pt-3">
                <motion.button 
                  className="bg-black/60 text-green-400 text-xs font-medium py-1.5 px-3 rounded-md border border-green-500/30 hover:bg-black/80 transition-colors flex items-center"
                  whileTap={{ scale: 0.97 }}
                  onClick={() => window.open('https://birdeye.so/?chain=solana&type=meme', '_blank')}
                >
                  <span>View All Meme Tokens</span>
                  <ArrowRight className="w-3 h-3 ml-1.5" />
                </motion.button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Sample data for development
const sampleTokenData: TokenData[] = [
  {
    id: '1',
    symbol: 'BONK',
    name: 'Bonk',
    currentPrice: 0.00000254,
    priceChange24h: 5.23,
    marketCap: 1580000000,
    address: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263'
  },
  {
    id: '2',
    symbol: 'WIF',
    name: 'Dogwifhat',
    currentPrice: 0.9825,
    priceChange24h: -2.14,
    marketCap: 982000000,
    address: 'EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm'
  },
  {
    id: '3',
    symbol: 'POPCAT',
    name: 'Popcat',
    currentPrice: 0.00023567,
    priceChange24h: 12.67,
    marketCap: 235000000,
    address: 'P0PCwR3VJKfgEr1UFdBLV5vmTkiULw47NuPRTHh9ZCk'
  },
  {
    id: '4',
    symbol: 'BERN',
    name: 'Bern Inu',
    currentPrice: 0.00000145,
    priceChange24h: -8.35,
    marketCap: 145000000,
    address: 'bernerJbrB7RW7LF1TGpW4zrV2j1qWxCoPDMSM2LarNx'
  },
  {
    id: '5',
    symbol: 'CAT',
    name: 'Solana Cat',
    currentPrice: 0.00034521,
    priceChange24h: 3.78,
    marketCap: 124000000,
    address: 'CAT3vJ5X8H9iy3VgLkK9iqwxzV2PiXR6NMzaT4Réb2i'
  }
];

export default MemeOracle;