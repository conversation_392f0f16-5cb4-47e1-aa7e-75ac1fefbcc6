/* Optimized animations using pure CSS for better performance */

@keyframes spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

.fanAnimation {
  animation-name: spin;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  animation-duration: var(--fan-duration, 5s);
  animation-play-state: var(--fan-play-state, paused);
  
  /* Performance optimizations */
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  
  /* Hardware acceleration */
  transform: translateZ(0);
}

.meterProgress {
  stroke-dashoffset: var(--stroke-offset, 0);
  stroke: var(--stroke-color, #22c55e);
  transition: stroke-dashoffset 0.3s ease-out, stroke 0.3s ease-out;
  
  /* Performance optimizations */
  will-change: stroke-dashoffset, stroke;
}

.hardwareAccelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .fanAnimation {
    animation-duration: 10s;
  }
  
  .meterProgress {
    transition: none;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .fanAnimation {
    animation-duration: calc(var(--fan-duration, 5s) * 1.2); /* Slightly slower on mobile */
  }
} 