import { NextRequest, NextResponse } from 'next/server';
import ContentSubmission from '@/libs/model/contents.schema';
import { connectToDatabase } from '@/libs/db';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';

export async function POST(req: NextRequest) {
  try {
    // Check authentication via cookies
    //  const userId = "68380e3e3b44aa85b374c1f0"
    const userId = (await cookies()).get('userId')?.value;
    
    if (!userId) {
      return NextResponse.json(
        { error: 'You must be logged in to submit content' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Check if user already has a pending submission
    // @ts-ignore
    const hasPending = await ContentSubmission.hasPendingSubmission(new Types.ObjectId(userId));
    if (hasPending) {
      return NextResponse.json(
        { error: 'You already have a pending submission. Please wait for review before submitting again.' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { contentLink, walletAddress } = body;
    
    // Validate content link
    if (!contentLink || typeof contentLink !== 'string' || contentLink.trim() === '') {
      return NextResponse.json(
        { error: 'Content link is required' },
        { status: 400 }
      );
    }
    
    // Validate wallet address
    if (!walletAddress || typeof walletAddress !== 'string' || walletAddress.trim() === '') {
      return NextResponse.json(
        { error: 'Wallet address is required' },
        { status: 400 }
      );
    }
    
    // Basic URL validation
    let url;
    try {
      url = new URL(contentLink);
      // Ensure it has http or https protocol
      if (!url.protocol.match(/^https?:$/)) {
        throw new Error('Invalid URL protocol');
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid URL format. Please provide a valid URL.' },
        { status: 400 }
      );
    }
    
    // Create new submission
    const submission = new ContentSubmission({
      user: new Types.ObjectId(userId),
      walletAddress: walletAddress.trim(),
      contentLink,
      status: 'pending',
      submittedAt: new Date(),
      rewardAmount: 0, // Default reward amount is 0
      rewardCurrency: 'sol', // Default currency is SOL
    });
    
    // Save submission
    await submission.save();
    
    return NextResponse.json({
      success: true,
      data: {
        id: submission._id,
        walletAddress: submission.walletAddress,
        contentLink: submission.contentLink,
        status: submission.status,
        submittedAt: submission.submittedAt
      }
    });
    
  } catch (error: any) {
    console.error('Error submitting content:', error);
    
    // Handle duplicate submission error
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'You have already submitted this content link' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: error.message || 'An error occurred while submitting content' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Check authentication via cookies
       const userId = "68380e3e3b44aa85b374c1f0"
    // const userId = (await cookies()).get('userId')?.value;
    
    if (!userId) {
      return NextResponse.json(
        { error: 'You must be logged in to view your submissions' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Get submissions for this user
    const submissions = await ContentSubmission.find({ user: new Types.ObjectId(userId) })
      .sort({ submittedAt: -1 }) // Most recent first
      .select('-__v') // Exclude version field
      .lean(); // Return plain JavaScript objects
    
    // Check if user has any pending submissions
    const hasPendingSubmission = submissions.some(sub => sub.status === 'pending');
    
    return NextResponse.json({
      success: true,
      data: {
        submissions,
        hasPendingSubmission
      }
    });
    
  } catch (error: any) {
    console.error('Error fetching content submissions:', error);
    
    return NextResponse.json(
      { error: error.message || 'An error occurred while fetching submissions' },
      { status: 500 }
    );
  }
} 