import { NextRequest, NextResponse } from 'next/server';
import { getValidReferrers } from '../investment/create/route';

// Test endpoint to verify referrer functionality
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId') || "68380e3e3b44aa85b374c1f0"; // Default test user
    
    console.log(`Testing referrer system for user: ${userId}`);
    
    // Get valid referrers
    const validReferrers = await getValidReferrers(userId);
    
    console.log('Valid referrers found:', validReferrers);
    
    // Format the response with detailed information
    const response = {
      success: true,
      data: {
        userId,
        validReferrers,
        count: validReferrers.length,
        contractFormatted: validReferrers.map(ref => ({
          walletAddress: ref.walletAddress,
          percentage: ref.percentage,
          level: ref.level
        })),
        summary: {
          totalLevels: validReferrers.length,
          totalPercentage: validReferrers.reduce((sum, ref) => sum + ref.percentage, 0),
          levels: validReferrers.map(ref => `Level ${ref.level}: ${ref.percentage}%`).join(', ')
        }
      },
      message: validReferrers.length > 0 
        ? `Found ${validReferrers.length} valid referrers for contract` 
        : 'No valid referrers found - user will invest without referral rewards'
    };
    
    return NextResponse.json(response);
    
  } catch (error: any) {
    console.error('Error in test referrers endpoint:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Test failed',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 