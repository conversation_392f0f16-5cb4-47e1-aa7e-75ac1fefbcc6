import mongoose, { Schema } from 'mongoose';

export enum WithdrawalStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum WithdrawalCurrency {
  SOL = 'sol',
  FLOW = 'flow',
  BONK = 'bonk'
}

export interface IWithdrawal {
  user: mongoose.Types.ObjectId;
  wallet: string;
  amount: number;
  currency: WithdrawalCurrency;
  status: WithdrawalStatus;
  txHash?: string;
  notes?: string;
  completedAt?: Date;
  cancelledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const WithdrawalSchema = new Schema<IWithdrawal>(
  {
    // User reference
    user: { 
      type: Schema.Types.ObjectId, 
      ref: 'User', 
      required: true, 
      index: true 
    },
    
    // Wallet address to send funds to
    wallet: { 
      type: String, 
      required: true 
    },
    
    // Amount and currency
    amount: { 
      type: Number, 
      required: true, 
      min: 0 
    },
    currency: { 
      type: String, 
      enum: Object.values(WithdrawalCurrency), 
      required: true 
    },
    
    // Status and transaction data
    status: { 
      type: String, 
      enum: Object.values(WithdrawalStatus), 
      default: WithdrawalStatus.PENDING,
      index: true
    },
    txHash: { 
      type: String, 
      sparse: true 
    },
    notes: { 
      type: String 
    },
    
    // Timestamps for status changes
    completedAt: { 
      type: Date 
    },
    cancelledAt: { 
      type: Date 
    }
  },
  {
    timestamps: true,
    versionKey: false
  }
);

// Create indexes for faster queries
WithdrawalSchema.index({ createdAt: -1 });
WithdrawalSchema.index({ currency: 1, status: 1 });
WithdrawalSchema.index({ user: 1, createdAt: -1 });

export const Withdrawal = mongoose.models.Withdrawal || 
  mongoose.model<IWithdrawal>('Withdrawal', WithdrawalSchema);

export default Withdrawal;
