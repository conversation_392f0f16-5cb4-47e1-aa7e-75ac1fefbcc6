"use client"

import React from 'react'
import SlidebarAdmin from '@/components/admin/SlidebarAdmin'
import AdminCharts from '@/components/admin/AdminCharts'

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="flex h-screen overflow-hidden bg-gray-50 text-gray-600">
      <SlidebarAdmin />
      <main className="flex-1 overflow-y-auto p-6">
        {children}
      </main>
      <AdminCharts />
    </div>
  )
}
