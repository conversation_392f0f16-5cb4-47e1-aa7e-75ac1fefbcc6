"use client"
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, <PERSON>, Co<PERSON>, Share2, <PERSON><PERSON><PERSON>cle2, <PERSON>, Award, UserCheck, <PERSON>Che<PERSON>, Loader2 } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import ReferTasks from './ReferTasks'
type TabType = 'friends' | 'tasks';

const Refer = () => {
  const [activeTab, setActiveTab] = useState<TabType>('friends');
  const [copied, setCopied] = useState(false);
  
  // Use data from AuthContext directly
  const { user, isLoading } = useAuth();
  const { t } = useLanguage();
  const router = useRouter();
  console.log(user)
  
  // Generate referral link using telegramId
  const getReferralLink = () => {
    if (!user?.telegramId) return 'https://t.me/FlowTrade_bot';
    return `https://t.me/FlowTrade_bot?start=${user.telegramId}`;
  };
  
  // Handle copy function
  const handleCopy = () => {
    navigator.clipboard.writeText(getReferralLink())
    setCopied(true)
    setTimeout(() => {
      setCopied(false)
    }, 2000)
  }
  
  // Handle share function
  const handleShare = () => {
    if (typeof window !== 'undefined') {
      // For Telegram Web Apps
      let WebApp: any = null;
      try {
        WebApp = require('@twa-dev/sdk').default;
        if (WebApp?.MainButton) {
          WebApp.ShareButton.show();
          WebApp.ShareButton.onClick(() => {
            WebApp.shareUrl(getReferralLink());
          });
          return;
        }
      } catch (e) {
        console.error("Error using Telegram Web App SDK:", e);
      }
      
      // Fallback to Web Share API
      if (navigator.share) {
        navigator.share({
          title: t('refer.shareTitle'),
          text: t('refer.shareText'),
          url: getReferralLink()
        }).catch(() => {
          copyReferralLink(); // Fallback to copy
        });
      } else {
        copyReferralLink(); // Fallback to copy
      }
    }
  }
  
  // Fallback copy function
  const copyReferralLink = () => {
    navigator.clipboard.writeText(getReferralLink())
      .then(() => setCopied(true))
      .catch(err => console.error('Failed to copy:', err));
    
    setTimeout(() => setCopied(false), 2000);
  }

  // Helper functions to safely access nested user properties
  const getTotalReferrals = () => {
    if (!user) return 0;
    return user.referrals?.length || 0;
  };

  const getSolEarnings = () => {
    if (!user?.referralStats?.rewardsSol && user?.referralStats?.rewardsSol !== 0) return 0;
    return user.referralStats.rewardsSol;
  };

  const getFlowEarnings = () => {
    if (!user?.referralStats?.rewardsFlow && user?.referralStats?.rewardsFlow !== 0) return 0;
    return user.referralStats.rewardsFlow;
  };

  const getBonkEarnings = () => {
    if (!user?.referralStats?.rewardsBonk && user?.referralStats?.rewardsBonk !== 0) return 0;
    return user.referralStats.rewardsBonk;
  };

  const hasReferrals = () => {
    return user?.referrals && Array.isArray(user.referrals) && user.referrals.length > 0;
  };

  return (
    <div className="max-w-[500px] mx-auto relative z-10">
      {/* Glowing Background Effect */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-48 h-48 bg-green-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>
      
      {/* Header Section with Creator Program Button */}
      <div className="flex justify-between items-center mb-6">
        <motion.div 
          className="relative flex items-center"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-gradient-to-b from-green-400 to-transparent rounded-full"></div>
          <h1 className="text-xl font-bold ml-3 text-white">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-green-300">{t('refer.title')}</span>
          </h1>
          {/* Decorative elements */}
          <div className="absolute -right-5 top-1/2 transform -translate-y-1/2 w-10 h-[1px] bg-gradient-to-r from-green-500/50 to-transparent"></div>
        </motion.div>
        
        <motion.button
          onClick={() => router.push('/content')}
          className="flex cursor-pointer items-center space-x-1.5 bg-black/60 backdrop-blur-md rounded-lg border border-green-500/30 px-3.5 py-2 text-xs font-medium text-green-400"
          whileHover={{ y: -2, boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)" }}
          whileTap={{ scale: 0.97 }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <Gift className="w-3.5 h-3.5" />
          <span>{t('refer.creatorProgram')}</span>
        </motion.button>
      </div>
      
      {/* Stats Cards Section */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        {/* Total Referrals Card */}
        <motion.div
          className="bg-black/60 backdrop-blur-md rounded-lg p-3.5 border border-gray-800/50 relative overflow-hidden"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          whileHover={{ 
            y: -2, 
            borderColor: "rgba(74, 222, 128, 0.2)",
            boxShadow: '0 0 20px rgba(59, 130, 246, 0.1)'
          }}
        >
          <div className="absolute -right-6 -top-6 w-12 h-12 bg-blue-500/10 rounded-full blur-lg"></div>
          <div className="text-center">
            <div className="flex justify-center mb-1">
              <div className="w-7 h-7 rounded-full bg-gradient-to-br from-blue-900/60 to-blue-500/30 shadow-inner flex items-center justify-center">
                <Users className="w-4 h-4 text-blue-400" />
              </div>
            </div>
            <div className="text-lg font-semibold text-white">
              {isLoading ? 
                <span className="inline-block w-5 h-5 rounded-full border-2 border-blue-500/30 border-t-blue-500 animate-spin"></span> : 
                getTotalReferrals()
              }
            </div>
            <div className="text-[10px] text-gray-400 uppercase tracking-wider mt-1">{t('refer.referrals')}</div>
          </div>
        </motion.div>
        
        {/* SOL Earned Card */}
        <motion.div
          className="bg-black/60 backdrop-blur-md rounded-lg p-3.5 border border-gray-800/50 relative overflow-hidden"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          whileHover={{ 
            y: -2, 
            borderColor: "rgba(74, 222, 128, 0.2)",
            boxShadow: '0 0 20px rgba(168, 85, 247, 0.1)'
          }}
        >
          <div className="absolute -right-6 -top-6 w-12 h-12 bg-purple-500/10 rounded-full blur-lg"></div>
          <div className="text-center">
            <div className="flex justify-center mb-1">
              <div className="w-7 h-7 rounded-full bg-gradient-to-br from-purple-900/60 to-purple-500/30 shadow-inner flex items-center justify-center">
                <span className="text-purple-400 font-bold text-xs">B</span>
              </div>
            </div>
            <div className="text-lg font-semibold text-white">
              {isLoading ? 
                <span className="inline-block w-5 h-5 rounded-full border-2 border-purple-500/30 border-t-purple-500 animate-spin"></span> : 
                getBonkEarnings().toLocaleString()
              }
            </div>
            <div className="text-[10px] text-gray-400 uppercase tracking-wider mt-1">{t('refer.bonkEarned')}</div>
          </div>
        </motion.div>
        
        {/* FLOW Earned Card */}
        <motion.div
          className="bg-black/60 backdrop-blur-md rounded-lg p-3.5 border border-gray-800/50 relative overflow-hidden"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          whileHover={{ 
            y: -2, 
            borderColor: "rgba(74, 222, 128, 0.2)",
            boxShadow: '0 0 20px rgba(74, 222, 128, 0.1)'
          }}
        >
          <div className="absolute -right-6 -top-6 w-12 h-12 bg-green-500/10 rounded-full blur-lg"></div>
          <div className="text-center">
            <div className="flex justify-center mb-1">
              <div className="w-7 h-7 rounded-full bg-gradient-to-br from-green-900/60 to-green-500/30 shadow-inner flex items-center justify-center">
                <span className="text-green-400 font-bold text-xs">F</span>
              </div>
            </div>
            <div className="text-lg font-semibold text-white">
              {isLoading ? 
                <span className="inline-block w-5 h-5 rounded-full border-2 border-green-500/30 border-t-green-500 animate-spin"></span> : 
                getFlowEarnings().toFixed(2)
              }
            </div>
            <div className="text-[10px] text-gray-400 uppercase tracking-wider mt-1">{t('refer.flowEarned')}</div>
          </div>
        </motion.div>
      </div>
      
      {/* BONK Earned Card - Added as an additional stat */}
      {/* <motion.div
        className="mb-6 bg-black/60 backdrop-blur-md rounded-lg p-3.5 border border-gray-800/50 relative overflow-hidden"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.35 }}
        whileHover={{ 
          y: -2, 
          borderColor: "rgba(74, 222, 128, 0.2)",
          boxShadow: '0 0 20px rgba(239, 68, 68, 0.1)'
        }}
      >
        <div className="absolute -right-6 -top-6 w-12 h-12 bg-red-500/10 rounded-full blur-lg"></div>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-red-900/60 to-orange-500/30 shadow-inner flex items-center justify-center mr-3">
              <span className="text-orange-400 font-bold text-sm">B</span>
            </div>
            <div>
              <div className="text-xs text-gray-400 uppercase tracking-wider">{t('refer.bonkEarned')}</div>
              <div className="text-lg font-semibold text-white">
                {isLoading ? 
                  <span className="inline-block w-5 h-5 rounded-full border-2 border-orange-500/30 border-t-orange-500 animate-spin"></span> : 
                  getBonkEarnings().toLocaleString()
                }
              </div>
            </div>
          </div>
          <div className="text-xs px-3 py-1 rounded-full bg-orange-500/10 text-orange-400 border border-orange-500/20">
            {t('refer.forInvitations')}
          </div>
        </div>
      </motion.div> */}
      
      {/* Referral Link Section */}
      <motion.div
        className="mb-6 bg-black/60 backdrop-blur-md rounded-lg border border-gray-800/50 p-5 relative overflow-hidden"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
        whileHover={{ 
          borderColor: "rgba(74, 222, 128, 0.15)",
          boxShadow: '0 0 25px rgba(0, 0, 0, 0.2)'
        }}
      >
        <div className="absolute -left-10 -top-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>
        <div className="absolute -right-10 -bottom-10 w-16 h-16 bg-blue-500/10 rounded-full blur-xl"></div>
        
        <div className="text-sm font-medium text-white mb-3">{t('refer.yourReferralLink')}</div>
        
        <div className="flex items-stretch mb-3">
          <div className="flex-1 bg-black/80 backdrop-blur-md rounded-l-lg border border-gray-800 py-2.5 px-3.5 text-sm text-gray-400 overflow-hidden overflow-ellipsis whitespace-nowrap">
            {getReferralLink()}
          </div>
          <button 
            className={`px-3.5 flex items-center justify-center rounded-r-lg border border-l-0 ${copied ? 'bg-green-500/20 border-green-500/30 text-green-400' : 'bg-gray-800/50 border-gray-800 text-gray-400'}`}
            onClick={handleCopy}
          >
            {copied ? <CheckCircle2 className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
          </button>
        </div>
        
        <motion.button
          className="w-full bg-green-500/20 backdrop-blur-md hover:bg-green-500/30 text-green-400 border border-green-500/30 rounded-lg py-2.5 text-sm font-medium flex items-center justify-center space-x-2"
          whileHover={{ y: -1, boxShadow: "0 4px 12px rgba(34, 197, 94, 0.1)" }}
          whileTap={{ scale: 0.98 }}
          onClick={handleShare}
        >
          <Share2 className="w-4 h-4" />
          <span>{t('refer.shareViaTelegram')}</span>
        </motion.button>
      </motion.div>
      
      {/* Tabs for Referrals and Tasks */}
      <div className="mb-4 flex bg-black/40 backdrop-blur-md rounded-lg border border-gray-800/50 p-1">
        <button
          onClick={() => setActiveTab('friends')}
          className={`flex-1 flex items-center justify-center space-x-1.5 py-2 px-3 rounded-md text-sm font-medium transition-all ${
            activeTab === 'friends' 
              ? 'bg-green-500/20 text-green-400' 
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          <UserCheck className="w-4 h-4" />
          <span>{t('refer.referralsTab')}</span>
        </button>
        <button
          onClick={() => setActiveTab('tasks')}
          className={`flex-1 flex items-center justify-center space-x-1.5 py-2 px-3 rounded-md text-sm font-medium transition-all ${
            activeTab === 'tasks' 
              ? 'bg-green-500/20 text-green-400' 
              : 'text-gray-400 hover:text-gray-300'
          }`}
        >
          <ListChecks className="w-4 h-4" />
          <span>{t('refer.tasksTab')}</span>
        </button>
      </div>
      
      {/* Content based on active tab */}
      {activeTab === 'friends' ? (
        <motion.div
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          <div className="flex items-center mb-3">
            <h2 className="text-sm font-medium text-white">{t('refer.yourReferrals')}</h2>
            <div className="flex-1 h-[1px] bg-gradient-to-r from-gray-800/50 to-transparent ml-3"></div>
          </div>
          
          {isLoading ? (
            <div className="py-8 flex justify-center">
              <Loader2 className="w-8 h-8 text-green-500/50 animate-spin" />
            </div>
          ) : hasReferrals() ? (
            <div className="space-y-3">
              {user?.referrals?.map((referral: any, index) => (
                <motion.div 
                  key={index}
                  className="bg-black/60 backdrop-blur-md rounded-lg border border-gray-800/50 p-4 relative overflow-hidden"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 * Math.min(index, 5) }}
                  whileHover={{ 
                    y: -2,
                    borderColor: "rgba(74, 222, 128, 0.2)",
                    boxShadow: '0 0 20px rgba(0, 0, 0, 0.15)'
                  }}
                >
                  <div className="absolute -right-6 -bottom-6 w-12 h-12 bg-blue-500/10 rounded-full blur-lg"></div>
                  <div className="absolute -left-6 -top-6 w-12 h-12 bg-green-500/5 rounded-full blur-lg"></div>
                  
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-500/20 to-blue-500/20 flex items-center justify-center text-white font-medium text-sm">
                          {referral?.user?.telegramUsername?.[0]?.toUpperCase() || referral?.user?.telegramName?.[0]?.toUpperCase() || '?'}
                        </div>
                      <div>
                        <div className="text-sm font-medium text-white mb-1">
                          {(referral?.user?.telegramUsername || referral?.user?.telegramName)?.length > 30 
                            ? `${(referral?.user?.telegramUsername || referral?.user?.telegramName)?.slice(0, 30)}...`
                            : referral?.user?.telegramUsername || referral?.user?.telegramName}
                        </div>
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="w-3 h-3 mr-1" />
                          <span>{new Date(referral.joinedAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col items-end">
                      <div className="text-sm font-medium text-green-400 mb-1 flex flex-col items-end">
                        {(referral.earnedSol > 0) && (
                          <div className="flex items-center">
                            <Award className="w-3 h-3 mr-1" />
                            <span>{referral.earnedSol.toFixed(2)} SOL</span>
                          </div>
                        )}
                        {(referral.earnedFlow > 0) && (
                          <div className="flex items-center mt-1">
                            <Award className="w-3 h-3 mr-1" />
                            <span>{referral.earnedFlow.toFixed(2)} FLOW</span>
                          </div>
                        )}
                        {(referral.earnedBonk > 0) && (
                          <div className="flex items-center mt-1">
                            <Award className="w-3 h-3 mr-1" />
                            <span>{referral.earnedBonk.toLocaleString()} BONK</span>
                          </div>
                        )}
                        {(!referral.earnedSol || referral.earnedSol === 0) && 
                          (!referral.earnedFlow || referral.earnedFlow === 0) && 
                          (!referral.earnedBonk || referral.earnedBonk === 0) && (
                          <span className="text-gray-500 text-xs">{t('refer.noEarningsYet')}</span>
                        )}
                      </div>
                      <div className="text-xs px-2 py-0.5 rounded-full bg-blue-500/10 text-blue-400 border border-blue-500/20">
                        {new Date(referral.lastEarned) > new Date(Date.now() - 86400000 * 7) ? t('refer.active') : t('refer.inactive')}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="bg-black/60 backdrop-blur-md rounded-lg border border-gray-800/50 p-6 text-center">
              <p className="text-gray-400 mb-2">{t('refer.noReferralsYet')}</p>
              <p className="text-sm text-gray-500">{t('refer.shareToEarn')}</p>
            </div>
          )}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          <ReferTasks />
        </motion.div>
      )}
    </div>
  )
}

export default Refer