{"app": {"name": "Flow Trade", "description": "Advanced crypto trading bot with algorithmic strategies"}, "language": {"saved": "Saved"}, "nav": {"home": "Home", "wallet": "Wallet", "trading": "Trading", "referrals": "Referrals", "profile": "Profile", "missions": "Missions", "news": "News"}, "wallet": {"title": "Wallet", "terms": "Terms", "totalAssets": "Total Assets", "connectWallet": "Connect Wallet", "walletConnected": "Wallet Connected", "invest": "Invest", "withdraw": "Withdraw", "assets": "Assets", "tutorial": "Tutorial", "airdropInfo": "Airdrop Info", "faqs": "FAQs", "connecting": "Connecting..."}, "withdraw": {"title": "Withdraw <PERSON>sets", "history": "Withdrawal History", "backToWithdrawal": "Back to Withdrawal", "walletAddress": "Wallet Address", "enterWalletAddress": "Enter your wallet address", "currency": "<PERSON><PERSON><PERSON><PERSON>", "availableBalance": "Available Balance", "amountToWithdraw": "Amount to Withdraw", "max": "MAX", "withdrawButton": "Withdraw", "processing": "Processing...", "minimumWithdrawal": "Minimum withdrawal", "viewHistory": "View History", "noHistoryFound": "No withdrawal history found", "needWalletConnection": "You need to connect a wallet before you can withdraw assets.", "successful": "<PERSON><PERSON><PERSON> Successful", "requestSubmitted": "Your withdrawal request has been submitted successfully."}, "refer": {"title": "Refer & Earn", "creatorProgram": "Creator Program", "referrals": "REFERRALS", "solEarned": "SOL EARNED", "flowEarned": "FLOW EARNED", "bonkEarned": "BONK EARNED", "forInvitations": "For Invitations", "yourReferralLink": "Your Referral Link", "shareViaTelegram": "Share via Telegram", "shareTitle": "Join me on FLOW TRADE!", "shareText": "Use my referral link to sign up:", "referralsTab": "Referrals", "tasksTab": "Tasks", "yourReferrals": "Your Referrals", "noReferralsYet": "You haven't referred anyone yet", "shareToEarn": "Share your referral link to start earning rewards!", "noEarningsYet": "No earnings yet", "active": "Active", "inactive": "Inactive"}, "referTasks": {"referralRewards": "Referral Rewards", "inviteFriends": "Invite friends to earn BONK tokens", "refresh": "Refresh", "dataRefreshed": "Referral data refreshed", "refreshFailed": "Failed to refresh data", "loginRequired": "You must be logged in to claim rewards", "claimFailed": "Failed to claim reward", "claimSuccess": "{amount} {currency} claimed successfully!", "errorClaimingReward": "Error claiming reward. Please try again.", "referralCount": "{current}/{required} Referrals", "claimed": "Claimed", "claimReward": "<PERSON><PERSON><PERSON>", "needMoreReferrals": "Need More Referrals", "noTasksAvailable": "No referral tasks available", "checkBackLater": "Check back later for new reward opportunities"}, "referralTasks": {"rt-001": {"title": "Invite 5 Friends", "description": "Invite 5 new users to claim 10,000 BONK"}, "rt-002": {"title": "Invite 10 Friends", "description": "Invite 10 new users to claim 20,000 BONK"}, "rt-003": {"title": "Invite 25 Friends", "description": "Invite 25 new users to claim 50,000 BONK"}, "rt-004": {"title": "Invite 50 Friends", "description": "Invite 50 new users to claim 100,000 BONK"}, "rt-005": {"title": "Invite 100 Friends", "description": "Invite 100 new users to claim 200,000 BONK"}, "rt-006": {"title": "Invite 250 Friends", "description": "Invite 250 new users to claim 500,000 BONK"}, "rt-007": {"title": "Invite 500 Friends", "description": "Invite 500 new users to claim 1,000,000 BONK"}, "rt-008": {"title": "Invite 1000 Friends", "description": "Invite 1000 new users to claim 2,000,000 BONK"}}, "content": {"creators": "Creators", "backToHome": "Back to Home", "contentCreator": "Content Creator", "rewardsProgram": "Rewards Program", "about": "About", "aboutDescription": "Create and share content related to Flow Trade, like videos, short movies, memes, animations, charts, and tutorials. Your creativity can shine in any format.", "earnRewards": "Earn rewards for high-quality content that promotes Flow Trade", "submitYourContent": "Submit Your Content", "linkLabel": "Link to your content (Twitter, YouTube, Instagram, etc.):", "linkPlaceholder": "https://twitter.com/yourusername/status/123456789", "pendingSubmissionWarning": "You already have a pending submission. Please wait for review before submitting again.", "submitting": "Submitting...", "submitContent": "Submit Content", "yourSubmissions": "Your Submissions", "submittedOn": "Submitted on", "reward": "<PERSON><PERSON>", "note": "Note", "noSubmissionsYet": "You haven't submitted any content yet.", "guidelines": "Guidelines", "guideline1": {"title": "Content Focus", "description": "Ensure Flow Trade is prominently mentioned or featured in your creation."}, "guideline2": {"title": "Originality", "description": "Content must be original and created by you. Plagiarism will result in rejection."}, "guideline3": {"title": "Quality", "description": "High-quality content with clear audio/video, proper grammar, and professional presentation."}, "guideline4": {"title": "Accuracy", "description": "Information must be accurate and not misleading. No false promises or claims."}, "status": {"pending": "pending", "approved": "approved", "rejected": "rejected"}, "fetchFailed": "Failed to fetch submissions", "validLinkRequired": "Please enter a valid content link", "submitFailed": "Failed to submit content", "submitSuccess": "Your content has been submitted successfully!", "connectWalletWarning": "Please connect your wallet before submitting content.", "walletConnected": "Wallet Connected", "walletAddressInfo": "Submissions will be linked to your wallet address", "walletRequired": "Please connect your wallet to submit content"}, "news": {"articles": "Articles", "hotTopics": "Hot Topics", "loadingArticles": "Loading latest articles...", "errorLoading": "Failed to load news articles. Please try again.", "tryAgain": "Try Again", "noArticlesFound": "No articles found", "loading": "Loading...", "loadMore": "Load More", "fetchFailed": "Failed to fetch news articles", "backToArticles": "Back to Articles", "unknownDate": "Unknown date", "openInTelegramToInteract": "Please open in Telegram to interact", "waitForAuthentication": "Please wait while we authenticate you", "failedToUpdateLikeStatus": "Failed to update like status", "alreadyLiked": "You've already liked this article", "linkCopied": "Link copied!", "processing": "Processing...", "liked": "Liked", "like": "Like", "share": "Share"}, "missions": {"title": "Missions", "tasks": "Tasks", "partnerRewards": "Partner Rewards", "special": "Special", "telegramRequired": "Please Open App Via Telegram to Start Missions.", "telegramRequiredClaim": "Please open via telegram app to claim this reward", "loading": "Loading...", "missionStarted": "Mission started! Verification Pending : {minutes}.", "partnerMissionStarted": "Partner mission started! Verification Pending.", "verificationInProgress": "Mission verification in progress. Please wait till verification.", "alreadyCompleted": "You have already completed this mission.", "failedToStart": "Failed to start mission", "somethingWentWrong": "Something went wrong", "networkError": "Network error", "failedToComplete": "Failed to complete mission", "missionCompleted": "Mission completed! You earned {amount} {type}", "noMissionsAvailable": "No missions available at the moment.", "noPartnerRewardsAvailable": "No partner rewards available at the moment.", "estimatedVerificationTime": "Estimated Verification Time", "completed": "Completed", "claimReward": "<PERSON><PERSON><PERSON>", "continueTask": "Continue Task", "continuePartnerTask": "Continue Partner Task", "startTask": "Start Task", "visitPartner": "Visit Partner", "enterPromoCode": "Please enter a promo code", "promoCodeSubmitted": "Promo code submitted! 500 tokens earned!", "enterPromoCodeTitle": "Enter Promo Code", "redeemSpecialRewards": "Redeem special rewards and tokens", "enterCodeHere": "Enter code here", "submit": "Submit"}}