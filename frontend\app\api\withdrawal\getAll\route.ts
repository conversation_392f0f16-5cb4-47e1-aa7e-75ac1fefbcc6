import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { User } from '@/libs/model/user.schema';
import { Withdrawal, WithdrawalStatus, WithdrawalCurrency } from '@/libs/model/withdrawal.schema';

export async function GET(req: NextRequest) {
  try {
      
    // Get URL parameters for filtering
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Max 50 items per page
    const status = searchParams.get('status') || '';
    const currency = searchParams.get('currency') || '';
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';
    
    // Build filter
    const filter: any = {};
  
    
    // Add status filter if provided
    if (status && Object.values(WithdrawalStatus).includes(status as WithdrawalStatus)) {
      filter.status = status;
    }
    
    // Add currency filter if provided
    if (currency && Object.values(WithdrawalCurrency).includes(currency as WithdrawalCurrency)) {
      filter.currency = currency;
    }
    
    // Add date range filter if provided
    if (startDate || endDate) {
      filter.createdAt = {};
      
      if (startDate) {
        filter.createdAt.$gte = new Date(startDate);
      }
      
      if (endDate) {
        // Set time to end of day
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        filter.createdAt.$lte = endDateTime;
      }
    }
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const withdrawals = await Withdrawal.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('user', 'telegramId telegramUsername telegramName displayName');
    
    // Get total count for pagination
    const total = await Withdrawal.countDocuments(filter);
    const pages = Math.ceil(total / limit);
    
    // Return success response
    return NextResponse.json({
      success: true,
      data: withdrawals,
      pagination: {
        page,
        limit,
        total,
        pages,
      }
    });
    
  } catch (error) {
    console.error('Error fetching withdrawals:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch withdrawals' },
      { status: 500 }
    );
  }
}
