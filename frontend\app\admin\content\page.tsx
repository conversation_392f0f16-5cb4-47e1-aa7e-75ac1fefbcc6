"use client"

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { toast } from 'react-hot-toast'
import { 
  Check, 
  X, 
  ExternalLink, 
  Loader2, 
  ChevronLeft, 
  ChevronRight, 
  Search,
  RotateCcw
} from 'lucide-react'

interface Submission {
  _id: string;
  contentLink: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  updatedAt: string;
  reviewedAt?: string;
  rewardAmount: number;
  rewardCurrency: string;
  reviewNote?: string;
  platform?: string;
  user: {
    _id: string;
    displayName: string;
    telegramUsername?: string;
    telegramId?: string;
    wallet?: {
      balance?: {
        sol?: number;
        flow?: number;
        bonk?: number;
      }
    }
  } | null;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface ReviewData {
  status: 'pending' | 'approved' | 'rejected';
  rewardAmount: number;
  rewardCurrency: 'sol' | 'flow' | 'bonk';
  reviewNote: string;
  txHash?: string;
  manualPayment: boolean;
}

export default function ContentPage() {
  const [submissions, setSubmissions] = useState<Submission[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  })
  const [statusFilter, setStatusFilter] = useState<string | null>(null)
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null)
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false)
  const [reviewData, setReviewData] = useState<ReviewData>({
    status: 'pending',
    rewardAmount: 0,
    rewardCurrency: 'bonk',
    reviewNote: '',
    txHash: '',
    manualPayment: true
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // Fetch submissions
  const fetchSubmissions = async () => {
    try {
      setIsLoading(true)
      const queryParams = new URLSearchParams({
        page: String(pagination.page),
        limit: String(pagination.limit),
      })

      if (statusFilter) {
        queryParams.append('status', statusFilter)
      }

      if (searchQuery) {
        queryParams.append('query', searchQuery)
      }

      const response = await fetch(`/api/admin/content-submissions?${queryParams.toString()}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch submissions')
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch submissions')
      }

      setSubmissions(data.data.submissions)
      setPagination(data.data.pagination)
    } catch (error: any) {
      console.error('Error fetching submissions:', error)
      toast.error(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  // Load submissions on mount and when filters change
  useEffect(() => {
    fetchSubmissions()
  }, [pagination.page, statusFilter])

  // Format date function
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    })
  }

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  // Handle review click
  const handleReviewClick = (submission: Submission) => {
    setSelectedSubmission(submission)
    setReviewData({
      status: submission.status,
      rewardAmount: submission.rewardAmount || 0,
      rewardCurrency: (submission.rewardCurrency?.toLowerCase() as 'sol' | 'flow' | 'bonk') || 'bonk',
      reviewNote: submission.reviewNote || '',
      txHash: '',
      manualPayment: true
    })
    setIsReviewModalOpen(true)
  }

  // Handle review submission
  const handleReviewSubmit = async () => {
    if (!selectedSubmission) return
    
    // Validate transaction hash is provided for approved submissions
    if (reviewData.status === 'approved' && !reviewData.txHash) {
      toast.error('Transaction hash is required for approved submissions')
      return
    }
    
    try {
      setIsSubmitting(true)
      
      // Ensure currency is lowercase
      const submitData = {
        ...reviewData,
        rewardCurrency: reviewData.rewardCurrency.toLowerCase(),
        manualPayment: true // Always true as we're handling payments manually
      };
      
      console.log('Submitting data:', submitData);
      
      const response = await fetch('/api/admin/content-submissions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          submissionId: selectedSubmission._id,
          ...submitData,
        }),
      })
      
      const data = await response.json()
      
      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to update submission')
      }
      
      toast.success(`Submission ${reviewData.status === 'approved' ? 'approved' : reviewData.status === 'rejected' ? 'rejected' : 'updated'}`)
      setIsReviewModalOpen(false)
      fetchSubmissions()
    } catch (error: any) {
      console.error('Error updating submission:', error)
      toast.error(error.message)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
    fetchSubmissions()
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Content Submissions</h2>
          <p className="text-muted-foreground">
            Review and approve user-submitted content
          </p>
        </div>
      </div>
      
      <div className="my-6 h-[1px] bg-border" />
      
      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="flex-1 max-w-md">
          <form onSubmit={handleSearch} className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search by link or user..."
              className="w-full rounded-md border border-input bg-background pl-8 pr-10 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button 
              type="submit" 
              variant="ghost" 
              size="sm" 
              className="absolute right-0 top-0 h-full px-3"
            >
              Search
            </Button>
          </form>
        </div>
        
        <select
          value={statusFilter || ''}
          onChange={(e) => {
            setStatusFilter(e.target.value || null)
            setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
          }}
          className="rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
        >
          <option value="">All Statuses</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="rejected">Rejected</option>
        </select>
        
        <Button 
          variant="outline" 
          size="icon"
          onClick={() => {
            setSearchQuery('')
            setStatusFilter(null)
            setPagination(prev => ({ ...prev, page: 1 }))
            fetchSubmissions()
          }}
          title="Reset filters"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Content Submissions Table */}
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : submissions.length > 0 ? (
        <div className="rounded-md border">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">User</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Link</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Platform</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Submitted</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Reward</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {submissions.map((submission) => (
                  <tr key={submission._id} className="hover:bg-muted/50">
                    <td className="px-4 py-3 text-sm">
                      <div>
                        <div className="font-medium">
                          {submission.user?.displayName || 'Unknown User'}
                        </div>
                        {submission.user?.telegramUsername && (
                          <div className="text-xs text-muted-foreground">
                            @{submission.user.telegramUsername}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <a 
                        href={submission.contentLink} 
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className="flex items-center text-blue-600 hover:underline"
                      >
                        <span className="truncate max-w-[200px]">{submission.contentLink}</span>
                        <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
                      </a>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {submission.platform || 'Unknown'}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {formatDate(submission.submittedAt)}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        submission.status === 'approved' 
                          ? 'bg-green-100 text-green-800' 
                          : submission.status === 'rejected'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {submission.status}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {submission.status === 'approved' && submission.rewardAmount > 0 
                        ? `${submission.rewardAmount} ${submission.rewardCurrency}`
                        : '-'
                      }
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewClick(submission)}
                      >
                        Review
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex items-center justify-center space-x-2 py-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">
                Page {pagination.page} of {pagination.pages}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.pages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <p className="text-muted-foreground mb-4">No content submissions found</p>
          <Button 
            variant="outline" 
            onClick={() => {
              setSearchQuery('')
              setStatusFilter(null)
              fetchSubmissions()
            }}
          >
            Reset Filters
          </Button>
        </div>
      )}
      
      {/* Review Modal */}
      {isReviewModalOpen && selectedSubmission && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg bg-white shadow-lg w-full max-w-md p-6 m-4">
            <h3 className="text-xl font-bold mb-4">Review Content Submission</h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Content Link</label>
              <a 
                href={selectedSubmission.contentLink} 
                target="_blank" 
                rel="noopener noreferrer" 
                className="flex items-center text-blue-600 hover:underline text-sm"
              >
                <span className="truncate">{selectedSubmission.contentLink}</span>
                <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
              </a>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Status</label>
              <select
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm focus-visible:outline-none"
                value={reviewData.status}
                onChange={(e) => setReviewData({
                  ...reviewData,
                  status: e.target.value as 'pending' | 'approved' | 'rejected'
                })}
                disabled={selectedSubmission.status !== 'pending'}
              >
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
              {selectedSubmission.status !== 'pending' && (
                <p className="text-xs text-muted-foreground mt-1">
                  Status cannot be changed once reviewed
                </p>
              )}
            </div>
            
            {reviewData.status === 'approved' && (
              <>
                {/* User Wallet Information */}
                <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">User Wallet Information</h4>
                  <div className="text-xs space-y-1">
                    <p className="text-blue-700">
                      <span className="font-medium">SOL:</span> {selectedSubmission.user?.wallet?.balance?.sol || 0}
                    </p>
                    <p className="text-blue-700">
                      <span className="font-medium">FLOW:</span> {selectedSubmission.user?.wallet?.balance?.flow || 0}
                    </p>
                    <p className="text-blue-700">
                      <span className="font-medium">BONK:</span> {selectedSubmission.user?.wallet?.balance?.bonk || 0}
                    </p>
                  </div>
                </div>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">Reward Amount</label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm focus-visible:outline-none"
                    value={reviewData.rewardAmount}
                    onChange={(e) => setReviewData({
                      ...reviewData,
                      rewardAmount: parseFloat(e.target.value) || 0
                    })}
                    disabled={selectedSubmission.status !== 'pending'}
                  />
                </div>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">Reward Currency</label>
                  <select
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm focus-visible:outline-none"
                    value={reviewData.rewardCurrency}
                    onChange={(e) => {
                      const selectedCurrency = e.target.value.toLowerCase() as 'sol' | 'flow' | 'bonk';
                      setReviewData({
                        ...reviewData,
                        rewardCurrency: selectedCurrency
                      });
                    }}
                    disabled={selectedSubmission.status !== 'pending'}
                  >
                    <option value="sol">SOL</option>
                    <option value="flow">FLOW</option>
                    <option value="bonk">BONK</option>
                  </select>
                </div>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-1">
                    Transaction Hash <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm focus-visible:outline-none"
                    value={reviewData.txHash || ''}
                    onChange={(e) => setReviewData({
                      ...reviewData,
                      txHash: e.target.value
                    })}
                    placeholder="Enter transaction hash (required)"
                    disabled={selectedSubmission.status !== 'pending'}
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Transaction hash is required for manual payments
                  </p>
                </div>
              </>
            )}
            
            <div className="mb-6">
              <label className="block text-sm font-medium mb-1">Review Note</label>
              <textarea
                className="w-full rounded-md border border-input bg-background px-3 py-2 min-h-[80px] text-sm focus-visible:outline-none"
                placeholder={reviewData.status === 'rejected' ? 'Explain why the content was rejected...' : 'Optional notes about the content...'}
                value={reviewData.reviewNote}
                onChange={(e) => setReviewData({
                  ...reviewData,
                  reviewNote: e.target.value
                })}
                disabled={selectedSubmission.status !== 'pending'}
              />
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setIsReviewModalOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              
              <Button
                onClick={handleReviewSubmit}
                disabled={isSubmitting || selectedSubmission.status !== 'pending'}
                className={
                  reviewData.status === 'approved' 
                    ? 'bg-green-600 hover:bg-green-700 text-white' 
                    : reviewData.status === 'rejected'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : ''
                }
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : reviewData.status === 'approved' ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Approve
                  </>
                ) : reviewData.status === 'rejected' ? (
                  <>
                    <X className="mr-2 h-4 w-4" />
                    Reject
                  </>
                ) : (
                  'Save'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}