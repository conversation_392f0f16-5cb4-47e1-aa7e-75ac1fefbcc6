# Telegram Mini App with Next.js 15

This project demonstrates how to create a Telegram Mini App using Next.js 15. It uses the App Router pattern and shows how to extract Telegram user data, including the chat ID when a user opens your Mini App.

## Features

- Integration with Telegram Web App SDK
- User authentication via Telegram
- Chat ID extraction
- API endpoint for communication with your backend
- Responsive UI optimized for Telegram

## Prerequisites

- Node.js 18+ and npm
- A Telegram Bot (create one using [@BotFather](https://t.me/BotFather))
- Basic knowledge of Next.js and React

## Getting Started

1. Clone this repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Run the development server:
   ```bash
   npm run dev
   ```
4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Setting Up Your Telegram Bot

1. Talk to [@BotFather](https://t.me/BotFather) on Telegram
2. Send `/newbot` and follow the instructions to create a new bot
3. After creating the bot, set up a Mini App:
   - Send `/mybots` to Bot<PERSON>ather
   - Select your bot
   - Go to "Bot Settings" > "Menu Button" or use the `/setmenubutton` command
   - Set a menu button that links to your deployed Mini App URL

Alternatively, to set up a game that can pass chat_id:
1. Talk to BotFather and send `/newgame`
2. Follow instructions to create a game for your bot
3. Use the `/setgameshortname` command to set a short name
4. Use `/setgameurl` to set the URL of your deployed Mini App

## Deploying Your Mini App

1. Build your app:
   ```bash
   npm run build
   ```
2. Deploy to your preferred hosting service (Vercel, Netlify, etc.)
3. Update your bot's menu button or game URL to point to your deployed app

## How It Works

When a user opens your Mini App from within Telegram:

1. The Telegram WebApp SDK initializes and provides user data
2. If opened from a game, the chat ID is available in the initData
3. Your app can then use this information to make authenticated requests to your backend

## Project Structure

- `/app`: Main application code (Next.js App Router)
  - `/components`: React components
  - `/context`: Context providers for state management
  - `/utils`: Utility functions for Telegram integration
  - `/api`: API routes for backend communication

## Further Development

- Add server-side authentication by validating Telegram's init data hash
- Implement more features like payments, notifications, etc.
- Add support for Telegram's theme variables for better UI integration

## Resources

- [Telegram Mini Apps Documentation](https://core.telegram.org/bots/webapps)
- [Next.js Documentation](https://nextjs.org/docs)
- [Telegram Bot API](https://core.telegram.org/bots/api)
