import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { News } from '@/libs/model/news.schema';
import slugify from 'slugify';

// Local NewsCategory enum to match the one defined in CreateNews.tsx
enum NewsCategory {
  MARKET = 'market',
  TECHNOLOGY = 'technology',
  DEFI = 'defi',
  TRADING = 'trading',
  GENERAL = 'general',
}

export async function POST(req: NextRequest) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Parse request body
    const body = await req.json();
    
    // Validate required fields
    const { title, summary, content, category, authorName, image, isPublished, isHighlighted } = body;
    
    if (!title || !summary || !content || !category || !authorName || !image) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Validate category
    if (!Object.values(NewsCategory).includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      );
    }
    
    // Generate slug from title
    const baseSlug = slugify(title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g,
    });
    
    // Check if slug already exists
    let slug = baseSlug;
    let slugExists = await News.findOne({ slug });
    let counter = 1;
    
    // If slug exists, append a number until we find a unique slug
    while (slugExists) {
      slug = `${baseSlug}-${counter}`;
      slugExists = await News.findOne({ slug });
      counter++;
    }
    
    // Create new news article
    const news = new News({
      title,
      slug,
      summary,
      content,
      category,
      authorName,
      image,
      isPublished: Boolean(isPublished),
      isHighlighted: Boolean(isHighlighted),
      publishedAt: isPublished ? new Date() : null,
    });
    
    // Save to database
    await news.save();
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'News article created successfully',
      data: {
        id: news._id,
        title: news.title,
        slug: news.slug,
      }
    }, { status: 201 });
    
  } catch (error) {
    console.error('Error creating news article:', error);
    
    return NextResponse.json(
      { error: 'Failed to create news article' },
      { status: 500 }
    );
  }
}
