import { Document, Schema } from 'mongoose';

// News category types
export enum NewsCategory {
  MARKET = 'market',
  TECHNOLOGY = 'technology',
  DEFI = 'defi',
  TRADING = 'trading',
  GENERAL = 'general',
}

// User interaction interface for tracking likes and views
export interface IUserInteraction {
  userId: Schema.Types.ObjectId;
  timestamp: Date;
}

// News article interface for server-side (Mongoose)
export interface INews extends Document {
  title: string;
  slug: string;
  summary: string;
  content: string;
  image: string;
  category: NewsCategory;
  authorName: string;
  likes: IUserInteraction[];
  views: IUserInteraction[];
  likesCount: number;
  viewsCount: number;
  isPublished: boolean;
  isHighlighted: boolean;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Client-side news article type
export interface NewsArticle {
  _id: string;
  title: string;
  slug: string;
  summary: string;
  content: string;
  category: string;
  authorName: string;
  image: string;
  isPublished: boolean;
  isHighlighted: boolean;
  likesCount?: number;
  viewsCount?: number;
  publishedAt?: string | null;
  createdAt?: string;
  likes?: any[];
  userLiked?: boolean;
}

// Client-side news response type
export interface NewsResponse {
  success: boolean;
  data: NewsArticle[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
