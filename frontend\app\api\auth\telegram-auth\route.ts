import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, withDatabaseRetry } from '@/libs/db';
import { User } from '@/libs/model/user.schema';
import { UserRole, UserStatus } from '@/types/user';

export async function POST(req: NextRequest) {
  try {
    // Get the telegram data from request body
    const body = await req.json();
    const { telegramId, telegramData, chatData, referralCode } = body;
    // const {  telegramData, chatData, referralCode } = body;
    // const telegramId = 5195131141;    
    if (!telegramId) {
      return NextResponse.json(
        { success: false, error: 'Telegram ID is required' },
        { status: 400 }
      );
    }
    
    try {
      // Connect to database - wrapped in withDatabaseRetry for resilience
      await connectToDatabase();
    
      // Find existing user with retry logic
      let user = await withDatabaseRetry(async () => {
        return await User.findOne({ telegramId });
      });
      
      let isNewUser = false;
      
      // Create user if not found
      if (!user) {
        isNewUser = true;
        console.log(`Creating new user for telegramId: ${telegramId}`);
        
        // Check if there's a referral
        let referredBy = null;
        if (referralCode) {
          try {
            // The referralCode is directly the user ID (no need for prefixes)
            // We get this from:
            // 1. Telegram's startapp parameter
            // 2. Our start URL parameter for testing
            referredBy = await withDatabaseRetry(async () => {
              return await User.findById(referralCode);
            });
            
            if (referredBy) {
              console.log(`New user ${telegramId} referred by ${referredBy.telegramId}`);
            } else {
              console.log(`Could not find user with ID: ${referralCode} for referral`);
            }
          } catch (e) {
            console.error('Invalid referral code format:', referralCode, e);
          }
        }
        
        // Create a new user from Telegram data
        user = new User({
          telegramId,
          telegramUsername: telegramData.username,
          telegramName: `${telegramData.first_name || ''} ${telegramData.last_name || ''}`.trim(),
          displayName: telegramData.username || telegramData.first_name || 'User',
          telegramLanguage: telegramData.language_code,
          telegramPhotoUrl: telegramData.photo_url,
          telegramChatId: chatData?.id,
          telegramChatType: chatData?.type,
          role: UserRole.USER,
          status: UserStatus.ACTIVE,
          lastLogin: new Date(),
          wallet: {
            balance: {
              sol: 0,
              flow: 0,
              bonk: 0,
            }
          },
          completedMissions: [],
          likedNews: [],
          referredBy: referredBy ? referredBy._id : null,
          referrals: [],
          referralStats: {
            totalReferrals: 0,
            rewardsSol: 0,
            rewardsFlow: 0,
            rewardsBonk: 0
          }
        });
        
        // Save the new user with retry logic
        await withDatabaseRetry(async () => {
          await user.save();
        });
        
        console.log(`New user created: ${user.displayName} (${telegramId})`);
        
        // Update referrer's stats with retry logic
        if (referredBy) {
          try {
            // Track referral - add to the referrer's referrals list
            await withDatabaseRetry(async () => {
              await User.findByIdAndUpdate(referredBy._id, {
                $push: { 
                  referrals: {
                    user: user._id,
                    earnedSol: 0,
                    earnedFlow: 0,
                    earnedBonk: 0,
                    joinedAt: new Date(),
                    lastEarned: new Date()
                  }
                },
                $inc: { 
                  'referralStats.totalReferrals': 1,
                }
              });
            });
            
            console.log(`Updated referrer stats for ${referredBy.telegramId}`);
          } catch (error) {
            console.error('Error updating referrer stats:', error);
          }
        }
      } else {
        // Update existing user with latest Telegram data
        console.log(`Updating existing user: ${telegramId}`);
        
        user.telegramUsername = telegramData.username || user.telegramUsername;
        user.telegramName = `${telegramData.first_name || ''} ${telegramData.last_name || ''}`.trim() || user.telegramName;
        user.displayName = user.displayName || telegramData.username || telegramData.first_name || 'User';
        user.telegramLanguage = telegramData.language_code || user.telegramLanguage;
        user.telegramPhotoUrl = telegramData.photo_url || user.telegramPhotoUrl;
        
        // Update chat data if provided
        if (chatData?.id) {
          user.telegramChatId = chatData.id;
          user.telegramChatType = chatData.type || 'unknown';
        }
        
        user.lastLogin = new Date();
        
        // Save the updated user with retry logic
        await withDatabaseRetry(async () => {
          await user.save();
        });
        
        console.log(`User updated: ${user.displayName} (${telegramId})`);
      }
            
      // Set auth cookie
      const cookieOptions = {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        path: '/'
      };
      
      // Get complete user with detailed referral information
      const fullUser = await User.findById(user?._id)
        .populate({
          path: 'referredBy',
          select: 'telegramId telegramUsername telegramName displayName telegramPhotoUrl'
        })
        .populate({
          path: 'referrals.user',
          select: 'telegramId telegramUsername telegramName displayName telegramPhotoUrl'
        });
      
      const response = NextResponse.json({
        success: true,
        user: fullUser,
        isNewUser
      });
      
      // Set userId cookie for future requests
      response.cookies.set('userId', user._id.toString(), cookieOptions as any);
      
      return response;
    } catch (dbError) {
      console.error('Database operation failed:', dbError);
      return NextResponse.json(
        { success: false, error: 'Database operation failed' },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return NextResponse.json(
      { success: false, error: 'Authentication failed' },
      { status: 500 }
    );
  }
} 