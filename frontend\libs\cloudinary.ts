import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: 'dd8dbon<PERSON>',
  api_key: '649285876266699',
  api_secret: 'TG-6OVcv6aEqsTmG2OsNIhMyr8c',
});

/**
 * Upload a file to Cloudinary
 * @param file The file to upload (base64 string or file buffer)
 * @param folder The folder to upload to
 * @returns The uploaded file URL and public ID
 */
export const uploadImage = async (file: string, folder: string = 'discord') => {
  try {
    const result = await cloudinary.uploader.upload(file, {
      folder: folder,
    });
    
    return {
      url: result.secure_url,
      publicId: result.public_id,
    };
  } catch (error) {
    console.error('Error uploading image to Cloudinary:', error);
    throw new Error('Failed to upload image');
  }
};

export default cloudinary;
