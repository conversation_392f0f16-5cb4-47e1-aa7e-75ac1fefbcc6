import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: true,
  poweredByHeader: false, // Hide the X-Powered-By header
  // Define allowed image hosts
  images: {
    domains: ['avatars.githubusercontent.com', 'cdn.botframework.com', 't.me', 'res.cloudinary.com'],
  },
  // Add Content Security Policy for Telegram WebApp integration
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            // Very permissive CSP to allow Google Translate to work properly
            value: "default-src * 'unsafe-inline' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline';"
          },
          {
            // Change referrer policy to be more permissive for Google Translate
            key: 'Referrer-Policy',
            value: 'no-referrer-when-downgrade'
          }
        ]
      }
    ];
  },
  // Fix for handling client-side only libraries
  webpack: (config) => {
    config.resolve.fallback = { 
      ...config.resolve.fallback,
      fs: false 
    };
    
    return config;
  },
  // Use these transpilation settings
  transpilePackages: ['@twa-dev/sdk'],
};

export default nextConfig;
