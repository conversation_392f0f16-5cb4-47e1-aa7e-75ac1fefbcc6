'use client'

import { useState, useCallback, useEffect } from 'react'
import { useWallet } from '@/context/SolanaWalletContext'
import { useLanguage } from '@/context/LanguageContext'

interface WalletConnectButtonProps {
  compact?: boolean;
}

export const WalletConnectButton = ({ compact = false }: WalletConnectButtonProps) => {
  const { publicKey, connected, isLoading, error, connect, disconnect } = useWallet()
  const [copied, setCopied] = useState(false)
  const { t } = useLanguage()
  const [isInitialized, setIsInitialized] = useState(false)

  // Handle initial connection state
  useEffect(() => {
    if (!isInitialized && connected && publicKey) {
      setIsInitialized(true)
    }
  }, [connected, publicKey, isInitialized])

  const handleCopyAddress = useCallback(() => {
    if (publicKey) {
      navigator.clipboard.writeText(publicKey)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }, [publicKey])

  const handleConnect = useCallback(async () => {
    try {
      await connect()
    } catch (error) {
      console.error('Connection error:', error)
    }
  }, [connect])

  const handleDisconnect = useCallback(async () => {
    try {
      await disconnect()
    } catch (error) {
      console.error('Disconnection error:', error)
    }
  }, [disconnect])

  if (compact) {
    return (
      <div>
        {error && (
          <div className="text-red-500 text-xs mb-2">
            {error}
          </div>
        )}
        
        {!connected ? (
          <button
            onClick={handleConnect}
            disabled={isLoading}
            className="py-2 px-3 rounded-md text-sm font-medium flex items-center justify-center transition-colors bg-green-600 hover:bg-green-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>{t('wallet.connecting')}</span>
              </div>
            ) : (
              t('wallet.connectWallet')
            )}
          </button>
        ) : (
          <button
            onClick={handleDisconnect}
            disabled={isLoading}
            className="py-2 px-3 rounded-md text-sm font-medium flex items-center justify-center transition-colors bg-gray-800 hover:bg-gray-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              "Disconnect"
            )}
          </button>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-2">
      {error && (
        <div className="text-red-500 text-sm mb-2">
          {error}
        </div>
      )}
      
      {!connected ? (
        <button
          onClick={handleConnect}
          disabled={isLoading}
          className="w-full py-3 px-4 rounded-xl font-medium flex items-center justify-center transition-colors bg-green-500 hover:bg-green-600 text-black disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin" />
              <span>{t('wallet.connecting')}</span>
            </div>
          ) : (
            t('wallet.connectWallet')
          )}
        </button>
      ) : (
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between bg-blue-500/20 border-[0.3px] border-blue-400 p-3 rounded-xl">
            <div className="flex items-center gap-2">
              <span className="text-sm text-blue-300">
                {publicKey ? `${publicKey.slice(0, 4)}...${publicKey.slice(-4)}` : ''}
              </span>
              <button
                onClick={handleCopyAddress}
                className="text-blue-400 hover:text-gray-200 transition-colors"
                title="Copy address"
              >
                {copied ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                )}
              </button>
            </div>
            <button
              onClick={handleDisconnect}
              disabled={isLoading}
              className="text-red-400 hover:text-red-300 transition-colors disabled:opacity-50"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-red-400 border-t-transparent rounded-full animate-spin" />
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  )
} 