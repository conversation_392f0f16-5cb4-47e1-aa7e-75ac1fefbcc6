
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Zap, Award, Star, Calendar } from 'lucide-react';

interface FlowPointsSectionProps {
  scrollY: number;
}

const FlowPointsSection: React.FC<FlowPointsSectionProps> = ({ scrollY }) => {
  const rewards = [
    { icon: Zap, title: "Future $FLOW Token Bonuses", desc: "Early access to token rewards" },
    { icon: Star, title: "Exclusive Whitelists", desc: "Priority access to new meme launches" },
    { icon: Award, title: "Premium Zap Perks", desc: "Enhanced yield rates and bonuses" },
    { icon: Calendar, title: "Early Access Features", desc: "Beta test new platform features first" },
  ];

  return (
    <section id="flow-points" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-gray-900/50 backdrop-blur-xl text-green-400 border border-green-500/20 font-medium">
            ⚡ Flow Points Utility
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 font-syne">
            <span className="bg-gradient-to-r from-white to-green-400 bg-clip-text text-transparent">
              Daily Login Streaks
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto">
            Log in every 24 hours to maintain your Flow Streak and earn Flow Points — 
            your key to future upgrades, rewards, and special roles.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div 
            className="relative"
            style={{ transform: `translateY(${scrollY * 0.05}px)` }}
          >
            <Card className="bg-gray-950/50 backdrop-blur-xl border border-green-500/10 p-8 relative overflow-hidden hover:border-green-500/20 transition-all duration-300">
              {/* Glass effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent" />
              
              {/* Animated streak counter */}
              <div className="text-center mb-8 relative z-10">
                <div className="text-6xl font-bold text-green-400 mb-2 animate-pulse font-syne">
                  247
                </div>
                <div className="text-gray-300 text-lg font-medium">Current Streak</div>
                <div className="w-full bg-gray-800/50 rounded-full h-3 mt-4 backdrop-blur-sm">
                  <div className="bg-gradient-to-r from-green-500 to-emerald-400 h-3 rounded-full w-3/4 animate-pulse shadow-lg shadow-green-500/30"></div>
                </div>
              </div>

              {/* Daily rewards */}
              <div className="grid grid-cols-7 gap-2 relative z-10">
                {[...Array(7)].map((_, i) => (
                  <div 
                    key={i}
                    className={`h-8 w-8 rounded-full flex items-center justify-center text-xs font-bold transition-all duration-300 ${
                      i < 5 
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-black shadow-lg shadow-green-500/30 hover:scale-110' 
                        : 'bg-gray-700/50 text-gray-400 backdrop-blur-sm'
                    }`}
                  >
                    {i + 1}
                  </div>
                ))}
              </div>
              <div className="text-center mt-4 text-sm text-gray-400 relative z-10">
                This Week's Login Progress
              </div>
            </Card>
          </div>

          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6 font-syne">Redeem Flow Points For:</h3>
            {rewards.map((reward, index) => (
              <Card 
                key={index}
                className="bg-gray-950/50 backdrop-blur-xl border border-green-500/10 p-6 hover:bg-gray-950/70 transition-all duration-300 hover:border-green-500/20 hover:shadow-xl hover:shadow-green-500/10 group"
                style={{ 
                  transform: `translateX(${Math.sin((scrollY + index * 150) * 0.002) * 8}px)`,
                  animationDelay: `${index * 0.1}s`
                }}
              >
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <reward.icon className="h-10 w-10 text-green-400 group-hover:scale-110 transition-transform duration-300" />
                    <div className="absolute inset-0 bg-green-400/10 blur-lg rounded-full group-hover:bg-green-400/20 transition-all duration-300" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-white group-hover:text-green-300 transition-colors font-syne">
                      {reward.title}
                    </h4>
                    <p className="text-gray-400 text-sm">
                      {reward.desc}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Coming soon section */}
        <div className="mt-16 text-center">
          <Card className="bg-gray-950/30 backdrop-blur-xl p-8 border border-green-500/10 inline-block hover:border-green-500/20 transition-all duration-300">
            <Badge className="mb-4 bg-gray-900/50 backdrop-blur-xl text-green-400 border border-green-500/20 font-medium">
              🟢 Coming Q4 2025
            </Badge>
            <h3 className="text-xl font-bold text-white mb-2 font-syne">Weekly Meme Coin Launches</h3>
            <p className="text-gray-300">
              Users will be able to zap into community-selected meme tokens weekly!
            </p>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default FlowPointsSection;
