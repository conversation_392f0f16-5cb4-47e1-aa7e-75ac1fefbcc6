"use client"

import { usePathname } from 'next/navigation'
import Navigation from '@/components/Navigation'
import { useEffect, useState } from 'react'

// Create a simple pub/sub for loader state
let loaderState = true
const subscribers: Set<(isLoading: boolean) => void> = new Set()

// Exported functions to manage loader state
export function setLoaderState(isLoading: boolean) {
  loaderState = isLoading
  subscribers.forEach(callback => callback(isLoading))
}

export function useLoaderState() {
  const [isLoading, setIsLoading] = useState(loaderState)
  
  useEffect(() => {
    const callback = (state: boolean) => setIsLoading(state)
    subscribers.add(callback)
    return () => { subscribers.delete(callback) }
  }, [])
  
  return isLoading
}

export default function NavigationWrapper() {
  const pathname = usePathname()
  const isAdminPage = pathname.includes('/admin')
  const isLoading = useLoaderState()
  
  if (isAdminPage || isLoading) {
    return null
  }
  
  return <Navigation />
} 