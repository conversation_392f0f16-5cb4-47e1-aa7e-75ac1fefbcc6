import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment, InvestmentStatus } from '@/libs/model/investment.schema';
import { User } from '@/libs/model/user.schema';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';

// Validation response interface
interface WithdrawValidationResponse {
  success: boolean;
  error?: string;
  data?: {
    isValid: boolean;
    investment?: any;
    user?: any;
    validationDetails: {
      amountValid: boolean;
      walletMatches: boolean;
      investmentExists: boolean;
      cooldownMet: boolean;
      claimsRequirementMet: boolean;
      sufficientBalance: boolean;
      timeUntilNextWithdrawal?: number;
      availableAmounts: {
        zapEarnings: number;
        referralEarnings: number;
        totalAvailable: number;
      };
    };
    withdrawalData?: {
      maxWithdrawable: number;
      isCompletingInvestment: boolean;
      projectedStatus: string;
    };
  };
}

export async function POST(req: NextRequest): Promise<NextResponse<WithdrawValidationResponse>> {
  try {
    // Get the user ID from cookies
    const userId = "68380e3e3b44aa85b374c1f0";
    
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Parse request body
    const body = await req.json();
    const { amount, walletAddress, source = 'both' } = body;
    
    // Initialize validation results
    let isValid = true;
    let errors: string[] = [];
    const validationDetails = {
      amountValid: false,
      walletMatches: false,
      investmentExists: false,
      cooldownMet: false,
      claimsRequirementMet: false,
      sufficientBalance: false,
      timeUntilNextWithdrawal: undefined as number | undefined,
      availableAmounts: {
        zapEarnings: 0,
        referralEarnings: 0,
        totalAvailable: 0
      }
    };
    
    // Validate amount
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      errors.push('Valid amount is required');
      isValid = false;
    } else {
      const withdrawalAmount = parseFloat(parseFloat(amount).toFixed(6));
      
      if (withdrawalAmount < 0.05) {
        errors.push('Minimum withdrawal amount is 0.05 SOL');
        isValid = false;
      } else {
        validationDetails.amountValid = true;
      }
    }
    
    // Validate wallet address
    if (!walletAddress || typeof walletAddress !== 'string') {
      errors.push('Valid wallet address is required');
      isValid = false;
    }
    
    // Validate source
    if (!source || !['zap', 'referral', 'both'].includes(source)) {
      errors.push('Valid withdrawal source is required');
      isValid = false;
    }
    
    // Find the user
    const user = await User.findById(userId);
    
    if (!user) {
      errors.push('User not found');
      isValid = false;
    }
    
    // Find the user's active investment
    let investment = null;
    
    if (user) {
      investment = await Investment.findOne({
        user: new Types.ObjectId(userId),
        status: InvestmentStatus.ACTIVE
      });
      
      if (!investment) {
        errors.push('No active investment found');
        isValid = false;
      } else {
        validationDetails.investmentExists = true;
        
        // Check if wallet address matches the investment
        if (investment.walletAddress && investment.walletAddress !== walletAddress) {
          errors.push('Wallet address does not match the investment wallet');
          isValid = false;
        } else {
          validationDetails.walletMatches = true;
        }
      }
    }
    
    // Check withdrawal cooldown (24 hours)
    if (user?.lastWithdrawal) {
      const secondsSinceLastWithdrawal = (new Date().getTime() - user.lastWithdrawal.getTime()) / 1000;
      const cooldownPeriod = 24 * 60 * 60; // 24 hours
      
      if (secondsSinceLastWithdrawal < cooldownPeriod) {
        const remainingSeconds = cooldownPeriod - secondsSinceLastWithdrawal;
        const hoursRemaining = Math.ceil(remainingSeconds / 3600);
        validationDetails.timeUntilNextWithdrawal = remainingSeconds;
        errors.push(`Withdrawal cooldown active. You can withdraw again in ${hoursRemaining} hours`);
        isValid = false;
      } else {
        validationDetails.cooldownMet = true;
      }
    } else {
      validationDetails.cooldownMet = true;
    }
    
    // Check claims requirement for non-referral withdrawals
    if (investment && user && source !== 'referral') {
      if (investment.percentageOfROI !== 250 || source === 'zap' || source === 'both') {
        if (user.claimsSinceWithdrawal < 5) {
          errors.push(`Insufficient claims since last withdrawal. Required: 5, Available: ${user.claimsSinceWithdrawal}`);
          isValid = false;
        } else {
          validationDetails.claimsRequirementMet = true;
        }
      } else {
        validationDetails.claimsRequirementMet = true;
      }
    } else {
      validationDetails.claimsRequirementMet = true;
    }
    
    // Calculate available amounts and validate balance
    if (investment && user) {
      const amountWithdrawable = investment.earnedAmount - investment.withdrawnAmount;
      const walletZapAmount = user.wallet?.balance?.sol || 0;
      const referralAmount = user.referralStats?.rewardsSol || 0;
      
      validationDetails.availableAmounts = {
        zapEarnings: walletZapAmount,
        referralEarnings: referralAmount,
        totalAvailable: walletZapAmount + referralAmount
      };
      
      // Validate based on withdrawal source
      const withdrawalAmount = parseFloat(amount);
      
      switch (source) {
        case 'zap':
          if (amountWithdrawable <= 0) {
            errors.push('No zap earnings available to withdraw');
            isValid = false;
          } else if (withdrawalAmount > amountWithdrawable) {
            errors.push('Insufficient zap earnings available to withdraw');
            isValid = false;
          } else if (withdrawalAmount > walletZapAmount) {
            errors.push('Insufficient wallet balance available to withdraw');
            isValid = false;
          } else {
            validationDetails.sufficientBalance = true;
          }
          break;
          
        case 'referral':
          if (referralAmount <= 0) {
            errors.push('No referral earnings available to withdraw');
            isValid = false;
          } else if (withdrawalAmount > referralAmount) {
            errors.push('Insufficient referral earnings available to withdraw');
            isValid = false;
          } else if (withdrawalAmount > amountWithdrawable) {
            errors.push('Insufficient total earnings available to withdraw');
            isValid = false;
          } else {
            validationDetails.sufficientBalance = true;
          }
          break;
          
        case 'both':
          const totalAvailable = walletZapAmount + referralAmount;
          if (withdrawalAmount > totalAvailable) {
            errors.push(`Insufficient balance available to withdraw. Available: ${amountWithdrawable.toFixed(4)} SOL`);
            isValid = false;
          } else if (withdrawalAmount > amountWithdrawable) {
            errors.push('Insufficient total earnings available to withdraw');
            isValid = false;
          } else {
            validationDetails.sufficientBalance = true;
          }
          break;
      }
    }
    
    // Calculate withdrawal data if valid
    let withdrawalData = undefined;
    
    if (investment && validationDetails.amountValid) {
      const withdrawalAmount = parseFloat(amount);
      const currentWithdrawn = investment.withdrawnAmount;
      const totalEarned = investment.earnedAmount;
      const maxWithdrawable = totalEarned - currentWithdrawn;
      
      const isCompletingInvestment = (currentWithdrawn + withdrawalAmount) >= totalEarned &&
                                     investment.percentageOfROI >= 250;
      
      withdrawalData = {
        maxWithdrawable,
        isCompletingInvestment,
        projectedStatus: isCompletingInvestment ? 'completed' : 'active'
      };
    }
    
    // Return validation results
    if (isValid) {
      return NextResponse.json({
        success: true,
        data: {
          isValid: true,
          investment: investment ? {
            id: investment._id,
            initialAmount: investment.initialAmount,
            earnedAmount: investment.earnedAmount,
            withdrawnAmount: investment.withdrawnAmount,
            walletAddress: investment.walletAddress,
            percentageOfROI: investment.percentageOfROI
          } : undefined,
          user: user ? {
            id: user._id,
            claimsSinceWithdrawal: user.claimsSinceWithdrawal,
            lastWithdrawal: user.lastWithdrawal
          } : undefined,
          validationDetails,
          withdrawalData
        }
      });
    } else {
      return NextResponse.json({
        success: true,
        data: {
          isValid: false,
          investment: investment ? {
            id: investment._id,
            initialAmount: investment.initialAmount,
            earnedAmount: investment.earnedAmount,
            withdrawnAmount: investment.withdrawnAmount,
            walletAddress: investment.walletAddress,
            percentageOfROI: investment.percentageOfROI
          } : undefined,
          user: user ? {
            id: user._id,
            claimsSinceWithdrawal: user.claimsSinceWithdrawal,
            lastWithdrawal: user.lastWithdrawal
          } : undefined,
          validationDetails
        },
        error: errors.join('; ')
      });
    }
    
  } catch (error: any) {
    console.error('Error validating withdrawal:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to validate withdrawal'
    }, { status: 500 });
  }
} 