
import React, { useEffect, useState } from 'react';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import FeaturesSection from '@/components/FeaturesSection';
import ZappingSection from '@/components/ZappingSection';
import ReferralSection from '@/components/ReferralSection';
import FlowPointsSection from '@/components/FlowPointsSection';
import CTASection from '@/components/CTASection';
import Footer from '@/components/Footer';

const Index = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden font-syne">
      {/* Subtle gradient overlay for depth */}
      <div className="fixed inset-0 bg-gradient-to-br from-black via-gray-950 to-black pointer-events-none" />
      
      {/* Minimal geometric background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none opacity-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 border border-green-500/20 rounded-full" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 border border-green-400/10 rounded-full" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] border border-green-500/5 rounded-full" />
      </div>

      <Header />
      
      <main className="relative z-10">
        <HeroSection scrollY={scrollY} />
        <ZappingSection scrollY={scrollY} />
        <FeaturesSection scrollY={scrollY} />
        <ReferralSection scrollY={scrollY} />
        <FlowPointsSection scrollY={scrollY} />
        <CTASection scrollY={scrollY} />
      </main>

      <Footer />
    </div>
  );
};

export default Index;
