import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Discord configuration
export const TELEGRAM_TOKEN = process.env.BOT_TOKEN || '';

// MongoDB configuration
export const MONGODB_URI = process.env.MONGODB_URI || "" ;

// Validate required environment variables
if (!TELEGRAM_TOKEN) {
  console.error('Missing TELEGRAM_TOKEN environment variable');
  process.exit(1);
}
