'use client'

import { <PERSON>actN<PERSON>, createContext, useState, useContext, useCallback, useEffect, useRef } from 'react'
import { ConnectionProvider } from '@solana/wallet-adapter-react'
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base'
import { clusterApiUrl, Connection, PublicKey, Transaction } from '@solana/web3.js'
import { WalletConnectWalletAdapter } from '@walletconnect/solana-adapter'
import { PhantomWalletAdapter } from '@solana/wallet-adapter-phantom'
import { SolflareWalletAdapter } from '@solana/wallet-adapter-solflare'
import { BackpackWalletAdapter } from '@solana/wallet-adapter-backpack'

// WalletConnect Project ID
const WALLETCONNECT_PROJECT_ID = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || '83a653fd2f0a9163218565e2469a24a6'

// Storage keys
const WALLET_STORAGE_KEY = 'flow_trade_wallet_state'
const WALLET_SESSION_KEY = 'flow_trade_wallet_session'

// Device detection
const isMobileDevice = () => {
  if (typeof window === 'undefined') return false
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// Wallet adapter type
type SolanaWalletAdapter = 
  | WalletConnectWalletAdapter 
  | PhantomWalletAdapter 
  | SolflareWalletAdapter 
  | BackpackWalletAdapter;

// Enhanced wallet state interface
interface WalletState {
  publicKey: string | null;
  connected: boolean;
  walletType: 'extension' | 'walletconnect' | null;
  timestamp: number;
  lastVerified?: number;
}

// Create context for wallet state
interface SolanaWalletContextType {
  wallet: SolanaWalletAdapter | null;
  publicKey: string | null;
  connected: boolean;
  isLoading: boolean;
  error: string | null;
  walletType: 'extension' | 'walletconnect' | null;
  isInitialized: boolean;
  connectionVerified: boolean;
  connect: (walletName?: 'phantom' | 'solflare' | 'backpack' | 'walletconnect') => Promise<void>;
  disconnect: () => Promise<void>;
  verifyConnection: () => Promise<boolean>;
  clearError: () => void;
  solPrice: number;
  bonkPrice: number;
  flowPrice: number;
  loadingPrice: boolean;
}

const SolanaWalletContext = createContext<SolanaWalletContextType>({
  wallet: null,
  publicKey: null,
  connected: false,
  isLoading: false,
  error: null,
  walletType: null,
  isInitialized: false,
  connectionVerified: false,
  connect: async () => {},
  disconnect: async () => {},
  verifyConnection: async () => false,
  clearError: () => {},
  solPrice: 155,
  bonkPrice: 0.000016,
  flowPrice: 0.000032,
  loadingPrice: false,
});

export const useWallet = () => useContext(SolanaWalletContext);

export const SolanaWalletProvider = ({ children }: { children: ReactNode }) => {
  const network = WalletAdapterNetwork.Devnet
  const endpoint = clusterApiUrl(network)
  
  // Enhanced state management
  const [wallet, setWallet] = useState<SolanaWalletAdapter | null>(null)
  const [connected, setConnected] = useState(false)
  const [publicKey, setPublicKey] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [walletType, setWalletType] = useState<'extension' | 'walletconnect' | null>(null)
  const [connectionVerified, setConnectionVerified] = useState(false)
  
  const [solPrice, setSolPrice] = useState(155);
  const [bonkPrice, setBonkPrice] = useState(0.000016);
  const [flowPrice, setFlowPrice] = useState(bonkPrice * 2);
  const [loadingPrice, setLoadingPrice] = useState(false);


  const fetchPrices = async () => {
try {
  setLoadingPrice(true);
  const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=solana,bonk&vs_currencies=usd');
  const data = await response.json(); 
  setSolPrice(data.solana.usd);
  setBonkPrice(data.bonk.usd);
  setFlowPrice(bonkPrice * 2);
  setLoadingPrice(false);
} catch (error) {
    setSolPrice(155);
    setBonkPrice(0.000016);
    setFlowPrice(0.000032);
    setLoadingPrice(false);
} 
  }
  useEffect(() => {
    fetchPrices();
  }, []);

  // Refs for cleanup and state tracking
  const initializationRef = useRef(false);
  // @ts-ignore
  const connectionTimeoutRef = useRef<NodeJS.Timeout>();
  // @ts-ignore
  const verificationIntervalRef = useRef<NodeJS.Timeout>();

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Enhanced connection verification
  const verifyConnection = useCallback(async (): Promise<boolean> => {
    if (!wallet || !publicKey) {
      setConnectionVerified(false);
      return false;
    }

    try {
      // Check if wallet is still connected
      const isWalletConnected = wallet.connected && wallet.publicKey;
      const publicKeyMatches = wallet.publicKey?.toBase58() === publicKey;
      
      const isVerified = isWalletConnected && publicKeyMatches;
      // @ts-ignore
      setConnectionVerified(isVerified);
      
      if (!isVerified && connected) {
        // Connection state mismatch, trigger disconnect
        console.warn('Connection verification failed, disconnecting...');
        await disconnect();
      }
      // @ts-ignore
      return isVerified;
    } catch (error) {
      console.error('Connection verification error:', error);
      setConnectionVerified(false);
      return false;
    }
  }, [wallet, publicKey, connected]);

  // Enhanced state persistence
  const saveWalletState = useCallback((state: WalletState) => {
    try {
      const stateWithVerification = {
        ...state,
        lastVerified: Date.now()
      };
      
      localStorage.setItem(WALLET_STORAGE_KEY, JSON.stringify(stateWithVerification));
      sessionStorage.setItem(WALLET_SESSION_KEY, JSON.stringify(stateWithVerification));
    } catch (error) {
      console.error('Failed to save wallet state:', error);
    }
  }, []);

  // Enhanced state loading
  const loadWalletState = useCallback((): WalletState | null => {
    try {
      // Try sessionStorage first for current session
      let storedState = sessionStorage.getItem(WALLET_SESSION_KEY);
      
      // Fall back to localStorage
      if (!storedState) {
        storedState = localStorage.getItem(WALLET_STORAGE_KEY);
      }
      
      if (!storedState) return null;
      
      const state: WalletState = JSON.parse(storedState);
      
      // Check if state is still valid (within 7 days and recently verified)
      const isStateValid = Date.now() - state.timestamp < 7 * 24 * 60 * 60 * 1000;
      const isRecentlyVerified = state.lastVerified && (Date.now() - state.lastVerified < 60 * 60 * 1000); // 1 hour
      
      if (!isStateValid) return null;
      
      return state;
    } catch (error) {
      console.error('Failed to load wallet state:', error);
      return null;
    }
  }, []);

  // Clear stored state
  const clearStoredState = useCallback(() => {
    try {
      localStorage.removeItem(WALLET_STORAGE_KEY);
      sessionStorage.removeItem(WALLET_SESSION_KEY);
    } catch (error) {
      console.error('Failed to clear wallet state:', error);
    }
  }, []);

  // Enhanced wallet connection setup
  const setupWalletListeners = useCallback((walletAdapter: SolanaWalletAdapter, newWalletType: 'extension' | 'walletconnect') => {
    // Remove existing listeners first
    walletAdapter.removeAllListeners();

    walletAdapter.on('connect', () => {
      const newPublicKey = walletAdapter.publicKey?.toBase58() || null;
      
      setConnected(true);
      setPublicKey(newPublicKey);
      setWalletType(newWalletType);
      setIsLoading(false);
      setConnectionVerified(true);
      setError(null);
      
      // Save state
      if (newPublicKey) {
        saveWalletState({
          publicKey: newPublicKey,
          connected: true,
          walletType: newWalletType,
          timestamp: Date.now(),
          lastVerified: Date.now()
        });
      }
      
      console.log('Wallet connected successfully:', newPublicKey);
    });

    walletAdapter.on('disconnect', () => {
      setConnected(false);
      setPublicKey(null);
      setWalletType(null);
      setIsLoading(false);
      setConnectionVerified(false);
      clearStoredState();
      
      console.log('Wallet disconnected');
    });

    walletAdapter.on('error', (error) => {
      console.error('Wallet error:', error);
      setError(`Wallet error: ${error.message || 'Unknown error'}`);
      setIsLoading(false);
      setConnectionVerified(false);
      
      // Clear state on error
      setConnected(false);
      setPublicKey(null);
      setWalletType(null);
      clearStoredState();
    });
  }, [saveWalletState, clearStoredState]);

  // Enhanced initialization
  useEffect(() => {
    const initializeWallet = async () => {
      if (initializationRef.current) return;
      initializationRef.current = true;

      try {
        setIsLoading(true);
        
        const storedState = loadWalletState();
        
        if (storedState && storedState.connected && storedState.publicKey && storedState.walletType) {
          console.log('Attempting to restore wallet connection...', storedState.walletType);
          
          let walletAdapter: SolanaWalletAdapter | null = null;
          
          try {
            // Create appropriate wallet adapter
            if (storedState.walletType === 'walletconnect') {
              walletAdapter = new WalletConnectWalletAdapter({
                network: WalletAdapterNetwork.Devnet,
                options: {
                  projectId: WALLETCONNECT_PROJECT_ID,
                  metadata: {
                    name: 'Flow Trade',
                    description: 'Flow Trade Wallet Connection',
                    url: typeof window !== 'undefined' ? window.location.origin : '',
                    icons: ['https://your-app-icon.com/icon.png'],
                  },
                  relayUrl: 'wss://relay.walletconnect.com',
                  // @ts-ignore
                  qrcodeModalOptions: {
                    mobileLinks: ['rainbow', 'metamask', 'argent', 'trust', 'imtoken', 'pillar'],
                  },
                },
              });
            } else {
              // Try extension wallets
              try {
                walletAdapter = new PhantomWalletAdapter();
              } catch {
                try {
                  walletAdapter = new SolflareWalletAdapter();
                } catch {
                  walletAdapter = new BackpackWalletAdapter();
                }
              }
            }
            
            if (walletAdapter) {
              setupWalletListeners(walletAdapter, storedState.walletType);
              
              // Set connection timeout
              connectionTimeoutRef.current = setTimeout(() => {
                console.warn('Connection timeout, clearing stored state');
                clearStoredState();
                setIsLoading(false);
              }, 10000); // 10 second timeout
              
              // Attempt to connect
              await walletAdapter.connect();
              setWallet(walletAdapter);
              
              // Clear timeout on successful connection
              if (connectionTimeoutRef.current) {
                clearTimeout(connectionTimeoutRef.current);
              }
            } else {
              throw new Error('Failed to create wallet adapter');
            }
          } catch (error) {
            console.error('Failed to restore wallet connection:', error);
            clearStoredState();
            setError('Failed to restore wallet connection');
          }
        }
      } catch (error) {
        console.error('Wallet initialization error:', error);
        setError('Failed to initialize wallet');
        clearStoredState();
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    };

    initializeWallet();
  }, [loadWalletState, setupWalletListeners, clearStoredState]);

  // Enhanced connect function
  const connect = useCallback(async (walletName?: 'phantom' | 'solflare' | 'backpack' | 'walletconnect') => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Disconnect existing wallet first
      if (wallet) {
        await wallet.disconnect();
      }
      
      const isMobile = isMobileDevice();
      let walletAdapter: SolanaWalletAdapter | null = null;
      let newWalletType: 'extension' | 'walletconnect' = 'extension';
      
      // Determine wallet type
      if ((isMobile && !walletName) || walletName === 'walletconnect') {
        walletAdapter = new WalletConnectWalletAdapter({
          network: WalletAdapterNetwork.Devnet,
          options: {
            projectId: WALLETCONNECT_PROJECT_ID,
            metadata: {
              name: 'Flow Trade',
              description: 'Flow Trade Wallet Connection',
              url: typeof window !== 'undefined' ? window.location.origin : '',
              icons: ['https://your-app-icon.com/icon.png'],
            },
            relayUrl: 'wss://relay.walletconnect.com',
            // @ts-ignore
            qrcodeModalOptions: {
              mobileLinks: ['rainbow', 'metamask', 'argent', 'trust', 'imtoken', 'pillar'],
            },
          },
        });
        newWalletType = 'walletconnect';
      } else {
        // Extension wallets
        if (walletName === 'phantom' || !walletName) {
          walletAdapter = new PhantomWalletAdapter();
        } else if (walletName === 'solflare') {
          walletAdapter = new SolflareWalletAdapter();
        } else if (walletName === 'backpack') {
          walletAdapter = new BackpackWalletAdapter();
        }
      }
      
      if (!walletAdapter) {
        throw new Error('No compatible wallet found');
      }

      setupWalletListeners(walletAdapter, newWalletType);
      
      // Set connection timeout
      connectionTimeoutRef.current = setTimeout(() => {
        setError('Connection timeout. Please try again.');
        setIsLoading(false);
      }, 30000); // 30 second timeout for user interaction
      
      await walletAdapter.connect();
      setWallet(walletAdapter);
      
      // Clear timeout on successful connection
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
      }
      
    } catch (error: any) {
      console.error('Failed to connect wallet:', error);
      
      // Provide user-friendly error messages
      let errorMessage = 'Failed to connect wallet';
      if (error.message?.includes('User rejected')) {
        errorMessage = 'Connection rejected by user';
      } else if (error.message?.includes('timeout')) {
        errorMessage = 'Connection timeout. Please try again.';
      } else if (error.message?.includes('not found') || error.message?.includes('not available')) {
        errorMessage = 'Wallet not found. Please install the wallet extension.';
      }
      
      setError(errorMessage);
      setIsLoading(false);
    } finally {
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
      }
    }
  }, [wallet, setupWalletListeners]);

  // Enhanced disconnect function
  const disconnect = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (wallet) {
        await wallet.disconnect();
      }
      
      // Clear all state
      setWallet(null);
      setConnected(false);
      setPublicKey(null);
      setWalletType(null);
      setConnectionVerified(false);
      clearStoredState();
      
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
      setError('Failed to disconnect wallet');
    } finally {
      setIsLoading(false);
    }
  }, [wallet, clearStoredState]);

  // Periodic connection verification
  useEffect(() => {
    if (connected && wallet) {
      verificationIntervalRef.current = setInterval(async () => {
        await verifyConnection();
      }, 30000); // Verify every 30 seconds
      
      return () => {
        if (verificationIntervalRef.current) {
          clearInterval(verificationIntervalRef.current);
        }
      };
    }
  }, [connected, wallet, verifyConnection]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
      }
      if (verificationIntervalRef.current) {
        clearInterval(verificationIntervalRef.current);
      }
      if (wallet) {
        wallet.removeAllListeners();
      }
    };
  }, [wallet]);

  // Update saved state when connection changes
  useEffect(() => {
    if (connected && publicKey && walletType) {
      saveWalletState({
        publicKey,
        connected,
        walletType,
        timestamp: Date.now(),
        lastVerified: Date.now()
      });
    }
  }, [connected, publicKey, walletType, saveWalletState]);

  return (
    <ConnectionProvider endpoint={endpoint}>
      <SolanaWalletContext.Provider
        value={{
          wallet,
          publicKey,
          connected: connected && connectionVerified, // Only show as connected if verified
          isLoading,
          error,
          walletType,
          isInitialized,
          connectionVerified,
          connect,
          disconnect,
          verifyConnection,
          clearError,
          solPrice,
          bonkPrice,
          flowPrice,
          loadingPrice
        }}
      >
        {children}
      </SolanaWalletContext.Provider>
    </ConnectionProvider>
  )
}
