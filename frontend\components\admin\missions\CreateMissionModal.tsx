import React, { useState, useRef } from 'react'
import { toast } from 'react-hot-toast'
import { MissionType, MissionStatus, RewardType } from '@/types/mission'
import { Button } from '@/components/ui/button'
import Image from 'next/image'

interface CreateMissionModalProps {
  isOpen: boolean
  onClose: () => void
  onMissionCreated: () => void
}

const CreateMissionModal = ({ isOpen, onClose, onMissionCreated }: CreateMissionModalProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewImage, setPreviewImage] = useState('');
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: MissionType.TASK,
    image: '',
    taskLink: '',
    rewardType: RewardType.SOL,
    rewardAmount: 0.01,
    isHighlighted: false
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement
      setFormData(prev => ({
        ...prev,
        [name]: checkbox.checked
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
    
    // Clear error when field is modified
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }
  
  // Helper function to convert file to base64
  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Validate file size (2MB max)
    if (file.size > 2 * 1024 * 1024) {
      toast.error("Image is too large. Maximum size is 2MB.");
      return;
    }
    
    // Validate file type
    if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
      toast.error("Invalid file type. Please upload JPEG, PNG, or WebP images.");
      return;
    }
    
    // Store the file for later upload
    setSelectedFile(file);
    
    // Show preview of the image
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setPreviewImage(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
    
    toast.success("Image selected. It will be uploaded when you submit the form.");
  };
  
  const removeImage = () => {
    setSelectedFile(null);
    setPreviewImage('');
    setFormData(prev => ({
      ...prev,
      image: ''
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    toast.success("Image removed");
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.title.trim() || formData.title.length < 3) {
      newErrors.title = 'Title must be at least 3 characters'
    }
    
    if (!formData.description.trim() || formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters'
    }
    
    if (!selectedFile && !formData.image) {
      newErrors.image = 'Image is required'
    }
    
    if (formData.rewardAmount <= 0) {
      newErrors.rewardAmount = 'Reward amount must be positive'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  // Form submission handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setIsSubmitting(true)
    
    try {
      let imageUrl = formData.image;
      
      // Upload image if a file was selected
      if (selectedFile) {
        setIsUploading(true);
        toast.loading("Uploading image...");
        
        const imageBase64 = await convertFileToBase64(selectedFile);
        
        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            image: imageBase64,
            folder: 'mission_images',
          }),
        });
        
        if (!uploadResponse.ok) {
          throw new Error('Failed to upload image');
        }
        
        const uploadData = await uploadResponse.json();
        imageUrl = uploadData.url;
        setIsUploading(false);
        toast.success("Image uploaded successfully");
      }
      
      const missionData = {
        title: formData.title,
        description: formData.description,
        type: formData.type,
        status: MissionStatus.ACTIVE,
        image: imageUrl,
        taskLink: formData.taskLink,
        reward: {
          type: formData.rewardType,
          amount: Number(formData.rewardAmount)
        },
        isHighlighted: formData.isHighlighted
      }
      
      const response = await fetch('/api/admin/missions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(missionData)
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create mission')
      }
      
      const data = await response.json()
      
      if (data.success) {
        toast.success('Mission created successfully')
        setFormData({
          title: '',
          description: '',
          type: MissionType.TASK,
          image: '',
          taskLink: '',
          rewardType: RewardType.SOL,
          rewardAmount: 0.01,
          isHighlighted: false
        })
        setSelectedFile(null);
        setPreviewImage('');
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        onMissionCreated()
      } else {
        toast.error(data.error || 'Failed to create mission')
      }
    } catch (error) {
      console.error('Error creating mission:', error)
      toast.error((error as Error).message || 'Failed to create mission')
    } finally {
      setIsSubmitting(false)
      setIsUploading(false)
    }
  }
  
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4 overflow-y-auto">
      <div className="bg-black/80 backdrop-blur-md border border-gray-800 rounded-lg max-w-2xl w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white">Create New Mission</h2>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="col-span-2">
              <label htmlFor="title" className="block text-sm font-medium text-gray-200 mb-1">Title</label>
              <input 
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className="w-full px-3 py-2 rounded-md bg-gray-900/50 border border-gray-800 text-white"
                placeholder="Enter mission title"
              />
              {errors.title && <p className="mt-1 text-sm text-red-500">{errors.title}</p>}
            </div>
            
            <div className="col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-200 mb-1">Description</label>
              <textarea 
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="w-full px-3 py-2 rounded-md bg-gray-900/50 border border-gray-800 text-white min-h-[100px]"
                placeholder="Enter mission description"
              />
              {errors.description && <p className="mt-1 text-sm text-red-500">{errors.description}</p>}
            </div>
            
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-200 mb-1">Mission Type</label>
              <select 
                id="type"
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full px-3 py-2 rounded-md bg-gray-900/50 border border-gray-800 text-white"
              >
                {Object.values(MissionType).map((type) => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="taskLink" className="block text-sm font-medium text-gray-200 mb-1">Task Link (Optional)</label>
              <input 
                id="taskLink"
                name="taskLink"
                value={formData.taskLink}
                onChange={handleChange}
                className="w-full px-3 py-2 rounded-md bg-gray-900/50 border border-gray-800 text-white"
                placeholder="https://..."
              />
            </div>
            
            <div>
              <label htmlFor="rewardType" className="block text-sm font-medium text-gray-200 mb-1">Reward Type</label>
              <select 
                id="rewardType"
                name="rewardType"
                value={formData.rewardType}
                onChange={handleChange}
                className="w-full px-3 py-2 rounded-md bg-gray-900/50 border border-gray-800 text-white"
              >
                {Object.values(RewardType).map((type) => (
                  <option key={type} value={type}>
                    {type.toUpperCase()}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="rewardAmount" className="block text-sm font-medium text-gray-200 mb-1">Reward Amount</label>
              <input 
                id="rewardAmount"
                name="rewardAmount"
                type="number"
                step="0.01"
                min="0.01"
                value={formData.rewardAmount}
                onChange={handleChange}
                className="w-full px-3 py-2 rounded-md bg-gray-900/50 border border-gray-800 text-white"
                placeholder="0.01"
              />
              {errors.rewardAmount && <p className="mt-1 text-sm text-red-500">{errors.rewardAmount}</p>}
            </div>
            
            <div className="col-span-2">
              <label htmlFor="image" className="block text-sm font-medium text-gray-200 mb-1">Mission Image</label>
              <div className="flex flex-col space-y-4">
                {!previewImage && !formData.image ? (
                  <div className="space-y-4">
                    <input
                      type="file"
                      accept="image/jpeg,image/png,image/webp"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      className="hidden"
                      id="image-upload"
                    />
                    <button 
                      type="button" 
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full flex items-center justify-center gap-2 p-6 border border-dashed border-gray-700 rounded-md text-gray-300 hover:text-white hover:border-gray-500"
                      disabled={isSubmitting}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" y1="3" x2="12" y2="15"></line>
                      </svg>
                      <span>Select Image to Upload</span>
                    </button>
                    <p className="text-xs text-gray-500 text-center">
                      Supported formats: JPG, PNG, WebP. Max size: 2MB.
                    </p>
                  </div>
                ) : (
                  <div className="relative rounded-md overflow-hidden bg-gray-900">
                    <Image 
                      src={previewImage || formData.image} 
                      alt="Preview" 
                      width={500}
                      height={300}
                      className="w-full h-60 object-contain"
                    />
                    <button
                      type="button"
                      className="absolute top-2 right-2 h-8 w-8 bg-black/60 text-red-500 hover:bg-red-900/50 hover:text-red-300 rounded-full flex items-center justify-center"
                      onClick={removeImage}
                      disabled={isSubmitting}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>
                )}
                {errors.image && <p className="mt-1 text-sm text-red-500">{errors.image}</p>}
              </div>
            </div>
            
            <div className="col-span-2 flex items-center">
              <input
                id="isHighlighted"
                name="isHighlighted"
                type="checkbox"
                checked={formData.isHighlighted}
                onChange={(e) => handleChange(e as React.ChangeEvent<HTMLInputElement>)}
                className="h-4 w-4 rounded border-gray-800 bg-gray-900/50 mr-2"
              />
              <label htmlFor="isHighlighted" className="text-sm font-medium text-gray-200">
                Highlight this mission
              </label>
            </div>
          </div>
          
          <div className="flex justify-end gap-3 mt-6">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose}
              disabled={isSubmitting}
              className="border-gray-700 text-gray-300 hover:bg-gray-800"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting && (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {isUploading ? "Uploading Image..." : "Creating Mission..."}
                </>
              )}
              {!isSubmitting && 'Create Mission'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CreateMissionModal 