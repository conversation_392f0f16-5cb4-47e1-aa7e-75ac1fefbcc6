"use client"

import React, { useState, useRef, useEffect } from 'react'
import { useLanguage, languageNames, languageFlags, type Language } from '@/context/LanguageContext'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, Check } from 'lucide-react'

interface LanguageSelectorProps {
  variant?: 'dropdown' | 'inline'
  className?: string
}

const LanguageSelector = ({ variant = 'dropdown', className = '' }: LanguageSelectorProps) => {
  const { language, setLanguage } = useLanguage()
  const [isOpen, setIsOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // List of supported languages
  const languages: Language[] = ['en', 'ru', 'hi', 'fr', 'es', 'de']

  // Handle mounting to prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Close dropdown when clicking outside
  useEffect(() => {
    if (!mounted) return
    
    const handleClickOutside = (e: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [mounted])

  // Return empty placeholder during SSR/before mount
  if (!mounted) {
    return <div className={className} />
  }

  if (variant === 'inline') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {languages.map((lang) => (
          <button
            key={lang}
            onClick={() => setLanguage(lang)}
            className={`flex items-center justify-center rounded-full p-1 transition-all ${
              language === lang
                ? 'bg-green-500/20 shadow-inner'
                : 'hover:bg-gray-800'
            }`}
            title={languageNames[lang]}
          >
            <span className="text-xl">{languageFlags[lang]}</span>
          </button>
        ))}
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center justify-between gap-2 rounded-xl border border-gray-800 px-3 py-2 transition-colors hover:bg-gray-800/50 ${
          isOpen ? 'bg-gray-800/50' : 'bg-gray-900/50'
        }`}
        aria-expanded={isOpen}
      >
        <span className="text-xl mr-1">{languageFlags[language]}</span>
        <span className="text-sm text-gray-300">{languageNames[language]}</span>
        <ChevronDown
          className={`h-4 w-4 text-gray-400 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -5 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -5 }}
            transition={{ duration: 0.15 }}
            className="absolute z-50 mt-1 w-48 rounded-lg border border-gray-800 bg-gray-900 p-1 shadow-lg"
          >
            {languages.map((lang) => (
              <button
                key={lang}
                onClick={() => {
                  setLanguage(lang)
                  setIsOpen(false)
                }}
                className={`flex w-full items-center rounded-md px-3 py-2 text-left text-sm transition-colors ${
                  language === lang
                    ? 'bg-green-500/10 text-green-400'
                    : 'text-gray-300 hover:bg-gray-800'
                }`}
              >
                <span className="text-xl mr-2">{languageFlags[lang]}</span>
                <span className="flex-1">{languageNames[lang]}</span>
                {language === lang && <Check className="h-4 w-4" />}
              </button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default LanguageSelector 