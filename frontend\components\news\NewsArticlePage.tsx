"use client"

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { Heart, Share2, Eye, ArrowLeft, Calendar } from 'lucide-react'
import { NewsArticle } from './EditNewsModal'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import { toast } from 'react-hot-toast'
import { useRouter } from 'next/navigation'
import NewsContent from './NewsContent'

interface NewsArticlePageProps {
  article: NewsArticle
}

// Function to format numbers with K (thousands)
const formatNumber = (num: number = 0): string => {
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// Function to get a category color
const getCategoryColor = (category: string): string => {
  switch(category.toLowerCase()) {
    case 'market':
    case 'markets':
      return 'bg-blue-500/20 text-blue-400 border-blue-500/20'
    case 'global':
      return 'bg-purple-500/20 text-purple-400 border-purple-500/20'
    case 'regulation':
      return 'bg-amber-500/20 text-amber-400 border-amber-500/20'
    case 'defi':
      return 'bg-green-500/20 text-green-400 border-green-500/20'
    case 'technology':
      return 'bg-cyan-500/20 text-cyan-400 border-cyan-500/20'
    case 'trading':
      return 'bg-red-500/20 text-red-400 border-red-500/20'
    case 'general':
    default:
      return 'bg-gray-500/20 text-gray-400 border-gray-500/20'
  }
}

const NewsArticlePage: React.FC<NewsArticlePageProps> = ({ article }) => {
  const router = useRouter()
  const { user, isAuthenticated, isTelegram, telegramUser } = useAuth()
  const { t } = useLanguage()
  const [likeCount, setLikeCount] = useState(article.likesCount || 0)
  const [userLiked, setUserLiked] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  
  // Determine if user has liked the article
  useEffect(() => {
    const checkLikedStatus = () => {
      // Make sure we have a user and article
      if (!user || !article._id) return
      
      // First check the user's likedNews array if available
      if (user.likedNews && Array.isArray(user.likedNews)) {
        const articleId = String(article._id)
        const hasLikedFromUser = user.likedNews.some((id: any) => String(id) === articleId)
        
        if (hasLikedFromUser) {
          setUserLiked(true)
          return
        }
      }
      
      // If available, check the article's userLiked property
      // This property comes from our API response, not the original schema
      if ((article as any).userLiked) {
        setUserLiked(true)
        return
      }
    }
    
    checkLikedStatus()
  }, [user, article])

  // Format date
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return t('news.unknownDate')
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    })
  }

  // Handle like/unlike action
  const handleLike = async () => {
    if (!isAuthenticated) {
      if (!isTelegram) {
        toast.error(t('news.openInTelegramToInteract'))
      } else {
        toast.error(t('news.waitForAuthentication'))
      }
      return
    }
    
    if (isLoading) return
    
    try {
      setIsLoading(true)
      
      // Determine if we're liking or unliking
      const method = userLiked ? 'DELETE' : 'POST'
      
      // Optimistically update UI
      setUserLiked(!userLiked)
      setLikeCount(prev => userLiked ? Math.max(0, prev - 1) : prev + 1)
      
      // Add telegram ID to header for Telegram-based auth
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (telegramUser?.id) {
        headers['x-telegram-id'] = telegramUser.id.toString()
      }
      
      // Make API request
      const response = await fetch(`/api/news/like/${article._id}`, {
        method,
        headers
      })
      
      if (!response.ok) {
        throw new Error(t('news.failedToUpdateLikeStatus'))
      }
      
      const data = await response.json()
      
      if (!data.success) {
        // Revert optimistic update on error
        setUserLiked(userLiked)
        setLikeCount(data.likesCount || likeCount)
        
        if (data.alreadyLiked) {
          toast.success(t('news.alreadyLiked'))
          setUserLiked(true)
        } else if (data.notLiked) {
          setUserLiked(false)
        } else {
          toast.error(data.error || t('news.failedToUpdateLikeStatus'))
        }
      } else {
        // Update count from server response to ensure accuracy
        setLikeCount(data.likesCount)
        
        // Force router refresh to get updated data
        router.refresh()
      }
    } catch (error) {
      // Revert on error
      setUserLiked(userLiked)
      setLikeCount(prev => userLiked ? prev + 1 : Math.max(0, prev - 1))
      toast.error(t('news.failedToUpdateLikeStatus'))
    } finally {
      setIsLoading(false)
    }
  }

  // Handle share action
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: article.title,
        text: article.summary,
        url: window.location.href
      }).catch(() => {})
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
        .then(() => toast.success(t('news.linkCopied')))
        .catch(() => {})
    }
  }

  return (
    <motion.div 
      className="max-w-[500px] mx-auto relative z-10"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      
      {/* Glowing Background Effect */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-48 h-48 bg-green-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>
      
      {/* Back button */}
      <Link href="/news" className="inline-block mb-4">
        <motion.div 
          className="flex items-center text-sm text-gray-400 hover:text-green-400 transition-colors"
          whileHover={{ x: -3 }}
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          <span>{t('news.backToArticles')}</span>
        </motion.div>
      </Link>

      {/* User info - only show if authenticated */}
      {isAuthenticated && user && (
        <div className="mb-4 bg-black/60 backdrop-blur-md rounded-lg p-3 border border-gray-800/50">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-6 h-6 rounded-full bg-green-900/60 flex items-center justify-center mr-2">
                <span className="text-xs text-green-400">{user?.displayName?.charAt(0)}</span>
              </div>
              <span className="text-sm text-gray-300">{user?.displayName}</span>
            </div>
          </div>
        </div>
      )}

      {/* Article Header */}
      <div className="bg-black/60 backdrop-blur-md rounded-lg overflow-hidden border border-gray-800/50 mb-4">
        {/* Article Category */}
        <div className="p-4 pb-0">
          <div className={`inline-block px-2 py-1 text-xs font-medium rounded border ${getCategoryColor(article.category)}`}>
            {article.category}
          </div>
        </div>
        
        {/* Article Title */}
        <h1 className="text-xl sm:text-2xl font-bold text-white px-4 pt-2 pb-4">
          {article.title}
        </h1>
        
        {/* Meta info */}
        <div className="flex items-center justify-between text-sm text-gray-400 border-t border-gray-800/50 px-4 py-3">
          <div className="flex items-center">
            <span className="font-medium text-gray-300 mr-2">{article.authorName}</span>
            <div className="flex items-center">
              <Calendar className="w-3.5 h-3.5 mr-1 text-gray-500" />
              <span>{formatDate(article.publishedAt)}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Eye className="w-3.5 h-3.5 mr-1 text-gray-500" />
              <span>{formatNumber(article.viewsCount)}</span>
            </div>
            
            <motion.div 
              className={`flex items-center cursor-pointer ${userLiked ? 'text-red-400' : 'text-gray-400 hover:text-red-400'}`}
              onClick={handleLike}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Heart className={`w-3.5 h-3.5 mr-1 ${userLiked ? 'fill-red-500 text-red-500' : ''}`} />
              <span>{formatNumber(likeCount)}</span>
            </motion.div>
            
            <motion.div 
              className="flex items-center cursor-pointer text-gray-400 hover:text-green-400"
              onClick={handleShare}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Share2 className="w-3.5 h-3.5" />
            </motion.div>
          </div>
        </div>
      </div>
      
      {/* Main Image */}
      {article.image && (
        <div className="relative h-[200px] sm:h-[250px] mb-6 rounded-lg overflow-hidden border border-gray-800/50">
          <Image 
            src={article.image}
            alt={article.title}
            fill
            className="object-cover"
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        </div>
      )}
      
      {/* Article Summary - Highlighted */}
      <div className="bg-black/60 backdrop-blur-md rounded-lg overflow-hidden border border-gray-800/50 mb-6 p-4">
        <p className="text-gray-300 italic">
          {article.summary}
        </p>
      </div>
      
      {/* Article Content */}
      <div className="bg-black/60 backdrop-blur-md rounded-lg overflow-hidden border border-gray-800/50 p-4 mb-6">
        <NewsContent 
          content={article.content}
          className="prose-sm prose-headings:text-gray-200 prose-p:text-gray-300 prose-a:text-blue-400 prose-strong:text-white prose-blockquote:text-gray-300 prose-blockquote:border-green-500"
        />
      </div>
      
      {/* Share and like buttons (bottom) */}
      <div className="flex justify-between bg-black/60 backdrop-blur-md rounded-lg overflow-hidden border border-gray-800/50 p-3">
        <motion.button
          className={`flex items-center justify-center px-4 py-2 rounded-md ${
            isLoading ? 'bg-gray-800 text-gray-400' : 
            userLiked ? 'bg-red-500/20 text-red-400' : 
            'bg-black/40 text-gray-300 hover:bg-red-500/20 hover:text-red-400'
          }`}
          onClick={handleLike}
          whileHover={{ scale: isLoading ? 1 : 1.03 }}
          whileTap={{ scale: isLoading ? 1 : 0.97 }}
          disabled={isLoading}
        >
          <Heart className={`mr-2 h-4 w-4 ${userLiked ? 'fill-red-500' : ''}`} />
          {isLoading ? t('news.processing') : userLiked ? t('news.liked') : t('news.like')}
        </motion.button>
        
        <motion.button
          className="flex items-center justify-center px-4 py-2 rounded-md bg-black/40 text-gray-300 hover:bg-green-500/20 hover:text-green-400"
          onClick={handleShare}
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
        >
          <Share2 className="mr-2 h-4 w-4" />
          {t('news.share')}
        </motion.button>
      </div>
    </motion.div>
  )
}

export default NewsArticlePage 