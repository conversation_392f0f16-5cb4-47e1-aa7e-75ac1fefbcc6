import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Mission } from '@/libs/model/missions.schema';
import { UserRole } from '@/types/user';

// GET: Get all missions (admin)
export async function GET(req: NextRequest) {
  try {
    
    // Connect to database
    await connectToDatabase();
    
    // Get query parameters
    const url = new URL(req.url);
    const status = url.searchParams.get('status');
    const type = url.searchParams.get('type');
    const highlighted = url.searchParams.get('highlighted');
    
    // Build query
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (type) {
      query.type = type;
    }
    
    if (highlighted) {
      query.isHighlighted = highlighted === 'true';
    }
    
    // Get missions
    const missions = await Mission.find(query).sort({ createdAt: -1 });
    
    return NextResponse.json({ success: true, missions });
    
  } catch (error) {
    console.error('Error getting missions:', error);
    return NextResponse.json({ success: false, error: 'Failed to get missions' }, { status: 500 });
  }
}

// POST: Create a new mission (admin only)
export async function POST(req: NextRequest) {
  try {
    
    // Parse request body
    const body = await req.json();
    
    // Validate required fields
    if (!body.title || !body.description || !body.type || !body.image) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    if (!body.reward || !body.reward.type || !body.reward.amount) {
      return NextResponse.json(
        { success: false, error: 'Reward information is required' },
        { status: 400 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Create new mission
    const mission = new Mission({
      title: body.title,
      description: body.description,
      type: body.type,
      status: body.status,
      image: body.image,
      taskLink: body.taskLink,
      reward: {
        type: body.reward.type,
        amount: body.reward.amount
      },
      currentParticipants: 0,
      redeemedBy: [],
      isHighlighted: body.isHighlighted || false
    });
    
    await mission.save();
    
    return NextResponse.json({ success: true, mission });
    
  } catch (error) {
    console.error('Error creating mission:', error);
    return NextResponse.json({ success: false, error: 'Failed to create mission' }, { status: 500 });
  }
} 