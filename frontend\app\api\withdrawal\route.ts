import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { User } from '@/libs/model/user.schema';
import { Withdrawal } from '@/libs/model/withdrawal.schema';

export async function GET(req: NextRequest) {
  try {
    // Get user ID from cookie or header
    const userId = req.cookies.get('userId')?.value;
    const telegramId = req.headers.get('x-telegram-id') || req.cookies.get('telegramId')?.value;
    
    // const userId = "68380e3e3b44aa85b374c1f0";
    // const telegramId = 5195131141;

    // Require authentication
    if (!userId && !telegramId) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find user by either userId or telegramId
    let user;
    if (userId) {
      user = await User.findById(userId);
    } else if (telegramId) {
      user = await User.findOne({ telegramId });
    }
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Check if user is admin (for viewing all withdrawals)
    const isAdmin = user.role === 'admin';
    
    // Get URL parameters for optional filtering
    const searchParams = req.nextUrl.searchParams;
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Default 10, max 50
    
  // Get recent withdrawals for this user
    const recentWithdrawals = await Withdrawal.find(
      isAdmin && searchParams.get('all') === 'true' ? {} : { user: user._id }
    )
      .sort({ createdAt: -1 })
      .limit(limit);
    
    // Get total withdrawal counts
    const totalCount = await Withdrawal.countDocuments(
      isAdmin && searchParams.get('all') === 'true' ? {} : { user: user._id }
    );
    
    // Return success response with just the necessary data
    return NextResponse.json({
      success: true,
      data: {
        recentWithdrawals,
        totalCount,
        userWallet: user.wallet.balance
      }
    });
    
  } catch (error) {
    console.error('Error fetching withdrawals:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch withdrawals' },
      { status: 500 }
    );
  }
} 