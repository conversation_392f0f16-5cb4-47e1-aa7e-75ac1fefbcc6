import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Mission } from '@/libs/model/missions.schema';
import { MissionProgressStatus } from '@/types/mission';
import { cookies } from 'next/headers';
import { headers } from 'next/headers';

export async function POST(req: NextRequest) {
  try {
    const userId = (await cookies()).get('userId')?.value;
    const telegramId = (await headers()).get('x-telegram-id') || '';
    console.log(userId, telegramId)
    if (!userId ) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const body = await req.json();
    const { missionId } = body;
    
    if (!missionId) {
      return NextResponse.json({ error: 'Mission ID is required' }, { status: 400 });
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find mission
    const mission = await Mission.findById(missionId);
    if (!mission) {
      return NextResponse.json({ error: 'Mission not found' }, { status: 404 });
    }
    
    // Check if mission is active
    if (mission.status !== 'active') {
      return NextResponse.json({ error: 'Mission is not active' }, { status: 400 });
    }
    
    // Check if mission already redeemed by user
    if (mission.redeemedBy && mission.redeemedBy.some((r: any) => r.userId.toString() === userId)) {
      return NextResponse.json({ 
        error: 'Mission already redeemed',
        status: 'REDEEMED'
      }, { status: 400 });
    }
    
    // Check if mission already started by user
    const userProgress = mission.startedBy && mission.startedBy.find((p: any) => p.userId.toString() === userId);
    if (userProgress) {
      if (userProgress.status === MissionProgressStatus.IN_PROGRESS) {
        // Calculate remaining time
        const now = new Date();
        const completionTime = new Date(userProgress.estimatedCompletionAt);
        const remainingMs = Math.max(0, completionTime.getTime() - now.getTime());
        const remainingTimeMinutes = Math.ceil(remainingMs / (1000 * 60));
        
        if (remainingTimeMinutes > 0) {
          return NextResponse.json({
            error: `Mission already in progress. Please wait ${remainingTimeMinutes} more minute(s).`,
            progress: userProgress,
            remainingTimeMinutes,
            status: 'IN_PROGRESS',
            taskLink: mission.taskLink || null
          }, { status: 400 });
        } else {
          // Timer expired, mission can be completed
          return NextResponse.json({
            message: 'Mission timer completed. You can now claim your reward!',
            progress: userProgress,
            status: 'COMPLETABLE',
            taskLink: mission.taskLink || null
          }, { status: 200 });
        }
      }
    }
    
    // Start the mission for the user with random timer
    const now = new Date();
    
    // Generate random time between min and max minutes
    const minTimeMs = (mission.minTimeToComplete || 10) * 60 * 1000;
    const maxTimeMs = (mission.maxTimeToComplete || 20) * 60 * 1000;
    const randomTimeMs = Math.floor(Math.random() * (maxTimeMs - minTimeMs + 1)) + minTimeMs;
    
    console.log("Before update:", JSON.stringify({
      userId,
      missionId: mission._id.toString(),
      currentStartedBy: mission.startedBy || [],
      hasStartedByArray: !!mission.startedBy
    }));
    
    // Ensure startedBy is initialized as an array (handling potential schema migration issues)
    if (!Array.isArray(mission.startedBy)) {
      mission.startedBy = [];
    }
    
    // Create the progress entry
    const progressEntry = {
      userId,
      startedAt: now,
      status: MissionProgressStatus.IN_PROGRESS,
      estimatedCompletionAt: new Date(now.getTime() + randomTimeMs)
    };
    
    // Add user progress to startedBy array
    mission.startedBy.push(progressEntry);
    
    console.log("After push:", JSON.stringify({
      userId,
      missionId: mission._id.toString(),
      currentStartedBy: mission.startedBy,
      startedByLength: mission.startedBy.length
    }));
    
    // Save with explicit markModified to ensure MongoDB updates the field
    mission.markModified('startedBy');
    await mission.save();
    
    console.log("After save:", JSON.stringify({
      missionId: mission._id.toString(),
      startedByLength: mission.startedBy.length
    }));
    
    // Get the user progress we just added
    const progress = mission.startedBy[mission.startedBy.length - 1];
    
    // Calculate random time in minutes for client display
    const startTime = new Date(progress.startedAt);
    const endTime = new Date(progress.estimatedCompletionAt);
    const durationMinutes = Math.ceil((endTime.getTime() - startTime.getTime()) / (1000 * 60));
    
    console.log(mission);
    // Return the mission with a success message
    return NextResponse.json({
      message: 'Mission started successfully',
      status: 'STARTED',
      mission: {
        _id: mission._id,
        title: mission.title,
        description: mission.description,
        taskLink: mission.taskLink || null,
        waitTimeMinutes: durationMinutes
      },
      progress
    }, { status: 200 });
    
  } catch (error: any) {
    console.error('Error starting mission:', error);
    return NextResponse.json({ error: 'Failed to start mission' }, { status: 500 });
  }
} 