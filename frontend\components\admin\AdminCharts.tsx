"use client"

import React, { useEffect } from 'react'

const AdminCharts = () => {
  useEffect(() => {
    // Function to fix Recharts measurement span issue
    const fixRechartsSpan = () => {
      // First approach: Find by ID and hide
      const span = document.getElementById('rechart_measurement_span');
      if (span) {
        span.style.display = 'none';
        span.style.visibility = 'hidden';
        span.style.position = 'absolute';
        span.style.top = '-9999px';
        span.style.left = '-9999px';
      }
      
      // Second approach: Find all spans with specific attributes
      document.querySelectorAll('span[aria-hidden="true"][style*="position: absolute"]').forEach(el => {
        const element = el as HTMLElement;
        element.style.display = 'none';
        element.style.visibility = 'hidden';
        element.style.position = 'absolute';
        element.style.top = '-9999px';
        element.style.left = '-9999px';
      });
    };
    
    // Run immediately
    fixRechartsSpan();
    
    // Also run after a short delay to catch any that might be added after initial render
    const intervalId = setInterval(fixRechartsSpan, 1000);
    
    return () => clearInterval(intervalId);
  }, []);
  
  return null; // This component doesn't render anything
}

export default AdminCharts 