"use client"
import React, { useState, useCallback, useEffect, useMemo } from 'react'
import { motion } from 'framer-motion'
import { Award, Check, Gift, Loader2, AlertCircle, Lock, Users, RefreshCw, ArrowRight } from 'lucide-react'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import { referralTasks } from '@/data'
import { showErrorToast, showSuccessToast } from '@/components/initial/ToasterProvider'

const ReferTasks = () => {
  const { user, refreshUser } = useAuth()
  const { t } = useLanguage()
  const [processingTask, setProcessingTask] = useState<string | null>(null)
  const [claimError, setClaimError] = useState<string | null>(null)
  const [claimedTasksMap, setClaimedTasksMap] = useState<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Get the total referrals count
  const totalReferrals = user?.referrals?.length || 0
  
  // Get already claimed task IDs
  const claimedTasks = user?.redeemedReferralTasks || []

  // Initialize claimed tasks map for UI state management
  useEffect(() => {
    const tasksMap: Record<string, boolean> = {}
    claimedTasks.forEach(taskId => {
      tasksMap[taskId] = true
    })
    setClaimedTasksMap(tasksMap)
  }, [claimedTasks])

  // Calculate referral progress for each task
  const calculateProgress = (requiredCount: number) => {
    return Math.min(100, (totalReferrals / requiredCount) * 100)
  }

  // Refresh user data
  const handleRefresh = async () => {
    if (isRefreshing) return
    setIsRefreshing(true)
    try {
      await refreshUser()
      showSuccessToast(t('referTasks.dataRefreshed'))
    } catch (error) {
      showErrorToast(t('referTasks.refreshFailed'))
    } finally {
      setIsRefreshing(false)
    }
  }

  // Handle claim task with debounce protection
  const handleClaimTask = useCallback(async (taskId: string) => {
    if (!user?._id) {
      showErrorToast(t('referTasks.loginRequired'))
      return
    }

    // Prevent multiple claim attempts
    if (isSubmitting || processingTask || claimedTasksMap[taskId]) {
      return
    }

    try {
      setIsSubmitting(true)
      setProcessingTask(taskId)
      setClaimError(null)
      
      const response = await fetch('/api/user/referral-tasks/claim', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ taskId })
      })

      const data = await response.json()
      
      if (!response.ok) {
        setClaimError(data.error || t('referTasks.claimFailed'))
        showErrorToast(data.error || t('referTasks.claimFailed'))
        return
      }
      
      // Update local state immediately to prevent multiple clicks
      setClaimedTasksMap(prev => ({
        ...prev,
        [taskId]: true
      }))
      
      // Show success message
      showSuccessToast(t('referTasks.claimSuccess', { amount: data.reward.amount.toLocaleString(), currency: data.reward.currency.toUpperCase() }))
      
      // Refresh user data to update claimed tasks
      try {
        // First try the refreshUser function from context
        await refreshUser()
      } catch (error) {
        console.error('Error refreshing user data with context method:', error)
        
        // Fallback to /api/auth/me if refreshUser fails
        try {
          const meResponse = await fetch('/api/auth/me')
          if (meResponse.ok) {
            const userData = await meResponse.json()
            if (userData.success && userData.data) {
              // No need to manually update state as it should be handled by AuthContext
              console.log('User data refreshed via /api/auth/me')
            }
          }
        } catch (refreshError) {
          console.error('Error refreshing user data via /api/auth/me:', refreshError)
        }
      }
      
    } catch (error) {
      console.error('Error claiming reward:', error)
      showErrorToast(t('referTasks.errorClaimingReward'))
    } finally {
      setProcessingTask(null)
      // Add a small delay before allowing another submission
      setTimeout(() => {
        setIsSubmitting(false)
      }, 1000)
    }
  }, [user, processingTask, claimedTasksMap, refreshUser, isSubmitting, t])

  // Check if a task is claimable
  const isTaskClaimable = (task: any) => {
    return totalReferrals >= task.required && !claimedTasksMap[task.id]
  }

  // Check if a task is claimed
  const isTaskClaimed = (taskId: string) => {
    return claimedTasksMap[taskId]
  }

  // Sort tasks to show unclaimed and claimable tasks at the top
  const sortedTasks = useMemo(() => {
    return [...referralTasks].sort((a, b) => {
      const aIsClaimed = claimedTasksMap[a.id];
      const bIsClaimed = claimedTasksMap[b.id];
      
      // First sort claimed tasks to the bottom
      if (aIsClaimed && !bIsClaimed) return 1;
      if (!aIsClaimed && bIsClaimed) return -1;
      
      // Then sort claimable tasks to the top
      const aIsClaimable = totalReferrals >= a.required && !claimedTasksMap[a.id];
      const bIsClaimable = totalReferrals >= b.required && !claimedTasksMap[b.id];
      
      if (aIsClaimable && !bIsClaimable) return -1;
      if (!aIsClaimable && bIsClaimable) return 1;
      
      // Then sort by required referrals (ascending)
      return a.required - b.required;
    });
  }, [referralTasks, claimedTasksMap, totalReferrals]);

  return (
    <div className="space-y-4 w-full">
      {/* Header description */}
      <div className="mb-4 bg-black/60 rounded-lg overflow-hidden border border-gray-800/50 flex justify-between items-center p-3">
        <div className="flex items-center">
          <div className="w-10 h-10 flex items-center justify-center rounded-lg mr-3 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-orange-900/80 to-orange-500/40"></div>
            <Gift className="w-5 h-5 text-white relative z-10" />
          </div>
          <div>
            <h3 className="text-sm font-medium text-white">{t('referTasks.referralRewards')}</h3>
            <p className="text-xs text-gray-400">{t('referTasks.inviteFriends')}</p>
          </div>
        </div>
        
        <button 
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="bg-black/60 hover:bg-black/80 text-gray-400 hover:text-green-400 text-xs px-2.5 py-1.5 rounded-md border border-gray-800/50 transition-colors duration-200 disabled:opacity-50 flex items-center"
        >
          <RefreshCw className={`w-3.5 h-3.5 mr-1.5 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span className="hidden sm:inline">{t('referTasks.refresh')}</span>
        </button>
      </div>

      {/* Tasks list */}
      <div className="space-y-4">
        {sortedTasks.map((task, index) => {
          const progress = calculateProgress(task.required)
          const isClaimable = isTaskClaimable(task)
          const isClaimed = isTaskClaimed(task.id)
          const isProcessing = processingTask === task.id

          return (
            <motion.div
              key={task.id}
              className={`bg-black/60 rounded-lg overflow-hidden relative border border-gray-800/50 hover:border-green-500/30 ${isClaimed ? 'opacity-60' : 'opacity-100'}`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: isClaimed ? 0.6 : 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ 
                y: -2,
                opacity: isClaimed ? 0.8 : 1,
                boxShadow: "0 4px 15px rgba(0, 0, 0, 0.2)",
              }}
            >
              {/* Background glow */}
              <div className="absolute -right-20 -top-20 w-40 h-40 bg-green-500/5 rounded-full blur-xl"></div>
              <div className="absolute -left-10 -bottom-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>
              
              {/* Progress bar */}
              <div 
                className={`absolute left-0 bottom-0 h-1 transition-all duration-1000 ${
                  isClaimed 
                    ? 'bg-green-500/70' 
                    : isClaimable 
                      ? 'bg-yellow-500/70' 
                      : 'bg-blue-500/70'
                }`}
                style={{ width: `${progress}%` }}
              />
              
              {/* Reward badge */}
              <div className="absolute top-2 right-2 bg-black/60 backdrop-blur-md border border-green-500/30 px-2 py-1 rounded-full flex items-center space-x-1">
                <Award className="w-3 h-3 text-green-400" />
                <span className="text-xs text-green-400 font-medium">{task.reward.amount.toLocaleString()} {task.reward.currency.toUpperCase()}</span>
              </div>
              
              {/* Icon and title */}
              <div className="p-3 pb-0">
                <div className="flex items-center">
                  <div className={`
                    w-10 h-10 flex items-center justify-center rounded-lg mr-3 overflow-hidden relative shadow-lg
                    ${isClaimed 
                      ? 'bg-gradient-to-br from-green-600 to-green-400' 
                      : isClaimable 
                        ? 'bg-gradient-to-br from-yellow-600 to-yellow-400' 
                        : 'bg-gradient-to-br from-blue-600 to-blue-400'}
                  `}>
                    <div className="absolute inset-0 opacity-20 mix-blend-overlay bg-gradient-to-br from-white via-transparent to-black"></div>
                    {task.icon}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-white">{t(`referralTasks.${task.id}.title`, { defaultValue: task.title })}</h3>
                    <div className="text-xs text-gray-400 flex items-center">
                      <Users className="w-3 h-3 mr-1 text-blue-400" />
                      <span>{t('referTasks.referralCount', { current: String(totalReferrals), required: String(task.required) })}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Description */}
              <div className="px-3 pt-2 pb-2">
                <p className="text-xs text-gray-400">{t(`referralTasks.${task.id}.description`, { defaultValue: task.description })}</p>
              </div>
              
              {/* Progress indicator */}
              <div className="px-3 pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex-1 h-1.5 bg-gray-800/70 rounded-full overflow-hidden mr-3">
                    <motion.div 
                      className={`h-full rounded-full ${
                        isClaimed 
                          ? 'bg-gradient-to-r from-green-600 to-green-400' 
                          : isClaimable 
                            ? 'bg-gradient-to-r from-yellow-600 to-yellow-400' 
                            : 'bg-gradient-to-r from-blue-600 to-blue-400'
                      }`}
                      initial={{ width: 0 }}
                      animate={{ width: `${progress}%` }}
                      transition={{ duration: 1, ease: "easeOut" }}
                    />
                  </div>
                  <span className={`text-xs font-medium ${
                    isClaimed 
                      ? 'text-green-400' 
                      : isClaimable 
                        ? 'text-yellow-400' 
                        : 'text-blue-400'
                  }`}>{Math.round(progress)}%</span>
                </div>
              </div>
              
              {/* Error message */}
              {claimError && processingTask === task.id && (
                <div className="mx-3 mb-2 flex items-center text-xs text-red-400 bg-red-500/10 p-2 rounded-md border border-red-500/30">
                  <AlertCircle className="w-3.5 h-3.5 mr-1.5 flex-shrink-0" />
                  <span>{claimError}</span>
                </div>
              )}
              
              {/* Action footer */}
              <div className="flex justify-end bg-black/30 p-2 border-t border-gray-800/50">
                {isClaimed ? (
                  <div className="bg-green-500/20 text-green-400 text-xs font-medium py-1.5 px-3 rounded-md border border-green-500/30 flex items-center space-x-1">
                    <Check className="w-3 h-3 mr-1" />
                    <span>{t('referTasks.claimed')}</span>
                  </div>
                ) : isClaimable ? (
                  <motion.button
                    onClick={() => handleClaimTask(task.id)}
                    disabled={isProcessing || isSubmitting}
                    className="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 text-xs font-medium py-1.5 px-3.5 rounded-md border border-yellow-500/30 backdrop-blur-sm flex items-center space-x-1 disabled:opacity-50"
                    whileTap={{ scale: isProcessing ? 1 : 0.95 }}
                  >
                    {isProcessing ? (
                      <Loader2 className="w-3 h-3 animate-spin mr-1" />
                    ) : (
                      <>
                        <Award className="w-3 h-3 mr-1" />
                        <span>{t('referTasks.claimReward')}</span>
                      </>
                    )}
                  </motion.button>
                ) : (
                  <div className="bg-blue-500/20 text-blue-400 text-xs font-medium py-1.5 px-3 rounded-md border border-blue-500/30 flex items-center space-x-1">
                    <Lock className="w-3 h-3 mr-1" />
                    <span>{t('referTasks.needMoreReferrals')}</span>
                  </div>
                )}
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Empty state if no tasks */}
      {referralTasks.length === 0 && (
        <div className="bg-black/60 rounded-lg overflow-hidden border border-gray-800/50 p-6 text-center">
          <div className="flex flex-col items-center justify-center">
            <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
              <AlertCircle className="w-6 h-6 text-blue-400" />
            </div>
            <p className="text-gray-300 mb-2 font-medium">{t('referTasks.noTasksAvailable')}</p>
            <p className="text-sm text-gray-500">{t('referTasks.checkBackLater')}</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default ReferTasks
