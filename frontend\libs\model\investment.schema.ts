import mongoose, { Schema } from 'mongoose';
import { IUser } from '../../types/user';
import { TIME_CONFIG } from '../config';

export enum InvestmentStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// Interface for claim history
export interface IClaimHistory {
  timestamp: Date;
  amount: number;
  type: 'zap' | 'referral'; // zap for standard earnings, referral for referral bonuses
}

// Investment interface
export interface IInvestment extends mongoose.Document {
  walletAddress: string;
  user: mongoose.Schema.Types.ObjectId | IUser;
  initialAmount: number;              // Initial investment amount
  earnedAmount: number;               // Total amount earned from ROI and referrals
  withdrawnAmount: number;            // Total amount withdrawn
  currency: string;                   // 'sol', 'flow', 'bonk'
  startDate: Date;                    // When investment started
  status: InvestmentStatus;           // Current investment status
  claimsSinceWithdrawal: number;      // Number of claims since last withdrawal
  lastWithdrawalTime: Date | null;    // Timestamp of last withdrawal
  lastCalculationTime: Date;          // Last time ROI was calculated
  currentZapProgress: number;         // Current accumulation progress toward next claim (0-1.5%)
  lastClaimTime: Date | null;         // Timestamp of last claim
  claims: IClaimHistory[];
  percentageOfROI: number;            // Percentage of ROI cap reached
  referralBonus: number;              // Total earned through referrals
  
  // Methods
  calculateCurrentZapProgress: () => number;
  canClaimZappedRewards: () => boolean;
  claimZappedRewards: () => Promise<{ success: boolean; amount: number; message: string }>;
  canWithdraw: () => boolean;
  withdraw: (amount: number) => Promise<{ success: boolean; amount: number; message: string }>;
  addReferralBonus: (amount: number) => Promise<{ success: boolean; message: string }>;
}

const ClaimHistorySchema = new Schema({
  timestamp: {
    type: Date,
    default: Date.now
  },
  amount: {
    type: Number,
    required: true
  },
  type: {
    type: String,
    enum: ['zap', 'referral'],
    default: 'zap'
  }
});

// Investment schema
const InvestmentSchema = new Schema<IInvestment>(
  {
    walletAddress: {
      type: String,
      required: true,
      index: true
    },
    // User who made the investment
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    // Initial investment amount
    initialAmount: {
      type: Number,
      required: true,
      min: 0
    },
    // Total amount earned from ROI and referrals (not including initial)
    earnedAmount: {
      type: Number,
      required: true,
      default: 0,
      min: 0
    },
    // Total amount withdrawn
    withdrawnAmount: {
      type: Number,
      default: 0,
      min: 0
    },
    // Investment currency
    currency: {
      type: String,
      required: true,
      enum: ['sol', 'flow', 'bonk'],
      default: 'sol'
    },
    // When investment was started
    startDate: {
      type: Date,
      required: true,
      default: Date.now
    },
    // Current status of investment
    status: {
      type: String,
      enum: Object.values(InvestmentStatus),
      default: InvestmentStatus.ACTIVE,
      index: true
    },
    // Last time ROI progress was calculated
    lastCalculationTime: {
      type: Date,
      default: Date.now
    },
    // Current progress toward next claim (0-1.5%)
    currentZapProgress: { // Use less shit
      type: Number,
      default: 0,
      min: 0,
      max: 1.5
    },
    // Timestamp of last claim
    lastClaimTime: {
      type: Date,
      default: null
    },
    // History of all claims
    claims: [ClaimHistorySchema],
    // Total earned from referrals
    referralBonus: {
      type: Number,
      default: 0
    },
    // Percentage of ROI cap reached
    percentageOfROI: {
      type: Number,
      default: 0,
      min: 0,
      max: 250
    },
  },
  {
    timestamps: true,
    versionKey: false
  }
);

// Create indexes for faster queries
InvestmentSchema.index({ user: 1, status: 1 });
InvestmentSchema.index({ startDate: -1 });
InvestmentSchema.index({ currency: 1 });

// Calculate current Zap progress (0-1.5%)
InvestmentSchema.methods.calculateCurrentZapProgress = function() {
  const now = new Date();
  const lastCalc = this.lastCalculationTime;
  
  // Return current progress if investment is not active
  if (this.status !== InvestmentStatus.ACTIVE) {
    return this.currentZapProgress;
  }
  
  // Calculate elapsed seconds since last calculation
  const elapsedSeconds = (now.getTime() - lastCalc.getTime()) / 1000;
  
  // Calculate accumulation rate based on config
  const secondRate = TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE / TIME_CONFIG.ROI_ACCUMULATION_SECONDS;
  
  // Calculate new progress
  let newProgress = this.currentZapProgress + (elapsedSeconds * secondRate);
  
  // Cap at 1.5%
  newProgress = Math.min(newProgress, TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE);
  
  // Save the calculation results to the database
  this.lastCalculationTime = now;
  this.currentZapProgress = newProgress;
  
  return newProgress;
};

// Check if user can claim Zapped rewards
InvestmentSchema.methods.canClaimZappedRewards = function() {
  // Calculate current Zap progress
  const currentProgress = this.calculateCurrentZapProgress();
  
  // Can only claim if bar is full (1.5%) and investment is active
  return this.status === InvestmentStatus.ACTIVE && 
         currentProgress >= TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE && 
         this.initialAmount > 0;
};

// Get the current value of the investment (initial + earned - withdrawn)
InvestmentSchema.methods.getCurrentValue = function() {
  return this.initialAmount + this.earnedAmount - this.withdrawnAmount;
};

// Get progress toward ROI cap as percentage (0-100%)
InvestmentSchema.methods.getROIProgress = function() {
  const maxEarnings = this.initialAmount * (TIME_CONFIG.TOTAL_ROI_CAP - 1);
  return Math.min(100, (this.earnedAmount / maxEarnings) * 100);
};

// Claim Zapped rewards
InvestmentSchema.methods.claimZappedRewards = async function() {
  // First check if claim is possible
  if (!this.canClaimZappedRewards()) {
    return {
      success: false,
      amount: 0,
      message: "Cannot claim yet. Zapping progress must reach 1.5% and investment must be active."
    };
  }
  
  // Calculate claim amount (1.5% of current investment value)
  const currentValue = this.getCurrentValue();
  const claimAmount = currentValue * TIME_CONFIG.SINGLE_CLAIM_PERCENTAGE;
  
  // Add to claims array
  this.claims.push({
    timestamp: new Date(),
    amount: claimAmount,
    type: 'zap'
  });
  
  // Update totals
  this.earnedAmount += claimAmount;
  this.lastClaimTime = new Date();
  this.currentZapProgress = 0; // Reset accumulation
  
  // Increment claims since last withdrawal
  this.claimsSinceWithdrawal += 1;
  
  // Check if total earned has reached the cap
  const maxEarnings = this.initialAmount * (TIME_CONFIG.TOTAL_ROI_CAP - 1);
  if (this.earnedAmount >= maxEarnings) {
    // Cap at max earnings
    const excess = this.earnedAmount - maxEarnings;
    this.earnedAmount = maxEarnings;
    
    // Mark as completed if cap is reached and all is withdrawn
    if (this.withdrawnAmount >= this.earnedAmount) {
      this.status = InvestmentStatus.COMPLETED;
    }
  }
  
  // Save changes
  await this.save();
  
  return {
    success: true,
    amount: claimAmount,
    message: "Zapped rewards claimed successfully! Your investment value has increased."
  };
};

// Check if investment can be withdrawn
InvestmentSchema.methods.canWithdraw = function() {
  const now = new Date();
  
  // Check if withdrawal timeout has passed
  const timeoutPassed = !this.lastWithdrawalTime || 
    (now.getTime() - this.lastWithdrawalTime.getTime()) >= (TIME_CONFIG.WITHDRAWAL_COOLDOWN_SECONDS * 1000);
  
  // Get available amount to withdraw (earned amount that hasn't been withdrawn)
  const availableToWithdraw = this.earnedAmount - this.withdrawnAmount;
  
  // Can withdraw if:
  // 1. Investment is active
  // 2. Has amount available to withdraw
  // 3. Has made required number of claims since last withdrawal
  // 4. Timeout has passed since last withdrawal
  return this.status === InvestmentStatus.ACTIVE && 
         availableToWithdraw > 0 &&
         this.claimsSinceWithdrawal >= TIME_CONFIG.CLAIMS_BEFORE_WITHDRAWAL && 
         timeoutPassed;
};

// Withdraw investment
InvestmentSchema.methods.withdraw = async function(amount: number) {
  // First check if withdrawal is possible
  if (!this.canWithdraw()) {
    return {
      success: false,
      amount: 0,
      message: "Cannot withdraw at this time. Check withdrawal requirements."
    };
  }

  // Calculate available amount to withdraw
  const availableToWithdraw = this.earnedAmount - this.withdrawnAmount;
  
  // Ensure we don't withdraw more than available
  const actualWithdrawAmount = Math.min(amount, availableToWithdraw);
  
  if (actualWithdrawAmount <= 0) {
    return {
      success: false,
      amount: 0,
      message: "No funds available to withdraw."
    };
  }

  // Update withdrawn amount
  this.withdrawnAmount += actualWithdrawAmount;
  
  // Check if investment should be marked as completed
  if (this.earnedAmount === this.withdrawnAmount && this.percentageOfROI >= 250) {
    this.status = InvestmentStatus.COMPLETED;
  }
  
  // Save changes
  await this.save();
  
  return {
    success: true,
    amount: actualWithdrawAmount,
    message: "Investment withdrawn successfully to your wallet balance!"
  };
};

// Add referral bonus to investment
InvestmentSchema.methods.addReferralBonus = async function(amount: number) {
  if (this.status !== InvestmentStatus.ACTIVE) {
    return {
      success: false,
      message: "Cannot add referral bonus to inactive investment"
    };
  }
  
  // Add to claims array
  this.claims.push({
    timestamp: new Date(),
    amount,
    type: 'referral'
  });
  
  // Update referral stats
  this.referralBonus += amount;
  
  // Update earned amount
  this.earnedAmount += amount;
  
  // Check if ROI cap is reached
  const maxEarnings = this.initialAmount * (TIME_CONFIG.TOTAL_ROI_CAP - 1);
  if (this.earnedAmount >= maxEarnings) {
    // Cap earnings at maximum
    const excess = this.earnedAmount - maxEarnings;
    this.earnedAmount = maxEarnings;
    
    // Mark as completed if all withdrawn
    if (this.withdrawnAmount >= this.earnedAmount) {
      this.status = InvestmentStatus.COMPLETED;
    }
  }
  
  // Save changes
  await this.save();
  
  return {
    success: true,
    message: "Referral bonus added successfully"
  };
};

// Static method to create a new investment
InvestmentSchema.statics.createInvestment = async function(
  userId: mongoose.Types.ObjectId | string,
  amount: number,
  currency: string = 'sol'
) {
  // Check if user has an active investment
  const existingInvestment = await this.findOne({
    user: userId,
    status: InvestmentStatus.ACTIVE
  });
  
  if (existingInvestment) {
    throw new Error('User already has an active investment');
  }
  
  // Create new investment
  const investment = new this({
    user: userId,
    initialAmount: amount,
    earnedAmount: 0,
    withdrawnAmount: 0,
    currency,
    startDate: new Date(),
    status: InvestmentStatus.ACTIVE,
    claimsSinceWithdrawal: TIME_CONFIG.CLAIMS_BEFORE_WITHDRAWAL - 1, // Start with 1 more claim needed (for testing)
    lastWithdrawalTime: null,
    lastCalculationTime: new Date(),
    currentZapProgress: 0,
    lastClaimTime: null,
    claims: [],
    referralBonus: 0
  });
  
  return await investment.save();
};

// Static method to get active investment for a user
InvestmentSchema.statics.getActiveInvestment = async function(userId: mongoose.Types.ObjectId | string) {
  return await this.findOne({
    user: userId,
    status: InvestmentStatus.ACTIVE
  });
};

// Static method to get all investments for a user
InvestmentSchema.statics.getUserInvestments = async function(userId: mongoose.Types.ObjectId | string) {
  return await this.find({
    user: userId
  }).sort({ createdAt: -1 });
};

// Add virtual property to get percentage of ROI cap reached
InvestmentSchema.virtual('roiProgressPercentage').get(function() {
  const maxEarnings = this.initialAmount * (TIME_CONFIG.TOTAL_ROI_CAP - 1);
  return Math.min(100, (this.earnedAmount / maxEarnings) * 100);
});

// Add virtual property to get current investment value
InvestmentSchema.virtual('currentValue').get(function() {
  return this.initialAmount + this.earnedAmount - this.withdrawnAmount;
});

// Export the model
export const Investment = mongoose.models.Investment || 
  mongoose.model<IInvestment>('Investment', InvestmentSchema);

export default Investment;
