"use client"

import React, { useState } from 'react'
import AdminNews from '@/components/news/AdminNews'
import CreateNews from '@/components/news/CreateNews'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, List } from 'lucide-react'

export default function NewsAdminPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">News Management</h2>
      </div>
      
      <Tabs defaultValue="list" className="w-full">
        <TabsList className="grid w-full md:w-[400px] grid-cols-2">
          <TabsTrigger value="list" className="flex items-center gap-2 cursor-pointer">
            <List className="h-4 w-4" />
            <span>News Articles</span>
          </TabsTrigger>
          <TabsTrigger value="create" className="flex items-center gap-2 cursor-pointer">
            <Plus className="h-4 w-4" />
            <span>Create Article</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="list" className="mt-6">
          <AdminNews />
        </TabsContent>
        
        <TabsContent value="create" className="mt-6">
          <CreateNews />
        </TabsContent>
      </Tabs>
    </div>
  )
}
