{"address": "6UX6hhvGZDitWT1Tfr2iUo7BpCFNmcPUccXKgB4cA2J7", "metadata": {"name": "flow_trade", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "add_more_funds", "discriminator": [123, 121, 137, 55, 94, 116, 185, 48], "accounts": [{"name": "investment", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [105, 110, 118, 101, 115, 116, 109, 101, 110, 116]}, {"kind": "account", "path": "user"}]}}, {"name": "contract_state", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 116, 114, 97, 99, 116, 95, 115, 116, 97, 116, 101]}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}]}}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "amount", "type": "u64"}, {"name": "referrer_keys", "type": {"vec": "pubkey"}}, {"name": "referrer_percentages", "type": "bytes"}]}, {"name": "admin_add_funds_to_pool", "discriminator": [37, 196, 132, 215, 220, 74, 62, 127], "accounts": [{"name": "contract_state", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 116, 114, 97, 99, 116, 95, 115, 116, 97, 116, 101]}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}]}}, {"name": "admin", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "admin_withdraw", "discriminator": [160, 166, 147, 222, 46, 220, 75, 224], "accounts": [{"name": "contract_state", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 116, 114, 97, 99, 116, 95, 115, 116, 97, 116, 101]}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}]}}, {"name": "admin", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "amount", "type": "u64"}]}, {"name": "claim_roi", "discriminator": [108, 190, 179, 14, 37, 105, 182, 193], "accounts": [{"name": "investment", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [105, 110, 118, 101, 115, 116, 109, 101, 110, 116]}, {"kind": "account", "path": "user"}]}}, {"name": "user", "writable": true, "signer": true}], "args": []}, {"name": "create_investment", "discriminator": [54, 240, 181, 138, 116, 58, 134, 175], "accounts": [{"name": "investment", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [105, 110, 118, 101, 115, 116, 109, 101, 110, 116]}, {"kind": "account", "path": "user"}]}}, {"name": "contract_state", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 116, 114, 97, 99, 116, 95, 115, 116, 97, 116, 101]}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}]}}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "amount", "type": "u64"}, {"name": "referrer_keys", "type": {"vec": "pubkey"}}, {"name": "referrer_percentages", "type": "bytes"}], "returns": "string"}, {"name": "get_investment_details", "discriminator": [8, 91, 141, 35, 186, 98, 116, 38], "accounts": [{"name": "investment", "pda": {"seeds": [{"kind": "const", "value": [105, 110, 118, 101, 115, 116, 109, 101, 110, 116]}, {"kind": "account", "path": "owner"}]}}, {"name": "owner", "signer": true}], "args": [], "returns": {"defined": {"name": "InvestmentDetails"}}}, {"name": "initialize", "discriminator": [175, 175, 109, 31, 13, 152, 155, 237], "accounts": [{"name": "contract_state", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 116, 114, 97, 99, 116, 95, 115, 116, 97, 116, 101]}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}]}}, {"name": "signer", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "admin", "type": "pubkey"}]}, {"name": "withdraw", "discriminator": [183, 18, 70, 156, 148, 109, 161, 34], "accounts": [{"name": "investment", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [105, 110, 118, 101, 115, 116, 109, 101, 110, 116]}, {"kind": "account", "path": "user"}]}}, {"name": "contract_state", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [99, 111, 110, 116, 114, 97, 99, 116, 95, 115, 116, 97, 116, 101]}]}}, {"name": "pool", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [112, 111, 111, 108]}]}}, {"name": "user", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "amount", "type": "u64"}, {"name": "source", "type": {"defined": {"name": "WithdrawalSource"}}}]}], "accounts": [{"name": "ContractState", "discriminator": [190, 138, 10, 223, 189, 116, 222, 115]}, {"name": "Investment", "discriminator": [175, 134, 9, 175, 115, 153, 39, 28]}, {"name": "Pool", "discriminator": [241, 154, 109, 4, 17, 177, 109, 188]}], "events": [{"name": "AdminDepositEvent", "discriminator": [165, 68, 63, 133, 0, 28, 136, 206]}, {"name": "AdminWithdrawalEvent", "discriminator": [121, 98, 210, 245, 8, 43, 45, 109]}, {"name": "FundsAddedEvent", "discriminator": [127, 31, 108, 255, 187, 19, 70, 68]}, {"name": "FundsWithdrawnEvent", "discriminator": [86, 232, 194, 4, 211, 69, 172, 202]}, {"name": "InvestmentCompletedEvent", "discriminator": [174, 175, 246, 153, 248, 75, 12, 153]}, {"name": "InvestmentCreatedEvent", "discriminator": [205, 116, 89, 98, 240, 106, 132, 38]}, {"name": "RoiClaimedEvent", "discriminator": [56, 204, 107, 40, 16, 84, 124, 50]}], "errors": [{"code": 6000, "name": "Unauthorized", "msg": "Unauthorized: Only admin can perform this action"}, {"code": 6001, "name": "MinInvestment", "msg": "Investment amount below minimum (0.05 SOL)"}, {"code": 6002, "name": "ExistingInvestment", "msg": "User already has an active investment"}, {"code": 6003, "name": "ArithmeticOverflow", "msg": "Arithmetic overflow occurred"}, {"code": 6004, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "msg": "Withdrawal amount below minimum (0.05 SOL)"}, {"code": 6005, "name": "WithdrawalCooldown", "msg": "24-hour withdrawal cooldown not met"}, {"code": 6006, "name": "InsufficientClaims", "msg": "Insufficient claims for withdrawal (need 5)"}, {"code": 6007, "name": "InsufficientBalance", "msg": "Insufficient balance for withdrawal"}, {"code": 6008, "name": "InsufficientPoolBalance", "msg": "Insufficient pool balance"}, {"code": 6009, "name": "InvalidReferralPercentage", "msg": "Invalid referral percentage"}, {"code": 6010, "name": "ReferrerInactive", "msg": "Referrer does not have an active investment"}, {"code": 6011, "name": "ClaimCooldown", "msg": "36-hour claim cooldown not met"}, {"code": 6012, "name": "<PERSON><PERSON><PERSON>apReached", "msg": "ROI cap of 250% reached"}, {"code": 6013, "name": "InvalidInvestmentStatus", "msg": "Investment is not active"}, {"code": 6014, "name": "MinDeposit", "msg": "Deposit amount below minimum (0.05 SOL)"}, {"code": 6015, "name": "TooManyReferrals", "msg": "Too many referrals provided"}, {"code": 6016, "name": "Invalid<PERSON><PERSON>errer", "msg": "Invalid referrer account"}, {"code": 6017, "name": "InvalidReferralData", "msg": "Referrer keys and percentages must have the same length"}, {"code": 6018, "name": "InsufficientReferrerAccounts", "msg": "Insufficient referrer accounts provided"}], "types": [{"name": "AdminDepositEvent", "type": {"kind": "struct", "fields": [{"name": "admin", "type": "pubkey"}, {"name": "amount", "type": "u64"}, {"name": "timestamp", "type": "i64"}]}}, {"name": "AdminWithdrawalEvent", "type": {"kind": "struct", "fields": [{"name": "admin", "type": "pubkey"}, {"name": "amount", "type": "u64"}, {"name": "timestamp", "type": "i64"}]}}, {"name": "ContractState", "type": {"kind": "struct", "fields": [{"name": "admin", "type": "pubkey"}, {"name": "pool_balance", "type": "u64"}]}}, {"name": "FundsAddedEvent", "type": {"kind": "struct", "fields": [{"name": "investment_pda", "type": "pubkey"}, {"name": "investment_id", "type": "string"}, {"name": "owner", "type": "pubkey"}, {"name": "amount", "type": "u64"}, {"name": "new_total", "type": "u64"}, {"name": "timestamp", "type": "i64"}]}}, {"name": "FundsWithdrawnEvent", "type": {"kind": "struct", "fields": [{"name": "investment_pda", "type": "pubkey"}, {"name": "investment_id", "type": "string"}, {"name": "owner", "type": "pubkey"}, {"name": "amount", "type": "u64"}, {"name": "source", "type": "string"}, {"name": "remaining_balance", "type": "u64"}, {"name": "timestamp", "type": "i64"}]}}, {"name": "Investment", "type": {"kind": "struct", "fields": [{"name": "owner", "type": "pubkey"}, {"name": "initial_investment", "type": "u64"}, {"name": "earned_amount", "type": "u64"}, {"name": "refer_earned_amount", "type": "u64"}, {"name": "percentage_of_roi", "type": "u64"}, {"name": "claims_since_withdrawal", "type": "u64"}, {"name": "last_claim_time", "type": "i64"}, {"name": "last_withdrawal_time", "type": "i64"}, {"name": "withdrawal_amount", "type": "u64"}, {"name": "withdrawn_refer_amount", "type": "u64"}, {"name": "status", "type": {"defined": {"name": "InvestmentStatus"}}}, {"name": "creation_time", "type": "i64"}, {"name": "investment_id", "type": "string"}, {"name": "total_claimed_amount", "type": "u64"}, {"name": "referral_count", "type": "u8"}, {"name": "last_action", "type": "string"}, {"name": "total_lifetime_claims", "type": "u64"}]}}, {"name": "InvestmentCompletedEvent", "type": {"kind": "struct", "fields": [{"name": "investment_pda", "type": "pubkey"}, {"name": "investment_id", "type": "string"}, {"name": "owner", "type": "pubkey"}, {"name": "initial_investment", "type": "u64"}, {"name": "total_earned", "type": "u64"}, {"name": "total_withdrawn", "type": "u64"}, {"name": "lifetime_days", "type": "u64"}, {"name": "timestamp", "type": "i64"}]}}, {"name": "InvestmentCreatedEvent", "type": {"kind": "struct", "fields": [{"name": "investment_pda", "type": "pubkey"}, {"name": "investment_id", "type": "string"}, {"name": "owner", "type": "pubkey"}, {"name": "amount", "type": "u64"}, {"name": "timestamp", "type": "i64"}, {"name": "referral_count", "type": "u8"}]}}, {"name": "InvestmentDetails", "type": {"kind": "struct", "fields": [{"name": "investment_id", "type": "string"}, {"name": "owner", "type": "pubkey"}, {"name": "initial_investment", "type": "u64"}, {"name": "earned_amount", "type": "u64"}, {"name": "refer_earned_amount", "type": "u64"}, {"name": "percentage_of_roi", "type": "u64"}, {"name": "claims_since_withdrawal", "type": "u64"}, {"name": "last_claim_time", "type": "i64"}, {"name": "last_withdrawal_time", "type": "i64"}, {"name": "withdrawal_amount", "type": "u64"}, {"name": "withdrawn_refer_amount", "type": "u64"}, {"name": "status", "type": {"defined": {"name": "InvestmentStatus"}}}]}}, {"name": "InvestmentStatus", "type": {"kind": "enum", "variants": [{"name": "Active"}, {"name": "Completed"}]}}, {"name": "Pool", "type": {"kind": "struct", "fields": []}}, {"name": "RoiClaimedEvent", "type": {"kind": "struct", "fields": [{"name": "investment_pda", "type": "pubkey"}, {"name": "investment_id", "type": "string"}, {"name": "owner", "type": "pubkey"}, {"name": "amount", "type": "u64"}, {"name": "timestamp", "type": "i64"}, {"name": "total_claimed", "type": "u64"}, {"name": "claims_count", "type": "u64"}]}}, {"name": "WithdrawalSource", "type": {"kind": "enum", "variants": [{"name": "Zap"}, {"name": "Referral"}, {"name": "Both"}]}}]}