"use client"
import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Wallet2,
  ArrowUpRight,
  ArrowDownLeft,
  BookOpen,
  Gift,
  HelpCircle,
  Check,
  Copy,
  LogOut,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import { useWallet } from '@/context/SolanaWalletContext' // Use our enhanced context
import WithdrawModal from './WithdrawModal'
import LanguageSelector from '../language/LanguageSelector'
import Image from 'next/image'
import { truncateAddress } from '@/utils/walletUtils'
import {WalletConnectButton} from './WalletConnectButton'
// import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: { y: 0, opacity: 1 }
};

const WalletPage = () => {
  const { user } = useAuth();
  const { t } = useLanguage();
  
  // Use enhanced wallet context
  const { 
    connected, 
    publicKey, 
    isLoading: walletLoading, 
    error: walletError, 
    connectionVerified,
    isInitialized,
    clearError,
    solPrice,
    bonkPrice,
    flowPrice,
    loadingPrice
  } = useWallet();
  
  const [solBalance, setSolBalance] = useState<number>(0);
  const [isInvestment, setIsInvestment] = useState<boolean>(false);
  const [isLoadingInvestment, setIsLoadingInvestment] = useState<boolean>(true);
  const [investmentError, setInvestmentError] = useState<string | null>(null);
  
  // State for the withdraw modal
  const [isWithdrawModalOpen, setIsWithdrawModalOpen] = useState(false);
  
  const fetchInvestments = async () => {
    if (!connected || !publicKey) {
      setIsInvestment(false);
      setIsLoadingInvestment(false);
      return;
    }

    try {
      setIsLoadingInvestment(true);
      setInvestmentError(null);
      
      const response = await fetch('/api/investment');
      const data = await response.json();

      setIsInvestment(data.success);
      
      if (!data.success && data.error) {
        setInvestmentError(data.error);
      }
    } catch (error) {
      console.error('Error fetching investments:', error);
      setInvestmentError('Failed to load investment data');
      setIsInvestment(false);
    } finally {
      setIsLoadingInvestment(false);
    }
  };

  // Fetch investments when wallet connection changes
  useEffect(() => {
    if (connected && connectionVerified && publicKey) {
      fetchInvestments();
    } else {
      setIsInvestment(false);
      setIsLoadingInvestment(false);
      setInvestmentError(null);
    }
  }, [connected, connectionVerified, publicKey]);

  // Helper functions to safely access nested user properties
  const getSolBalance = () => {
    if (!user?.wallet?.balance?.sol && user?.wallet?.balance?.sol !== 0 || !user?.referralStats?.rewardsSol && user?.referralStats?.rewardsSol !== 0) return 0;
    return user.wallet.balance.sol + user.referralStats.rewardsSol;
  };

  const getFlowBalance = () => {
    if (!user?.wallet?.balance?.flow && user?.wallet?.balance?.flow !== 0) return 0;
    return user.wallet.balance.flow;
  };

  const getBonkBalance = () => {
    if (!user?.wallet?.balance?.bonk && user?.wallet?.balance?.bonk !== 0) return 0;
    return user.wallet.balance.bonk;
  };

  // Calculate total assets in USD
  const getTotalAssetsUSD = () => {
    const solValue = solBalance * solPrice;
    const flowValue = getFlowBalance() * flowPrice;
    const bonkValue = getBonkBalance() * bonkPrice;
    return solValue + flowValue + bonkValue || 0;
  };

  // Get total assets in SOL equivalent
  const getTotalAssetsSol = () => {
    return getTotalAssetsUSD() / 100;
  };

  // Update solBalance when user data changes
  useEffect(() => {
    setSolBalance(getSolBalance());
  }, [user]);

  // Prepare assets data for display
  const assetsData = [
    { id: 1, name: 'Solana', ticker: 'SOL', amount: getSolBalance(), valueUSD: getSolBalance() * 100, icon: '/icon/solana.png' },
    { id: 2, name: 'Flow', ticker: 'FLOW', amount: getFlowBalance(), valueUSD: getFlowBalance() * 0.000002, icon: '/logo.jpg' },
    { id: 3, name: 'Bonk', ticker: 'BONK', amount: getBonkBalance(), valueUSD: getBonkBalance() * 0.00001, icon: '/icon/bonk.png' },
  ];

  // Open withdraw modal
  const openWithdrawModal = () => {
    setIsWithdrawModalOpen(true);
  };

  // Close withdraw modal
  const closeWithdrawModal = () => {
    setIsWithdrawModalOpen(false);
  };

  // Show loading state during wallet initialization
  if (!isInitialized) {
    return (
      <div className="relative min-h-screen p-4 md:p-6 overflow-hidden pt-4 pb-24 px-3">
        <div className="absolute -top-20 -left-20 w-60 h-60 bg-green-500 rounded-full filter blur-[100px] opacity-20 z-0" />
        <div className="absolute top-40 -right-20 w-80 h-80 bg-purple-500 rounded-full filter blur-[100px] opacity-10 z-0" />
        
        <div className="relative z-10 max-w-4xl mx-auto">
          <div className="flex items-center justify-center min-h-[50vh]">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="w-8 h-8 animate-spin text-green-500" />
              <p className="text-gray-400">Initializing wallet...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen p-4 md:p-6 overflow-hidden pt-4 pb-24 px-3">
      {/* Background glow effect */}
      <div className="absolute -top-20 -left-20 w-60 h-60 bg-green-500 rounded-full filter blur-[100px] opacity-20 z-0" />
      <div className="absolute top-40 -right-20 w-80 h-80 bg-purple-500 rounded-full filter blur-[100px] opacity-10 z-0" />
      
      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Header section */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center">
            <Wallet2 className="mr-3 text-green-500" size={28} />
            <h1 className="text-2xl md:text-3xl font-bold text-white">{t('wallet.title')}</h1>
          </div>
          <div className="flex space-x-3 items-center">
            <LanguageSelector variant="dropdown" className="mr-2" />
            {/* <motion.button 
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-3 py-1 rounded-lg border border-gray-700 text-gray-300 text-sm flex items-center"
            >
              {t('wallet.terms')}
            </motion.button> */}
          </div>
        </div>
        
        {/* Wallet Error Display */}
        {walletError && (
          <motion.div 
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-xl flex items-center justify-between"
          >
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-3" />
              <span className="text-red-400">{walletError}</span>
            </div>
            <button 
              onClick={clearError}
              className="text-red-400 hover:text-red-300 transition-colors"
            >
              ×
            </button>
          </motion.div>
        )}
        
        {/* Total assets card */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-black/60 backdrop-blur-lg border border-gray-800 rounded-2xl p-6 mb-6"
        >
          <p className="text-gray-400 mb-2">{t('wallet.totalAssets')}</p>
          <div className="flex items-end">
            <h2 className="text-3xl font-bold text-white">
              ${getTotalAssetsUSD().toLocaleString(undefined, { maximumFractionDigits: 2 })}
            </h2>
            <p className="text-gray-400 ml-3 pb-1">
              ≈ {getTotalAssetsSol().toFixed(2)} SOL
            </p>
          </div>
          
          {/* Enhanced wallet connection display */}
          <div className='my-3'>
            <WalletConnectButton />
              
          </div>
    
          {/* Action buttons with enhanced state handling */}
          <div className="flex mt-2 gap-3">
            <motion.a
              href='/'
              whileHover={{ scale: connected ? 1.02 : 1 }}
              whileTap={{ scale: connected ? 0.98 : 1 }}
              className={`flex-1 font-medium py-3 px-4 rounded-xl flex items-center justify-center transition-all ${
                connected && connectionVerified
                  ? 'bg-green-500 hover:bg-green-600 text-black cursor-pointer'
                  : ''
              }`}
              style={{ pointerEvents: connected && connectionVerified ? 'auto' : 'none' }}
            >
              {isLoadingInvestment ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Loading...
                </>
              ) : (
                <>
                  {isInvestment ? 'Fuel Up' : t('wallet.invest')} 
                  <ArrowUpRight size={16} className="ml-1" />
                </>
              )}
            </motion.a>
            
            <motion.button
              whileHover={{ scale: connected && connectionVerified ? 1.02 : 1 }}
              whileTap={{ scale: connected && connectionVerified ? 0.98 : 1 }}
              onClick={openWithdrawModal}
              disabled={!connected || !connectionVerified || walletLoading}
              className={`flex-1 font-medium py-3 px-4 rounded-xl flex items-center justify-center transition-all ${
                connected && connectionVerified && !walletLoading
                  ? 'bg-gray-800 hover:bg-gray-700 text-white cursor-pointer'
                  : 'bg-gray-600 text-gray-400 cursor-not-allowed'
              }`}
            >
              {walletLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Loading...
                </>
              ) : (
                <>
                  {t('wallet.withdraw')} 
                  <ArrowDownLeft size={16} className="ml-1" />
                </>
              )}
            </motion.button>
          </div>

      
        </motion.div>
        
        {/* Assets list */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold text-white mb-4">{t('wallet.assets')}</h3>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 gap-4"
          >
            {assetsData.filter(asset => asset.amount > 0 || asset.id <= 2).map((asset) => (
              <motion.div
                key={asset.id}
                variants={itemVariants}
                whileHover={{ scale: 1.02, backgroundColor: 'rgba(26, 27, 30, 0.8)' }}
                className="bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 transition-all duration-200"
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="text-2xl mr-3">
                      <Image 
                        src={asset.icon} 
                        alt={asset.name} 
                        className={`${asset.ticker === 'BONK' ? 'w-10 h-10' : asset.ticker === 'FLOW' ? 'w-7 h-7' : 'w-6 h-6'}`} 
                        width={asset.ticker === 'BONK' ? 42 : asset.ticker === 'FLOW' ? 28 : 24} 
                        height={asset.ticker === 'BONK' ? 42  : asset.ticker === 'FLOW' ? 28 : 24} 
                      />
                    </div>
                    <div>
                      <h4 className="font-medium text-white">{asset.name}</h4>
                      <p className="text-sm text-gray-400">
                        {asset.ticker === 'BONK' 
                          ? asset.amount.toLocaleString(undefined, { maximumFractionDigits: 0 }) 
                          : asset.amount.toLocaleString(undefined, { maximumFractionDigits: 4 })
                        } {asset.ticker}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-white">${asset.valueUSD.toLocaleString(undefined, { maximumFractionDigits: 2 })}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
        
        {/* Resources section */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"
        >
          <motion.a
            href="#tutorial"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center p-4 rounded-xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-gray-700"
          >
            <BookOpen className="text-green-500 mr-3" size={20} />
            <span className="text-gray-300">{t('wallet.tutorial')}</span>
          </motion.a>
          
          <motion.a
            href="#airdrop"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center p-4 rounded-xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-gray-700"
          >
            <Gift className="text-green-500 mr-3" size={20} />
            <span className="text-gray-300">{t('wallet.airdropInfo')}</span>
          </motion.a>
          
          <motion.a
            href="#faq"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center p-4 rounded-xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-gray-700"
          >
            <HelpCircle className="text-green-500 mr-3" size={20} />
            <span className="text-gray-300">{t('wallet.faqs')}</span>
          </motion.a>
        </motion.div>
      </div>
      
      {/* Withdraw Modal */}
      <WithdrawModal 
        isOpen={isWithdrawModalOpen}
        onClose={closeWithdrawModal}
      />
    </div>
  )
}

export default WalletPage;