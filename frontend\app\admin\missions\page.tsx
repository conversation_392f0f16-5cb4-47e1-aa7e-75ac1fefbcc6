"use client"

import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { PlusCircle } from 'lucide-react'
import AdminMissionList from '@/components/admin/missions/AdminMissionList'
import CreateMissionModal from '@/components/admin/missions/CreateMissionModal'
import EditMissionModal from '@/components/admin/missions/EditMissionModal'
import DeleteMissionDialog from '@/components/admin/missions/DeleteMissionDialog'
import { toast } from 'react-hot-toast'
import { IMission, MissionStatus } from '@/types/mission'
import { useRouter } from 'next/navigation'

export default function AdminMissionsPage() {
  const router = useRouter()
  
  // State
  const [missions, setMissions] = useState<IMission[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingMission, setEditingMission] = useState<IMission | null>(null)
  const [deletingMission, setDeletingMission] = useState<IMission | null>(null)

  // Fetch missions
  const fetchMissions = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/admin/missions')
      const data = await response.json()
      
      if (data.success) {
        setMissions(data.missions)
      } else {
        toast.error(data.error || 'Failed to fetch missions')
      }
    } catch (error) {
      console.error('Error fetching missions:', error)
      toast.error('An error occurred while fetching missions')
    } finally {
      setIsLoading(false)
    }
  }
  
  // Load missions on mount
  useEffect(() => {
    fetchMissions()
  }, [])
  
  // Handlers
  const handleMissionCreated = () => {
    setIsCreateModalOpen(false)
    fetchMissions()
    toast.success('Mission created successfully!')
  }
  
  const handleEditMission = (mission: IMission) => {
    setEditingMission(mission)
  }
  
  const handleMissionUpdated = () => {
    setEditingMission(null)
    fetchMissions()
    toast.success('Mission updated successfully!')
  }
  
  const handleDeleteMission = (mission: IMission) => {
    setDeletingMission(mission)
  }
  
  const handleMissionDeleted = () => {
    setDeletingMission(null)
    fetchMissions()
    toast.success('Mission deleted successfully!')
  }
  
  const handleToggleStatus = async (mission: IMission) => {
    try {
      const newStatus = mission.status === MissionStatus.ACTIVE 
        ? MissionStatus.INACTIVE 
        : MissionStatus.ACTIVE
      
      const response = await fetch(`/api/admin/missions/${mission._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        fetchMissions()
        toast.success(`Mission ${newStatus === MissionStatus.ACTIVE ? 'activated' : 'deactivated'} successfully!`)
      } else {
        toast.error(data.error || 'Failed to update mission status')
      }
    } catch (error) {
      console.error('Error updating mission status:', error)
      toast.error('An error occurred while updating mission status')
    }
  }
  
  const handleToggleHighlight = async (mission: IMission) => {
    try {
      const response = await fetch(`/api/admin/missions/${mission._id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isHighlighted: !mission.isHighlighted }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        fetchMissions()
        toast.success(`Mission ${!mission.isHighlighted ? 'highlighted' : 'unhighlighted'} successfully!`)
      } else {
        toast.error(data.error || 'Failed to update mission highlight status')
      }
    } catch (error) {
      console.error('Error updating mission highlight status:', error)
      toast.error('An error occurred while updating mission highlight status')
    }
  }
  
  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Missions</h2>
          <p className="text-muted-foreground">
            Manage missions for users to complete and earn rewards
          </p>
        </div>
        <Button 
          onClick={() => setIsCreateModalOpen(true)}
          className="flex items-center gap-2"
        >
          <PlusCircle className="h-4 w-4" />
          Create Mission
        </Button>
      </div>
      
      <div className="my-6 h-[1px] bg-border" />
      
      <AdminMissionList 
        missions={missions}
        isLoading={isLoading}
        onEdit={handleEditMission}
        onDelete={handleDeleteMission}
        onToggleStatus={handleToggleStatus}
        onToggleHighlight={handleToggleHighlight}
      />
      
      {/* Modals */}
      <CreateMissionModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onMissionCreated={handleMissionCreated}
      />
      
      {editingMission && (
        <EditMissionModal
          isOpen={!!editingMission}
          mission={editingMission}
          onClose={() => setEditingMission(null)}
          onMissionUpdated={handleMissionUpdated}
        />
      )}
      
      {deletingMission && (
        <DeleteMissionDialog
          isOpen={!!deletingMission}
          mission={deletingMission}
          onClose={() => setDeletingMission(null)}
          onMissionDeleted={handleMissionDeleted}
        />
      )}
    </div>
  )
}