"use client"

import { Toaster, toast } from "react-hot-toast"
import { Check, AlertCircle, X, Info } from "lucide-react"

// Custom toast components
const CustomToast = ({ t, message, type }: { t: any, message: string, type: 'success' | 'error' | 'info' }) => {
  return (
    <div 
      className={`${
        type === 'success' ? 'bg-green-500/20 border-green-500/30 text-green-400' :
        type === 'error' ? 'bg-red-500/20 border-red-500/30 text-red-400' :
        'bg-blue-500/20 border-blue-500/30 text-blue-400'
      } backdrop-blur-md border px-4 py-3 rounded-lg flex items-center space-x-3 min-w-[250px] max-w-md shadow-lg`}
    >
      {type === 'success' && <Check className="w-5 h-5 flex-shrink-0" />}
      {type === 'error' && <AlertCircle className="w-5 h-5 flex-shrink-0" />}
      {type === 'info' && <Info className="w-5 h-5 flex-shrink-0" />}
      
      <span className="flex-1">{message}</span>
      
      <button 
        onClick={() => toast.dismiss(t.id)}
        className="opacity-70 hover:opacity-100 transition-opacity"
      >
        <X className="w-4 h-4" />
      </button>
    </div>
  )
}

// Export custom toast helpers for easy use throughout the app
export const showSuccessToast = (message: string) => 
  toast.custom((t) => <CustomToast t={t} message={message} type="success" />);

export const showErrorToast = (message: string) => 
  toast.custom((t) => <CustomToast t={t} message={message} type="error" />);

export const showInfoToast = (message: string) => 
  toast.custom((t) => <CustomToast t={t} message={message} type="info" />);

export default function ToasterProvider() {
  return (
    <Toaster 
      position="top-right"
      toastOptions={{
        duration: 4000,
        style: {
          background: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(10px)',
          color: '#fff',
          border: '1px solid rgba(74, 222, 128, 0.3)',
        },
      }}
    />
  )
} 