"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Calendar, X, <PERSON>, <PERSON>rk<PERSON>, PartyPopper } from 'lucide-react'
import { useAuth } from '@/context/AuthContext'
import Confetti from 'react-confetti'
import { createPortal } from 'react-dom'

// Define custom rewards for each day of the week
const DAILY_REWARDS = [
  { day: 1, amount: 100, icon: '💫' },
  { day: 2, amount: 200, icon: '⚡' },
  { day: 3, amount: 350, icon: '🔥' },
  { day: 4, amount: 500, icon: '✨' },
  { day: 5, amount: 750, icon: '🎯' },
  { day: 6, amount: 1000, icon: '🚀' },
  { day: 7, amount: 1500, icon: '💎' },
]

const DailyStreak = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [canClaim, setCanClaim] = useState(false)
  const [showCelebration, setShowCelebration] = useState(false)
  const [claimData, setClaimData] = useState<any>(null)
  const [confettiActive, setConfettiActive] = useState(false)
  const { user, refreshUser } = useAuth()
  
  const dailyStreak = user?.dailyStreak || {
    currentStreak: 0,
    highestStreak: 0,
    lastClaimDate: null,
    totalDaysVisited: 0,
    weeklyProgress: 0,
    totalRewardsEarned: 0
  }

  // Get reward for the current day
  const getCurrentDayReward = (weekProgress: number) => {
    const dayIndex = weekProgress % 7; 
    return DAILY_REWARDS[dayIndex];
  }

  // Auto-claim reward on component mount
  useEffect(() => {
    const autoClaimReward = async () => {
      if (!user) return
      
      try {
        const response = await fetch('/api/streak/claim', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        const data = await response.json()

        if (response.ok && data.success) {
          setClaimData(data)
          setShowCelebration(true)
          setConfettiActive(true)
          await refreshUser()
          
          // Stop confetti after 5 seconds
          setTimeout(() => {
            setConfettiActive(false)
          }, 5000)
        } else if (response.status !== 400) { // Don't show error for "Already claimed today"
          // showErrorToast(data.error || 'Failed to claim reward')
        }
      } catch (error) {
        console.error('Error auto-claiming reward:', error)
      }
    }

    autoClaimReward()
  }, [user])

  // Check if user can claim
  useEffect(() => {
    const checkClaimEligibility = () => {
      if (!user?.dailyStreak?.lastClaimDate) {
        setCanClaim(true)
        return
      }

      const lastClaim = new Date(user.dailyStreak.lastClaimDate)
      const now = new Date()
      
      lastClaim.setHours(0, 0, 0, 0)
      now.setHours(0, 0, 0, 0)
      
      setCanClaim(now > lastClaim)
    }

    checkClaimEligibility()
  }, [user?.dailyStreak?.lastClaimDate])

  // Function to check if we're in a browser environment
  const isBrowser = () => typeof window !== 'undefined'

  return (
    <>
      {/* Floating button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className=" bg-black/80 hover:bg-black/90 text-white p-3 rounded-full border border-green-500/30 backdrop-blur-lg shadow-lg hover:shadow-green-500/10 transition-all duration-300"
        whileHover={{ scale: 1.05, boxShadow: "0 0 15px rgba(34, 197, 94, 0.3)" }}
        whileTap={{ scale: 0.95 }}
      >
        <div className="relative">
          <Calendar className="w-6 h-6 text-green-400" />
          {canClaim && (
            <motion.div
              className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ 
                type: "spring", 
                stiffness: 500, 
                damping: 30,
                repeat: Infinity,
                repeatType: "reverse",
                repeatDelay: 1,
                duration: 0.5
              }}
            />
          )}
        </div>
      </motion.button>

      {/* Stats Modal - Using Portal */}
      {isBrowser() && isOpen && createPortal(
        <AnimatePresence>
          <>
            <motion.div
              className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />

            <motion.div
              className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md bg-black/90 border border-green-500/20 rounded-xl p-6 z-50 backdrop-blur-xl shadow-[0_0_15px_rgba(34,197,94,0.15)]"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", duration: 0.5 }}
            >
              <button
                onClick={() => setIsOpen(false)}
                className="absolute top-4 right-4 text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>

              <div className="text-center mb-6">
                <div className="flex items-center justify-center mb-3">
                  <Flame className="w-6 h-6 text-orange-500 mr-2" />
                  <h2 className="text-xl font-bold text-white">Daily Streak</h2>
                </div>
                <p className="text-sm text-gray-400">
                  Come back daily to earn increasing rewards!
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-black/50 rounded-lg p-4 border border-green-500/20 backdrop-blur-md">
                  <p className="text-sm text-gray-400 mb-1">Current Streak</p>
                  <p className="text-2xl font-bold text-green-400">
                    {dailyStreak.currentStreak} days
                  </p>
                </div>
                <div className="bg-black/50 rounded-lg p-4 border border-green-500/20 backdrop-blur-md">
                  <p className="text-sm text-gray-400 mb-1">Highest Streak</p>
                  <p className="text-2xl font-bold text-yellow-400">
                    {dailyStreak.highestStreak} days
                  </p>
                </div>
              </div>

              <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm text-gray-400">Weekly Progress</p>
                  <p className="text-sm text-green-400">{dailyStreak.weeklyProgress}/7</p>
                </div>
                <div className="h-2.5 bg-black/50 rounded-full overflow-hidden border border-green-500/20 backdrop-blur-md">
                  <motion.div
                    className="h-full bg-gradient-to-r from-green-500 to-green-400"
                    initial={{ width: 0 }}
                    animate={{ width: `${(dailyStreak.weeklyProgress / 7) * 100}%` }}
                    transition={{ duration: 1, ease: "easeOut" }}
                  />
                </div>
              </div>

              {/* Weekly rewards visualization */}
              <div className="mb-6">
                <p className="text-sm text-gray-400 mb-3">Weekly Reward Schedule</p>
                <div className="grid grid-cols-7 gap-1.5">
                  {DAILY_REWARDS.map((reward, index) => {
                    const isActive = index < dailyStreak.weeklyProgress;
                    const isToday = index === dailyStreak.weeklyProgress;
                    
                    return (
                      <div 
                        key={reward.day}
                        className={`
                          relative px-1 py-2.5 rounded-lg flex flex-col items-center 
                          ${isActive 
                            ? 'bg-green-500/20 border border-green-500/40' 
                            : isToday 
                              ? 'bg-black/60 border border-green-500/30 shadow-[0_0_10px_rgba(34,197,94,0.15)]' 
                              : 'bg-black/40 border border-gray-800/50'}
                        `}
                      >
                        <span className="text-lg mb-0.5">{reward.icon}</span>
                        <span className={`text-xs font-medium ${isActive ? 'text-green-400' : isToday ? 'text-white' : 'text-gray-400'}`}>
                          {reward.amount}
                        </span>
                        {isToday && (
                          <span className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-green-400 rounded-full"></span>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              <div className="mt-2 pt-4 border-t border-green-500/20">
                <div className="flex justify-between items-center text-sm">
                  <p className="text-gray-400">Total Days Visited</p>
                  <p className="text-white">{dailyStreak.totalDaysVisited}</p>
                </div>
                <div className="flex justify-between items-center text-sm mt-2">
                  <p className="text-gray-400">Total FLOW Earned</p>
                  <p className="text-green-400">{dailyStreak.totalRewardsEarned}</p>
                </div>
              </div>
            </motion.div>
          </>
        </AnimatePresence>,
        document.body
      )}

      {/* Celebration Modal - Using Portal */}
      {isBrowser() && showCelebration && claimData && createPortal(
        <AnimatePresence>
          <>
            {confettiActive && (
              <Confetti
                width={window.innerWidth}
                height={window.innerHeight}
                recycle={false}
                numberOfPieces={300}
                gravity={0.15}
                colors={['#22c55e', '#10b981', '#34d399', '#6ee7b7', '#ffffff']}
                tweenDuration={5000}
              />
            )}

            <motion.div
              className="fixed inset-0 bg-black/70 backdrop-blur-md z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowCelebration(false)}
            />

            <motion.div
              className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-sm bg-black/60 border border-green-500/30 rounded-2xl p-8 z-50 backdrop-blur-xl overflow-hidden shadow-[0_0_30px_rgba(34,197,94,0.2)]"
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              transition={{ type: "spring", duration: 0.6, bounce: 0.35 }}
            >
              {/* Animated background effects */}
              <div className="absolute inset-0 overflow-hidden">
                <div className="absolute -top-20 -right-20 w-60 h-60 bg-green-500/10 rounded-full blur-3xl" />
                <div className="absolute -bottom-40 -left-20 w-80 h-80 bg-green-500/10 rounded-full blur-3xl" />
                <motion.div 
                  className="absolute top-10 left-10 w-2 h-2 bg-green-400/50 rounded-full blur-sm"
                  animate={{ 
                    y: [0, 10, -10, 0],
                    opacity: [0.5, 1, 0.8, 0.5],
                    scale: [1, 1.2, 0.8, 1]
                  }}
                  transition={{ repeat: Infinity, duration: 4, ease: "easeInOut" }}
                />
                <motion.div 
                  className="absolute bottom-10 right-10 w-3 h-3 bg-green-500/30 rounded-full blur-md"
                  animate={{ 
                    y: [0, -15, 10, 0],
                    opacity: [0.3, 0.6, 0.4, 0.3],
                    scale: [1, 1.5, 0.9, 1] 
                  }}
                  transition={{ repeat: Infinity, duration: 5, ease: "easeInOut" }}
                />
              </div>
              
              <div className="relative z-10">
                <div className="text-center mb-6">
                  <motion.div
                    initial={{ scale: 0, rotate: -20 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ type: "spring", delay: 0.1, stiffness: 200 }}
                    className="w-20 h-20 mx-auto mb-5 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-[0_0_20px_rgba(34,197,94,0.3)]"
                  >
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                    >
                      <PartyPopper className="w-10 h-10 text-white" />
                    </motion.div>
                  </motion.div>

                  <div className="relative">
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <h2 className="text-2xl font-bold text-white mb-2">
                        Daily Reward Claimed!
                      </h2>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, scale: 0.6 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
                      className="relative"
                    >
                      <div className="flex items-center justify-center space-x-2">
                        <span className="text-6xl font-bold bg-gradient-to-br from-green-300 via-green-400 to-green-300 bg-clip-text text-transparent">
                          {claimData.reward}
                        </span>
                        <span className="text-2xl font-bold text-green-400">FLOW</span>
                      </div>
                      
                      {/* Animated sparkles */}
                      <motion.div 
                        className="absolute -top-2 -right-2"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.8 }}
                      >
                        <Sparkles className="w-5 h-5 text-yellow-400" />
                      </motion.div>
                      <motion.div 
                        className="absolute -bottom-1 -left-3"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 1.0 }}
                      >
                        <Sparkles className="w-4 h-4 text-green-300" />
                      </motion.div>
                    </motion.div>
                  </div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                  className="space-y-3 mb-5"
                >
                  <div className="bg-black/40 rounded-lg p-4 border border-green-500/30 backdrop-blur-lg shadow-[0_0_10px_rgba(34,197,94,0.1)]">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Flame className="w-4 h-4 mr-2 text-orange-400" />
                        <span className="text-gray-300">Current Streak</span>
                      </div>
                      <span className="text-green-400 font-medium text-lg">
                        {claimData.streak.current} days
                      </span>
                    </div>
                  </div>
                  
                  {/* Weekly progress with visual indicator */}
                  <div className="bg-black/40 rounded-lg p-4 border border-green-500/30 backdrop-blur-lg shadow-[0_0_10px_rgba(34,197,94,0.1)]">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-2 text-blue-400" />
                        <span className="text-gray-300">Weekly Progress</span>
                      </div>
                      <span className="text-green-400 font-medium">{claimData.streak.weeklyProgress}/7</span>
                    </div>
                    
                    <div className="mt-2">
                      <div className="h-2 bg-black/70 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-gradient-to-r from-green-500 to-green-400"
                          initial={{ width: 0 }}
                          animate={{ width: `${(claimData.streak.weeklyProgress / 7) * 100}%` }}
                          transition={{ duration: 1, ease: "easeOut", delay: 0.8 }}
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>

                <div className="text-center text-gray-400 text-sm mb-5">
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.0 }}
                  >
                    Come back tomorrow for another reward!
                  </motion.p>
                </div>

                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.9 }}
                  onClick={() => setShowCelebration(false)}
                  className="w-full bg-gradient-to-r from-green-500 to-green-400 text-white py-3.5 rounded-xl font-medium text-lg shadow-[0_5px_15px_rgba(34,197,94,0.25)] hover:shadow-[0_5px_25px_rgba(34,197,94,0.35)] transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Awesome!
                </motion.button>
              </div>
            </motion.div>
          </>
        </AnimatePresence>,
        document.body
      )}
    </>
  )
}

export default DailyStreak