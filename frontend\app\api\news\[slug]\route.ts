import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { News } from '@/libs/model/news.schema';
import { User } from '@/libs/model/user.schema';

// Add cache headers to improve performance
function setCacheHeaders(response: NextResponse) {
  // Cache for 5 minutes on CDN and browser
  response.headers.set('Cache-Control', 'public, s-maxage=300, stale-while-revalidate=60');
  return response;
}

export async function GET(
  req: NextRequest,
  { params }: any
) {
  try {
    const slug = params.slug;
    
    if (!slug) {
      return NextResponse.json(
        { success: false, error: 'Slug is required' },
        { status: 400 }
      );
    }
    
    // Connect to the database
    await connectToDatabase();
    
    // Try to find by slug first, then by ID if not found
    let article = await News.findOne({ 
      slug: slug,
      isPublished: true,
    });
    
    // If not found by slug, try to find by ID
    if (!article) {
      article = await News.findOne({ 
        _id: slug,
        isPublished: true, 
      });
    }
    
    if (!article) {
      return NextResponse.json(
        { success: false, error: 'Article not found' },
        { status: 404 }
      );
    }
    
    // Increment view count
    article.viewsCount = (article.viewsCount || 0) + 1;
    await article.save();
    
    // Get user ID from cookie or headers for liked status
    const userId = req.cookies.get('userId')?.value;
    const telegramId = req.headers.get('x-telegram-id') || '';
    
    // Check if the user has liked this article
    let userLiked = false;
    
    if (userId || telegramId) {
      let user;
      
      // Find user by userId or telegramId
      if (userId) {
        user = await User.findById(userId);
      }
      
      if (!user && telegramId) {
        user = await User.findOne({ telegramId });
      }
      
      if (user) {
        // Check if user has liked the article
        userLiked = article.likes.some((like: any) => 
          like.userId.toString() === user._id.toString()
        );
      }
    }
    
    // Format the article for response
    const formattedArticle = {
      ...article.toObject(),
      userLiked,
      // Don't send the full likes array to the client for privacy/security reasons
      likes: undefined,
      likesCount: article.likes.length, // Ensure accurate count
    };
    
    // Return success response with cache headers
    const response = NextResponse.json({
      success: true,
      data: formattedArticle
    });
    
    return setCacheHeaders(response);
    
  } catch (error) {
    console.error('Error fetching article:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch article' },
      { status: 500 }
    );
  }
}