"use client"
import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCir<PERSON>, Gift, Star } from 'lucide-react'
import TasksComponent from './TasksComponent'
import PartnerRewardsComponent from './PartnerRewardsComponent'
import SpecialComponent from './SpecialComponent'
import { IMission, MissionType } from '@/types/mission'
import { useLanguage } from '@/context/LanguageContext'

interface MissionsProps {
  initialMissions: IMission[]
}

const Missions = ({ initialMissions }: MissionsProps) => {
  const [activeTab, setActiveTab] = useState('tasks')
  const [missions, setMissions] = useState<IMission[]>(initialMissions)
  const [isLoading, setIsLoading] = useState(false)
  const { t } = useLanguage()

  // Filter missions by type for each component
  const taskMissions = missions.filter(mission => mission.type === MissionType.TASK)
  const partnerMissions = missions.filter(mission => mission.type === MissionType.PARTNER)

  // Refresh missions data from client-side if needed - with optimized caching
  const refreshMissions = useCallback(async () => {
    try {
      setIsLoading(true)
      // Use relative path for client-side fetching with caching
      const response = await fetch('/api/missions')
      
      if (!response.ok) {
        throw new Error('Failed to fetch missions')
      }
      
      const data = await response.json()
      
      if (data.missions) {
        setMissions(data.missions)
      }
    } catch (error) {
      console.error('Error fetching missions:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const tabs = [
    { id: 'tasks', name: t('missions.tasks'), icon: CheckCircle },
    { id: 'partnerRewards', name: t('missions.partnerRewards'), icon: Gift },
    { id: 'special', name: t('missions.special'), icon: Star },
  ]

  return (
    <div className="max-w-[500px] mx-auto">
      {/* Header - Simplified without unnecessary animations */}
      <div className="relative mb-5 flex items-center">
        <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-8 bg-gradient-to-b from-green-400 to-transparent rounded-full"></div>
        <h1 className="text-xl font-bold ml-3 text-white">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-400 to-green-300">{t('missions.title')}</span>
        </h1>
        {/* Decorative elements */}
        <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-20 h-[1px] bg-gradient-to-r from-green-500/50 to-transparent"></div>
        <div className="absolute right-5 -top-2 w-3 h-3 rounded-full bg-green-500/20 blur-xl"></div>
        <div className="absolute right-3 -bottom-2 w-2 h-2 rounded-full bg-green-500/30 blur-md"></div>
      </div>

      <div className="grid grid-cols-3 gap-3 mb-5">
        {tabs.map((tab) => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id
          
          return (
            <div
              key={tab.id}
              className={`
                relative cursor-pointer rounded-lg border p-3 flex flex-col items-center justify-center
                transition-all duration-200 backdrop-blur-sm hover:-translate-y-1
                ${isActive 
                  ? 'border-green-500/50 bg-black/70 shadow-lg shadow-green-500/10' 
                  : 'border-gray-800/50 bg-black/30 hover:bg-black/40'}
              `}
              onClick={() => setActiveTab(tab.id)}
            >
              {isActive && (
                <motion.div 
                  className="absolute inset-0 -z-10 rounded-lg opacity-30"
                  layoutId="tabGlow"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  style={{ boxShadow: "0 0 15px 2px rgba(34, 197, 94, 0.2)" }}
                />
              )}

              <div className={`
                mb-1.5 p-1.5 rounded
                ${isActive ? 'text-green-400' : 'text-gray-400'}
              `}>
                <Icon className={`w-5 h-5 ${isActive ? 'stroke-[2.5px]' : 'stroke-[1.5px]'}`} />
              </div>
              <span className={`
                text-xs font-medium text-center
                ${isActive ? 'text-green-400' : 'text-gray-400'}
              `}>
                {tab.name}
              </span>
              
              {isActive && (
                <motion.div
                  layoutId="activeTabIndicator"
                  className="absolute bottom-0 w-10 h-[2px] bg-green-500/70 rounded-full"
                  transition={{ duration: 0.2 }}
                />
              )}
            </div>
          )
        })}
      </div>

      <div className="relative bg-black/30 backdrop-blur-md rounded-xl border border-gray-800/50 overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-green-500/20 to-transparent"></div>
        <div className="absolute -right-10 -top-10 w-20 h-20 bg-green-500/5 rounded-full blur-xl"></div>
        <div className="absolute -left-5 bottom-10 w-10 h-10 bg-green-500/5 rounded-full blur-lg"></div>
        
        <div className="p-3">
          <AnimatePresence mode="wait">
            {activeTab === 'tasks' && (
              <motion.div
                key="tasks"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <TasksComponent missions={taskMissions} isLoading={isLoading} refreshMissions={refreshMissions} />
              </motion.div>
            )}
            {activeTab === 'partnerRewards' && (
              <motion.div
                key="partnerRewards"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <PartnerRewardsComponent missions={partnerMissions} isLoading={isLoading} refreshMissions={refreshMissions} />
              </motion.div>
            )}
            {activeTab === 'special' && (
              <motion.div
                key="special"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <SpecialComponent />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}

export default Missions