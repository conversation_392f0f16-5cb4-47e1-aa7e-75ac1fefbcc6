"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Edit, Trash2, Eye, Search, AlertCircle, Loader2 } from 'lucide-react'
import { Badge } from "@/components/ui/badge"
import { toast } from "react-hot-toast"
import Link from 'next/link'
import EditNewsModal, { NewsArticle } from '@/components/news/EditNewsModal'

// Types for API response
interface NewsResponse {
  success: boolean;
  data: NewsArticle[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

const categoryColors = {
  MARKET: "bg-blue-100 text-blue-800",
  DEFI: "bg-purple-100 text-purple-800",
  TECHNOLOGY: "bg-gray-100 text-gray-800",
  TRADING: "bg-green-100 text-green-800",
  GENERAL: "bg-yellow-100 text-yellow-800"
};

const AdminNews = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [news, setNews] = useState<NewsArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  
  // Add state for edit modal
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editArticleId, setEditArticleId] = useState<string | null>(null);

  // Fetch news articles
  const fetchNews = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let url = `/api/admin/news?page=${page}&limit=10`;
      
      if (searchTerm) {
        url += `&query=${encodeURIComponent(searchTerm)}`;
      }
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch news articles');
      }
      
      const data: NewsResponse = await response.json();
      
      if (data.success) {
        setNews(data.data);
        setTotalPages(data.pagination.pages);
      } else {
        throw new Error('Failed to fetch news articles');
      }
    } catch (error) {
      console.error('Error fetching news:', error);
      setError('Failed to load news articles. Please try again.');
      toast.error('Failed to load news articles');
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch on component mount
  useEffect(() => {
    fetchNews();
  }, [page]);

  // Fetch when search term changes (with debounce)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (page === 1) {
        fetchNews();
      } else {
        setPage(1);
      }
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Handle delete article
  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this article?")) {
      try {
        setIsDeleting(id);
        
        const response = await fetch(`/api/admin/news?id=${id}`, {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          throw new Error('Failed to delete article');
        }
        
        const data = await response.json();
        
        if (data.success) {
          setNews(prevNews => prevNews.filter(article => article._id !== id));
          toast.success('Article deleted successfully');
        } else {
          throw new Error(data.error || 'Failed to delete article');
        }
      } catch (error) {
        console.error('Error deleting article:', error);
        toast.error('Failed to delete article');
      } finally {
        setIsDeleting(null);
      }
    }
  };

  // Handle open edit modal
  const handleOpenEditModal = (article: NewsArticle) => {
    setEditArticleId(article._id);
    setIsEditModalOpen(true);
  };
  
  // Handle close edit modal
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setEditArticleId(null);
  };
  
  // Handle successful update
  const handleUpdateSuccess = () => {
    fetchNews();
    toast.success("News article updated successfully");
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            type="text"
            placeholder="Search articles..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>News Articles</CardTitle>
        </CardHeader>
        <CardContent>
          {loading && news.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400 mb-2" />
              <p className="text-gray-500">Loading news articles...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-10">
              <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
              <p className="text-gray-700 font-medium">{error}</p>
              <Button variant="outline" className="mt-4" onClick={fetchNews}>
                Try Again
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left font-medium p-2.5">Title</th>
                    <th className="text-left font-medium p-2.5">Category</th>
                    <th className="text-left font-medium p-2.5">Author</th>
                    <th className="text-left font-medium p-2.5">Status</th>
                    <th className="text-left font-medium p-2.5">Stats</th>
                    <th className="text-right font-medium p-2.5">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {news.length > 0 ? (
                    news.map((article) => (
                      <tr key={article._id} className="border-b hover:bg-gray-50">
                        <td className="p-2.5 min-w-[200px]">
                          <div>
                            <div className="font-medium text-gray-800 line-clamp-1">
                              {article.title}
                            </div>
                            <div className="text-gray-500 text-xs mt-1">
                              {article.publishedAt ? new Date(article.publishedAt).toLocaleDateString() : 'Draft'}
                            </div>
                          </div>
                        </td>
                        <td className="p-2.5">
                          <Badge variant="secondary" className={categoryColors[article.category as keyof typeof categoryColors]}>
                            {article.category}
                          </Badge>
                        </td>
                        <td className="p-2.5 text-gray-600">
                          {article.authorName}
                        </td>
                        <td className="p-2.5">
                          {article.isPublished ? (
                            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                              Published
                            </span>
                          ) : (
                            <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                              Draft
                            </span>
                          )}
                          {article.isHighlighted && (
                            <span className="ml-1 inline-flex items-center rounded-full bg-amber-100 px-2.5 py-0.5 text-xs font-medium text-amber-800">
                              Highlighted
                            </span>
                          )}
                        </td>
                        <td className="p-2.5">
                          <div className="flex space-x-2 text-xs">
                            <div className="text-blue-600">
                              {article.viewsCount || 0} views
                            </div>
                            <div className="text-red-500">
                              {article.likesCount || 0} likes
                            </div>
                          </div>
                        </td>
                        <td className="p-2.5 text-right">
                          <div className="flex justify-end space-x-1">
                            <Link href={`/news/${article.slug}`} target="_blank">
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8" 
                              onClick={() => handleOpenEditModal(article)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => handleDelete(article._id)}
                              disabled={isDeleting === article._id}
                            >
                              {isDeleting === article._id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="p-8 text-center text-gray-500">
                        No news articles found matching your search.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center space-x-2 mt-6">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1 || loading}
              >
                Previous
              </Button>
              <div className="flex items-center text-sm text-gray-600">
                Page {page} of {totalPages}
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages || loading}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <EditNewsModal
        isOpen={isEditModalOpen}
        onClose={handleCloseEditModal}
        articleId={editArticleId}
        articleData={news.find(article => article._id === editArticleId)}
        onSuccess={handleUpdateSuccess}
      />
    </div>
  );
};

export default AdminNews