import { Syne } from "next/font/google"
import "./globals.css"
import AnimatedGrid from "@/components/initial/AnimatedGrid"
import ClientShell from "@/components/initial/ClientShell"
import NavigationWrapper from "@/components/initial/NavigationWrapper"
import ToasterProvider from "@/components/initial/ToasterProvider"
import TickerBar from "@/components/initial/TickerBar"
import { AuthProvider } from "@/context/AuthContext"
import { LanguageProvider } from "@/context/LanguageContext"
import { SolanaWalletProvider } from "@/context/SolanaWalletContext"
const syne = Syne({
  variable: "--font-syne",
  subsets: ["latin"],
  weight: ['400', '500', '600', '700'],
  display: 'swap',
})
export const metadata = {
  title: "FLOW TRADE | Crypto Bot",
  description: "Advanced crypto trading bot with algorithmic strategies",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="color-scheme" content="dark" />
        <meta name="theme-color" content="#000000" />
      </head>
      <body 
        className={`${syne.className} antialiased bg-black`}
        style={{ 
          WebkitTapHighlightColor: "transparent",
          margin: "0 auto",
          minHeight: "100vh",
          position: "relative"
        }}
      >
        <ClientShell>
          <SolanaWalletProvider>
            <AuthProvider>
              <LanguageProvider>
                <div className="min-h-screen">
                  <AnimatedGrid />
                  <TickerBar />
                  <main className="relative z-10 pt-8">
                    {/* <LanguageSelector variant="dropdown" className="mr-2 absolute top-0 right-0" /> */}
                    {children}  
                  </main>  
                  <NavigationWrapper />
                </div>
                <ToasterProvider />
              </LanguageProvider>
            </AuthProvider>
          </SolanaWalletProvider>
        </ClientShell>
      </body>
    </html>
  )
}