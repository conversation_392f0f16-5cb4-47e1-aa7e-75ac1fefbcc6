import React from 'react'
import { IMission, MissionStatus, MissionType, RewardType } from '@/types/mission'

interface AdminMissionListProps {
  missions: IMission[]
  isLoading: boolean
  onEdit: (mission: IMission) => void
  onDelete: (mission: IMission) => void
  onToggleStatus: (mission: IMission) => void
  onToggleHighlight: (mission: IMission) => void
}

const AdminMissionList = ({
  missions,
  isLoading,
  onEdit,
  onDelete,
  onToggleStatus,
  onToggleHighlight
}: AdminMissionListProps) => {
  // Function to render mission type badge
  const renderTypeBadge = (type: MissionType) => {
    const colors = {
      [MissionType.TASK]: 'bg-green-100 text-green-800',
      [MissionType.PARTNER]: 'bg-purple-100 text-purple-800',
    }
    
    // Use a default color if type is not explicitly mapped
    return (
      <span className={`px-2 py-1 rounded-full text-xs ${colors[type] || 'bg-gray-100 text-gray-800'}`}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </span>
    )
  }
  
  // Function to render status badge
  const renderStatusBadge = (status: MissionStatus) => {
    const colors = {
      [MissionStatus.ACTIVE]: 'bg-green-100 text-green-800',
      [MissionStatus.INACTIVE]: 'bg-gray-100 text-gray-800',
      [MissionStatus.COMPLETED]: 'bg-blue-100 text-blue-800',
    }
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs ${colors[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }
  
  // Function to render reward
  const renderReward = (rewardType: RewardType, amount: number) => {
    const colors = {
      [RewardType.SOL]: 'text-purple-600',
      [RewardType.FLOW]: 'text-blue-600',
    }
    
    return (
      <span className={`font-medium ${colors[rewardType]}`}>
        {amount} {rewardType.toUpperCase()}
      </span>
    )
  }

  // Format date helper function
  const formatDate = (dateString: string | Date | undefined) => {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }

  if (isLoading) {
    return (
      <div className="h-96 flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-green-200 border-t-green-600 rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <caption className="caption-bottom p-4 text-gray-500 text-sm">
            List of all missions
          </caption>
          <thead className="bg-gray-50">
            <tr className="border-b border-gray-200">
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[250px]">Title</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Reward</th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Participants</th>
              <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {missions.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center h-24 text-gray-500 px-4 py-3">
                  No missions found. Create your first mission to get started.
                </td>
              </tr>
            ) : (
              missions.map((mission) => (
                <tr key={mission._id?.toString()} className={`hover:bg-gray-50 ${mission.isHighlighted ? 'bg-yellow-50' : ''}`}>
                  <td className="px-4 py-3">
                    <div className="flex flex-col">
                      <span className="font-medium text-gray-900">
                        {mission.isHighlighted && (
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-yellow-500 inline-block mr-1" viewBox="0 0 24 24" fill="currentColor" stroke="none">
                            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                          </svg>
                        )}
                        {mission.title}
                      </span>
                      <span className="text-gray-500 text-xs truncate max-w-[250px]">
                        {mission.description}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3">{renderTypeBadge(mission.type)}</td>
                  <td className="px-4 py-3">{renderStatusBadge(mission.status)}</td>
                  <td className="px-4 py-3 text-center">
                    {renderReward(mission.reward.type, mission.reward.amount)}
                  </td>
                  <td className="px-4 py-3 text-center">
                    <span className="font-medium text-gray-900">{mission.currentParticipants}</span>
                  </td>
                  <td className="px-4 py-3 text-center text-gray-500 text-sm">
                    {formatDate(mission.createdAt)}
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className="flex justify-end space-x-1">
                      <button 
                        className="p-1 rounded hover:bg-gray-100"
                        onClick={() => onToggleHighlight(mission)}
                        title={mission.isHighlighted ? "Remove highlight" : "Highlight mission"}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className={`w-4 h-4 ${mission.isHighlighted ? 'text-yellow-500' : 'text-gray-400'}`} viewBox="0 0 24 24" fill={mission.isHighlighted ? "currentColor" : "none"} stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                        </svg>
                      </button>
                      
                      <button 
                        className="p-1 rounded hover:bg-gray-100"
                        onClick={() => onToggleStatus(mission)}
                        title={mission.status === MissionStatus.ACTIVE ? "Deactivate" : "Activate"}
                      >
                        {mission.status === MissionStatus.ACTIVE ? (
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-green-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="1" y="5" width="22" height="14" rx="7" ry="7"></rect>
                            <circle cx="16" cy="12" r="3"></circle>
                          </svg>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="1" y="5" width="22" height="14" rx="7" ry="7"></rect>
                            <circle cx="8" cy="12" r="3"></circle>
                          </svg>
                        )}
                      </button>
                      
                      <button 
                        className="p-1 rounded hover:bg-gray-100"
                        onClick={() => onEdit(mission)}
                        title="Edit mission"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                      </button>
                      
                      <button 
                        className="p-1 rounded hover:bg-gray-100"
                        onClick={() => onDelete(mission)}
                        title="Delete mission"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-red-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="3 6 5 6 21 6"></polyline>
                          <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                          <line x1="10" y1="11" x2="10" y2="17"></line>
                          <line x1="14" y1="11" x2="14" y2="17"></line>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default AdminMissionList 