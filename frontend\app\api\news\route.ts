import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { News } from '@/libs/model/news.schema';
import { User } from '@/libs/model/user.schema';

export async function GET(req: NextRequest) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Get URL parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 20); // Max 20 items per page
    const query = searchParams.get('query') || '';
    const category = searchParams.get('category') || '';
    const isPublished = searchParams.get('isPublished') === 'true' ? true : undefined;
    const isHighlighted = searchParams.get('isHighlighted') === 'true' ? true : undefined;
    
    // Get user ID from cookie or headers for liked status
    const userId = req.cookies.get('userId')?.value;
    const telegramId = req.headers.get('x-telegram-id') || '';
    
    // Build filter
    const filter: any = {};
    
    if (query) {
      filter.$or = [
        { title: { $regex: query, $options: 'i' } },
        { summary: { $regex: query, $options: 'i' } },
      ];
    }
    
    if (category) {
      filter.category = category;
    }
    
    // Default to published articles for public view
    if (isPublished !== undefined) {
      filter.isPublished = isPublished;
    } else {
      // For public view, only show published articles
      filter.isPublished = true;
    }
    
    if (isHighlighted !== undefined) {
      filter.isHighlighted = isHighlighted;
    }
    
    // Calculate pagination
    const skip = (page - 1) * limit;
    
    // Execute query with pagination
    const news = await News.find(filter)
      .sort({ publishedAt: -1 }) // Sort by publish date descending
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await News.countDocuments(filter);
    const pages = Math.ceil(total / limit);
    
    // Determine if the user has liked each article
    let userLikedMap: Record<string, boolean> = {};
    
    if (userId || telegramId) {
      let user;
      
      // Find user by userId or telegramId
      if (userId) {
        user = await User.findById(userId);
      }
      
      if (!user && telegramId) {
        user = await User.findOne({ telegramId });
      }
      
      if (user) {
        // Create map of article IDs that the user has liked
        const newsArticles = news.map(article => article._id.toString());
        
        // Check like status for each article directly
        for (const article of news) {
          const articleId = article._id.toString();
          const hasLiked = article.likes.some((like: any) => 
            like.userId.toString() === user._id.toString()
          );
          userLikedMap[articleId] = hasLiked;
        }
      }
    }
    
    // Format the news data with user liked status
    const formattedNews = news.map(article => {
      const articleId = article._id.toString();
      return {
        ...article.toObject(),
        userLiked: userLikedMap[articleId] || false,
        // Don't send the full likes array to the client for privacy/security reasons
        likes: undefined,
        likesCount: article.likes.length, // Ensure accurate count
      };
    });
    
    // Return success response
    return NextResponse.json({
      success: true,
      data: formattedNews,
      pagination: {
        page,
        limit,
        total,
        pages,
      }
    });
    
  } catch (error) {
    console.error('Error fetching news:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch news articles' 
      },
      { status: 500 }
    );
  }
} 