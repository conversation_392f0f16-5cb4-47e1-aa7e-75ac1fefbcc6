
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Zap, Clock, TrendingUp, RefreshCw } from 'lucide-react';

interface ZappingSectionProps {
  scrollY: number;
}

const ZappingSection: React.FC<ZappingSectionProps> = ({ scrollY }) => {
  const steps = [
    { icon: Zap, title: "Deposit SOL once", desc: "Start your zapping journey with any amount" },
    { icon: RefreshCw, title: "AI starts zapping", desc: "Our AI engine trades meme coins automatically" },
    { icon: TrendingUp, title: "<PERSON><PERSON><PERSON> gains every 36hrs", desc: "Collect 1.5% of your invested capital" },
    { icon: Clock, title: "Log in daily", desc: "Maintain streaks and earn Flow Points" },
  ];

  return (
    <section id="zapping" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="mb-4 bg-gray-900/50 backdrop-blur-xl text-green-400 border border-green-500/20 font-medium animate-scale-in">
            ⚡ AI-Powered Zapping
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 font-syne animate-slide-up">
            <span className="text-white">
              What is Zapping?
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto animate-fade-in-left animation-delay-300">
            Once you invest, your funds begin "zapping" — a futuristic AI-powered simulation that trades 
            trending Solana meme coins and delivers real-time SOL yield directly to you.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {steps.map((step, index) => (
            <Card 
              key={index}
              className={`bg-gray-950/50 backdrop-blur-xl border border-green-500/10 p-6 hover:bg-gray-950/70 transition-all duration-500 hover:border-green-500/30 hover:shadow-2xl hover:shadow-green-500/10 group relative overflow-hidden animate-bounce-in`}
              style={{ 
                transform: `translateY(${Math.sin((scrollY + index * 200) * 0.001) * 10}px)`,
                animationDelay: `${index * 0.2 + 0.5}s`
              }}
            >
              <div className="relative z-10">
                <div className="relative mb-4">
                  <step.icon className={`h-12 w-12 text-green-400 group-hover:scale-110 transition-transform duration-300 animate-rotate-in`} style={{ animationDelay: `${index * 0.15 + 0.8}s` }} />
                </div>
                <h3 className={`text-lg font-semibold mb-2 text-white group-hover:text-green-300 transition-colors font-syne animate-slide-up`} style={{ animationDelay: `${index * 0.1 + 1}s` }}>
                  {step.title}
                </h3>
                <p className={`text-gray-400 text-sm animate-fade-in-right`} style={{ animationDelay: `${index * 0.1 + 1.2}s` }}>
                  {step.desc}
                </p>
              </div>
            </Card>
          ))}
        </div>

        <div className="bg-gray-950/30 backdrop-blur-xl rounded-2xl p-8 border border-green-500/10 hover:border-green-500/20 transition-all duration-300 animate-scale-in animation-delay-1400">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div className="group animate-fade-in-left animation-delay-1600">
              <div className="text-3xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform duration-300 font-syne ">36hrs</div>
              <div className="text-gray-300">Reward Cycle</div>
            </div>
            <div className="group animate-fade-in-left animation-delay-1800">
              <div className="text-3xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform duration-300 font-syne ">1.5%</div>
              <div className="text-gray-300">Per Cycle Yield</div>
            </div>
            <div className="group animate-fade-in-left animation-delay-2000">
              <div className="text-3xl font-bold text-green-400 mb-2 group-hover:scale-110 transition-transform duration-300 font-syne ">250%</div>
              <div className="text-gray-300">Maximum Yield</div>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .animation-delay-300 { animation-delay: 0.3s; }
        .animation-delay-1400 { animation-delay: 1.4s; }
        .animation-delay-1600 { animation-delay: 1.6s; }
        .animation-delay-1800 { animation-delay: 1.8s; }
        .animation-delay-2000 { animation-delay: 2s; }
      `}</style>
    </section>
  );
};

export default ZappingSection;
