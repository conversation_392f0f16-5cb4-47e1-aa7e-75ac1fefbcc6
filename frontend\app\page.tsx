import { Suspense } from 'react';
import ConsoleSection from '@/components/console/ConsoleSection';
import { SolanaWalletProvider } from '@/context/SolanaWalletContext';

export default function HomePage() {
  return (
    <main className="min-h-screen  relative z-10">
      {/* Background Effect */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-48 h-48 bg-green-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <Suspense fallback={<HomePageSkeleton />}>
        <SolanaWalletProvider>
          <ConsoleSection />
        </SolanaWalletProvider>
      </Suspense> 
    </main>
  );
}

function HomePageSkeleton() {
  return (
    <div className="max-w-md mx-auto mt-12">
      {/* Background Effect */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-20 -right-20 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-48 h-48 bg-green-500/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>
      
      <div className="mb-6 text-center">
        <h1 className="text-2xl font-bold mb-2 text-white">FLOW TRADE</h1>
        <p className="text-gray-400">Advanced crypto trading bot</p>
      </div>
      
      <div className="bg-black/60 backdrop-blur-md rounded-xl border border-gray-800/50 p-6 mb-6">
        <div className="flex justify-center">
          <div className="w-8 h-8 border-4 border-green-500/30 border-t-green-500 rounded-full animate-spin"></div>
        </div>
      </div>
    </div>
  );
}

