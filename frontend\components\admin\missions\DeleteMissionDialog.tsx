import React, { useState } from 'react'
import { toast } from 'react-hot-toast'
import { IMission } from '@/types/mission'
import { Button } from '@/components/ui/button'

interface DeleteMissionDialogProps {
  isOpen: boolean
  onClose: () => void
  mission: IMission
  onMissionDeleted: () => void
}

const DeleteMissionDialog = ({
  isOpen,
  onClose,
  mission,
  onMissionDeleted
}: DeleteMissionDialogProps) => {
  const [isDeleting, setIsDeleting] = useState(false)
  
  const handleDelete = async () => {
    setIsDeleting(true)
    
    try {
      const response = await fetch(`/api/admin/missions/${mission._id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete mission')
      }
      
      const data = await response.json()
      
      if (data.success) {
        toast.success('Mission deleted successfully')
        onMissionDeleted()
        onClose()
      } else {
        toast.error(data.error || 'Failed to delete mission')
      }
    } catch (error) {
      console.error('Error deleting mission:', error)
      toast.error((error as Error).message || 'Failed to delete mission')
    } finally {
      setIsDeleting(false)
    }
  }
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4">
      <div className="bg-black/80 backdrop-blur-md border border-gray-800 rounded-lg max-w-md w-full p-6">
        <div className="mb-5">
          <h2 className="text-xl font-semibold text-white mb-2">Are you absolutely sure?</h2>
          <p className="text-gray-400">
            This will permanently delete the mission <span className="font-semibold text-white">{mission.title}</span> and
            remove all associated data. This action cannot be undone.
          </p>
        </div>
        
        <div className="flex justify-end gap-3">
          <Button 
            variant="outline"
            onClick={onClose}
            disabled={isDeleting}
            className="bg-transparent border border-gray-700 text-gray-300 hover:bg-gray-800"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isDeleting ? (
              <>
                <svg className="w-4 h-4 mr-2 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default DeleteMissionDialog 