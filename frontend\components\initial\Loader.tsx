'use client'

import { useEffect, useState, useRef } from 'react'
import { motion } from 'framer-motion'

interface LoaderProps {
  onComplete?: () => void
}

const Loader = ({ onComplete }: LoaderProps) => {
  const [progress, setProgress] = useState(0)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  
  // Matrix-like animation setup
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    // Set canvas dimensions
    canvas.width = window.innerWidth
    canvas.height = window.innerHeight
    
    // Characters for the matrix effect
    const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネ'
    const columns = Math.floor(canvas.width / 20) // Character width
    const drops: number[] = []
    
    // Initialize all columns
    for (let i = 0; i < columns; i++) {
      drops[i] = Math.floor(Math.random() * -canvas.height)
    }
    
    // Draw the characters
    const draw = () => {
      // Add semi-transparent black background for trail effect
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      
      // Green text
      ctx.fillStyle = '#0f0'
      ctx.font = '15px'
      
      // Loop over drops
      for (let i = 0; i < drops.length; i++) {
        // Random character
        const text = chars[Math.floor(Math.random() * chars.length)]
        
        // x = i * 20, y = value of drops[i]
        ctx.fillStyle = `rgba(74, 222, 128, ${Math.random() * 0.5 + 0.5})`
        ctx.fillText(text, i * 20, drops[i] * 1)
        
        // Send the drop back to the top randomly after it crosses the screen
        // Adding randomness to the reset to make the drops scattered
        if (drops[i] * 1 > canvas.height && Math.random() > 0.975) {
          drops[i] = 0
        }
        
        // Increment y coordinate
        drops[i]++
      }
    }
    
    const interval = setInterval(draw, 35)
    
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    const duration = 4500 // 4.5 seconds
    const interval = 20 // Update every 20ms
    const steps = duration / interval
    const incrementSize = 100 / steps
    
    let currentProgress = 0
    const timer = setInterval(() => {
      currentProgress += incrementSize
      if (currentProgress >= 100) {
        clearInterval(timer)
        currentProgress = 100
        setTimeout(() => {
          onComplete?.()
        }, 400)
      }
      setProgress(currentProgress)
    }, interval)
    
    return () => clearInterval(timer)
  }, [onComplete])

  // Hexagon points calculation
  const hexPoints = Array.from({ length: 6 }).map((_, i) => {
    const angle = (i * 60 - 30) * (Math.PI / 180)
    return `${Math.cos(angle) * 45},${Math.sin(angle) * 45}`
  }).join(' ')

  return (
    <motion.div 
      className="fixed inset-0 bg-black flex flex-col items-center justify-center z-50 overflow-hidden"
      animate={{ opacity: progress === 100 ? 0 : 1 }}
      transition={{ duration: 0.4 }}
    >
      {/* Matrix-like background */}
      <canvas 
        ref={canvasRef} 
        className="absolute inset-0 opacity-30"
      />
      
      <div className="relative z-10 mb-10 flex flex-col items-center">
        {/* Radial glow effect */}
        <div className="absolute w-60 h-60 rounded-full bg-green-500/10 blur-3xl opacity-50" />
        
        {/* Tech hexagon frame */}
        <svg width="120" height="120" viewBox="-60 -60 120 120" className="relative">
          {/* Outer spinning hexagon */}
          <motion.polygon 
            points={hexPoints} 
            fill="none" 
            stroke="rgba(74, 222, 128, 0.3)" 
            strokeWidth="1"
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          />
          
          {/* Inner hexagon */}
          <motion.polygon 
            points={hexPoints} 
            fill="none" 
            stroke="rgba(74, 222, 128, 0.7)" 
            strokeWidth="1"
            strokeDasharray="120"
            strokeDashoffset="120"
            animate={{ 
              strokeDashoffset: 0,
              rotate: [0, 180]
            }}
            transition={{ 
              strokeDashoffset: { duration: 4.5, ease: "linear" },
              rotate: { duration: 4.5, ease: "easeInOut" }
            }}
          />
          
          {/* Progress circle */}
          <circle cx="0" cy="0" r="30" fill="rgba(0, 0, 0, 0.5)" stroke="rgba(74, 222, 128, 0.3)" strokeWidth="1" />
          
          {/* Progress text */}
          <text 
            x="0" 
            y="0" 
            textAnchor="middle" 
            dominantBaseline="middle" 
            fill="rgb(74, 222, 128)" 
            fontSize="12"
            className="--font-syne"
          >
            {Math.round(progress)}%
          </text>
          
          {/* Circular progress indicator */}
          <motion.circle
            cx="0"
            cy="0"
            r="30"
            fill="none"
            stroke="rgb(74, 222, 128)"
            strokeWidth="1"
            strokeDasharray={`${2 * Math.PI * 30 * progress / 100} ${2 * Math.PI * 30 * (1 - progress / 100)}`}
            strokeDashoffset={(2 * Math.PI * 30) / 4}
          />
        </svg>
        
        {/* Orbiting dots */}
        {[0, 72, 144, 216, 288].map((angle, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 rounded-full bg-green-400"
            style={{ 
              boxShadow: '0 0 8px rgba(74, 222, 128, 0.8)'
            }}
            animate={{
              x: `${Math.cos((angle + progress * 3.6) * Math.PI / 180) * 60}px`,
              y: `${Math.sin((angle + progress * 3.6) * Math.PI / 180) * 60}px`,
              scale: [1, 1.5, 1],
              opacity: [0.7, 1, 0.7]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "mirror"
            }}
          />
        ))}
      </div>
      
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="relative z-10 mt-8 text-center"
      >
        <div className="text-green-400 --font-syne text-sm tracking-wider mb-1">
          INITIALIZING SYSTEM
        </div>
        <div className="text-gray-500 text-xs --font-syne tracking-wider">
          {[
            "LOADING ALGORITHMS",
            "CONNECTING TO EXCHANGES",
            "ANALYZING MARKET DATA",
            "OPTIMIZING STRATEGIES",
            "PREPARING INTERFACE"
          ][Math.floor(progress / 20)]}
        </div>
      </motion.div>
      
      {/* Subtle scan line effect */}
      <motion.div 
        className="absolute inset-0 bg-gradient-to-b from-transparent via-green-500/5 to-transparent z-20 pointer-events-none"
        style={{ height: '200%' }}
        animate={{ y: ['-100%', '100%'] }}
        transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
      />
    </motion.div>
  )
}

export default Loader 