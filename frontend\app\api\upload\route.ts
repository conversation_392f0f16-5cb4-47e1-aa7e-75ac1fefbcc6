import { NextRequest, NextResponse } from 'next/server';
import { uploadImage } from '@/libs/cloudinary';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // Validate image data
    if (!body.image) {
      return NextResponse.json(
        { error: 'Image data is required' },
        { status: 400 }
      );
    }
    
    // Upload image to Cloudinary
    const result = await uploadImage(
      body.image,
      body.folder || 'news_images'
    );
    
    // Return image URL and public ID
    return NextResponse.json({
      url: result.url,
      publicId: result.publicId
    }, { status: 200 });
    
  } catch (error) {
    console.error('Error uploading image:', error);
    
    return NextResponse.json(
      { error: 'Failed to upload image' },
      { status: 500 }
    );
  }
} 