"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./components/wallet/Wallet.tsx":
/*!**************************************!*\
  !*** ./components/wallet/Wallet.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownLeft,ArrowUpRight,BookOpen,Gift,HelpCircle,Loader2,Wallet2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownLeft,ArrowUpRight,BookOpen,Gift,HelpCircle,Loader2,Wallet2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet-minimal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownLeft,ArrowUpRight,BookOpen,Gift,HelpCircle,Loader2,Wallet2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownLeft,ArrowUpRight,BookOpen,Gift,HelpCircle,Loader2,Wallet2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownLeft,ArrowUpRight,BookOpen,Gift,HelpCircle,Loader2,Wallet2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownLeft,ArrowUpRight,BookOpen,Gift,HelpCircle,Loader2,Wallet2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownLeft,ArrowUpRight,BookOpen,Gift,HelpCircle,Loader2,Wallet2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowDownLeft,ArrowUpRight,BookOpen,Gift,HelpCircle,Loader2,Wallet2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/AuthContext */ \"(app-pages-browser)/./context/AuthContext.tsx\");\n/* harmony import */ var _context_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/LanguageContext */ \"(app-pages-browser)/./context/LanguageContext.tsx\");\n/* harmony import */ var _context_SolanaWalletContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/SolanaWalletContext */ \"(app-pages-browser)/./context/SolanaWalletContext.tsx\");\n/* harmony import */ var _WithdrawModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./WithdrawModal */ \"(app-pages-browser)/./components/wallet/WithdrawModal.tsx\");\n/* harmony import */ var _language_LanguageSelector__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../language/LanguageSelector */ \"(app-pages-browser)/./components/language/LanguageSelector.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _WalletConnectButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./WalletConnectButton */ \"(app-pages-browser)/./components/wallet/WalletConnectButton.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_WithdrawModal__WEBPACK_IMPORTED_MODULE_5__]);\n_WithdrawModal__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n // Use our enhanced context\n\n\n\n\n// import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'\n// Animation variants\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.1\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        y: 20,\n        opacity: 0\n    },\n    visible: {\n        y: 0,\n        opacity: 1\n    }\n};\nconst WalletPage = ()=>{\n    _s();\n    const { user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t } = (0,_context_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    // Use enhanced wallet context\n    const { connected, publicKey, isLoading: walletLoading, error: walletError, connectionVerified, isInitialized, clearError, solPrice, bonkPrice, flowPrice, loadingPrice } = (0,_context_SolanaWalletContext__WEBPACK_IMPORTED_MODULE_4__.useWallet)();\n    const [solBalance, setSolBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isInvestment, setIsInvestment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoadingInvestment, setIsLoadingInvestment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [investmentError, setInvestmentError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for the withdraw modal\n    const [isWithdrawModalOpen, setIsWithdrawModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchInvestments = async ()=>{\n        if (!connected || !publicKey) {\n            setIsInvestment(false);\n            setIsLoadingInvestment(false);\n            return;\n        }\n        try {\n            setIsLoadingInvestment(true);\n            setInvestmentError(null);\n            const response = await fetch('/api/investment');\n            const data = await response.json();\n            setIsInvestment(data.success);\n            if (!data.success && data.error) {\n                setInvestmentError(data.error);\n            }\n        } catch (error) {\n            console.error('Error fetching investments:', error);\n            setInvestmentError('Failed to load investment data');\n            setIsInvestment(false);\n        } finally{\n            setIsLoadingInvestment(false);\n        }\n    };\n    // Fetch investments when wallet connection changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            if (connected && connectionVerified && publicKey) {\n                fetchInvestments();\n            } else {\n                setIsInvestment(false);\n                setIsLoadingInvestment(false);\n                setInvestmentError(null);\n            }\n        }\n    }[\"WalletPage.useEffect\"], [\n        connected,\n        connectionVerified,\n        publicKey\n    ]);\n    // Helper functions to safely access nested user properties\n    const getSolBalance = ()=>{\n        var _user_wallet_balance, _user_wallet, _user_wallet_balance1, _user_wallet1, _user_referralStats, _user_referralStats1;\n        if (!(user === null || user === void 0 ? void 0 : (_user_wallet = user.wallet) === null || _user_wallet === void 0 ? void 0 : (_user_wallet_balance = _user_wallet.balance) === null || _user_wallet_balance === void 0 ? void 0 : _user_wallet_balance.sol) && (user === null || user === void 0 ? void 0 : (_user_wallet1 = user.wallet) === null || _user_wallet1 === void 0 ? void 0 : (_user_wallet_balance1 = _user_wallet1.balance) === null || _user_wallet_balance1 === void 0 ? void 0 : _user_wallet_balance1.sol) !== 0 || !(user === null || user === void 0 ? void 0 : (_user_referralStats = user.referralStats) === null || _user_referralStats === void 0 ? void 0 : _user_referralStats.rewardsSol) && (user === null || user === void 0 ? void 0 : (_user_referralStats1 = user.referralStats) === null || _user_referralStats1 === void 0 ? void 0 : _user_referralStats1.rewardsSol) !== 0) return 0;\n        return user.wallet.balance.sol + user.referralStats.rewardsSol;\n    };\n    const getFlowBalance = ()=>{\n        var _user_wallet_balance, _user_wallet, _user_wallet_balance1, _user_wallet1;\n        if (!(user === null || user === void 0 ? void 0 : (_user_wallet = user.wallet) === null || _user_wallet === void 0 ? void 0 : (_user_wallet_balance = _user_wallet.balance) === null || _user_wallet_balance === void 0 ? void 0 : _user_wallet_balance.flow) && (user === null || user === void 0 ? void 0 : (_user_wallet1 = user.wallet) === null || _user_wallet1 === void 0 ? void 0 : (_user_wallet_balance1 = _user_wallet1.balance) === null || _user_wallet_balance1 === void 0 ? void 0 : _user_wallet_balance1.flow) !== 0) return 0;\n        return user.wallet.balance.flow;\n    };\n    const getBonkBalance = ()=>{\n        var _user_wallet_balance, _user_wallet, _user_wallet_balance1, _user_wallet1;\n        if (!(user === null || user === void 0 ? void 0 : (_user_wallet = user.wallet) === null || _user_wallet === void 0 ? void 0 : (_user_wallet_balance = _user_wallet.balance) === null || _user_wallet_balance === void 0 ? void 0 : _user_wallet_balance.bonk) && (user === null || user === void 0 ? void 0 : (_user_wallet1 = user.wallet) === null || _user_wallet1 === void 0 ? void 0 : (_user_wallet_balance1 = _user_wallet1.balance) === null || _user_wallet_balance1 === void 0 ? void 0 : _user_wallet_balance1.bonk) !== 0) return 0;\n        return user.wallet.balance.bonk;\n    };\n    // Calculate total assets in USD\n    const getTotalAssetsUSD = ()=>{\n        const solValue = solBalance * solPrice;\n        const flowValue = getFlowBalance() * flowPrice;\n        const bonkValue = getBonkBalance() * bonkPrice;\n        return solValue + flowValue + bonkValue || 0;\n    };\n    // Get total assets in SOL equivalent\n    const getTotalAssetsSol = ()=>{\n        return getTotalAssetsUSD() / 100;\n    };\n    // Update solBalance when user data changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            setSolBalance(getSolBalance());\n        }\n    }[\"WalletPage.useEffect\"], [\n        user\n    ]);\n    // Prepare assets data for display\n    const assetsData = [\n        {\n            id: 1,\n            name: 'Solana',\n            ticker: 'SOL',\n            amount: getSolBalance(),\n            valueUSD: getSolBalance() * 100,\n            icon: '/icon/solana.png'\n        },\n        {\n            id: 2,\n            name: 'Flow',\n            ticker: 'FLOW',\n            amount: getFlowBalance(),\n            valueUSD: getFlowBalance() * 0.000002,\n            icon: '/logo.jpg'\n        },\n        {\n            id: 3,\n            name: 'Bonk',\n            ticker: 'BONK',\n            amount: getBonkBalance(),\n            valueUSD: getBonkBalance() * 0.00001,\n            icon: '/icon/bonk.png'\n        }\n    ];\n    // Open withdraw modal\n    const openWithdrawModal = ()=>{\n        setIsWithdrawModalOpen(true);\n    };\n    // Close withdraw modal\n    const closeWithdrawModal = ()=>{\n        setIsWithdrawModalOpen(false);\n    };\n    // Show loading state during wallet initialization\n    if (!isInitialized) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative min-h-screen p-4 md:p-6 overflow-hidden pt-4 pb-24 px-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute -top-20 -left-20 w-60 h-60 bg-green-500 rounded-full filter blur-[100px] opacity-20 z-0\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-40 -right-20 w-80 h-80 bg-purple-500 rounded-full filter blur-[100px] opacity-10 z-0\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative z-10 max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center min-h-[50vh]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-8 h-8 animate-spin text-green-500\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"Initializing wallet...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n            lineNumber: 163,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative min-h-screen p-4 md:p-6 overflow-hidden pt-4 pb-24 px-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-20 -left-20 w-60 h-60 bg-green-500 rounded-full filter blur-[100px] opacity-20 z-0\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-40 -right-20 w-80 h-80 bg-purple-500 rounded-full filter blur-[100px] opacity-10 z-0\"\n            }, void 0, false, {\n                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"mr-3 text-green-500\",\n                                        size: 28\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl md:text-3xl font-bold text-white\",\n                                        children: t('wallet.title')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3 items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_language_LanguageSelector__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"dropdown\",\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    walletError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-xl flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-5 h-5 text-red-500 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-400\",\n                                        children: walletError\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearError,\n                                className: \"text-red-400 hover:text-red-300 transition-colors\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"bg-black/60 backdrop-blur-lg border border-gray-800 rounded-2xl p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-2\",\n                                children: t('wallet.totalAssets')\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold text-white\",\n                                        children: [\n                                            \"$\",\n                                            getTotalAssetsUSD().toLocaleString(undefined, {\n                                                maximumFractionDigits: 2\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 ml-3 pb-1\",\n                                        children: [\n                                            \"≈ \",\n                                            getTotalAssetsSol().toFixed(2),\n                                            \" SOL\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WalletConnectButton__WEBPACK_IMPORTED_MODULE_8__.WalletConnectButton, {}, void 0, false, {\n                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex mt-2 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                        href: \"/\",\n                                        whileHover: {\n                                            scale: connected ? 1.02 : 1\n                                        },\n                                        whileTap: {\n                                            scale: connected ? 0.98 : 1\n                                        },\n                                        className: \"flex-1 font-medium py-3 px-4 rounded-xl flex items-center justify-center transition-all \".concat(connected && connectionVerified ? 'bg-green-500 hover:bg-green-600 text-black cursor-pointer' : ''),\n                                        style: {\n                                            pointerEvents: connected && connectionVerified ? 'auto' : 'none'\n                                        },\n                                        children: isLoadingInvestment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Loading...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                isInvestment ? 'Fuel Up' : t('wallet.invest'),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                        whileHover: {\n                                            scale: connected && connectionVerified ? 1.02 : 1\n                                        },\n                                        whileTap: {\n                                            scale: connected && connectionVerified ? 0.98 : 1\n                                        },\n                                        onClick: openWithdrawModal,\n                                        disabled: !connected || !connectionVerified || walletLoading,\n                                        className: \"flex-1 font-medium py-3 px-4 rounded-xl flex items-center justify-center transition-all \".concat(connected && connectionVerified && !walletLoading ? 'bg-gray-800 hover:bg-gray-700 text-white cursor-pointer' : 'bg-gray-600 text-gray-400 cursor-not-allowed'),\n                                        children: walletLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Loading...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                t('wallet.withdraw'),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"ml-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-white mb-4\",\n                                children: t('wallet.assets')\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                variants: containerVariants,\n                                initial: \"hidden\",\n                                animate: \"visible\",\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: assetsData.filter((asset)=>asset.amount > 0 || asset.id <= 2).map((asset)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        variants: itemVariants,\n                                        whileHover: {\n                                            scale: 1.02,\n                                            backgroundColor: 'rgba(26, 27, 30, 0.8)'\n                                        },\n                                        className: \"bg-gray-900/40 backdrop-blur-sm rounded-xl p-4 transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                src: asset.icon,\n                                                                alt: asset.name,\n                                                                className: \"\".concat(asset.ticker === 'BONK' ? 'w-10 h-10' : asset.ticker === 'FLOW' ? 'w-7 h-7' : 'w-6 h-6'),\n                                                                width: asset.ticker === 'BONK' ? 42 : asset.ticker === 'FLOW' ? 28 : 24,\n                                                                height: asset.ticker === 'BONK' ? 42 : asset.ticker === 'FLOW' ? 28 : 24\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: asset.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: [\n                                                                        asset.ticker === 'BONK' ? asset.amount.toLocaleString(undefined, {\n                                                                            maximumFractionDigits: 0\n                                                                        }) : asset.amount.toLocaleString(undefined, {\n                                                                            maximumFractionDigits: 4\n                                                                        }),\n                                                                        \" \",\n                                                                        asset.ticker\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: [\n                                                            \"$\",\n                                                            asset.valueUSD.toLocaleString(undefined, {\n                                                                maximumFractionDigits: 2\n                                                            })\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, asset.id, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                href: \"/tutorial\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"flex items-center p-4 rounded-xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"text-green-500 mr-3\",\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: t('wallet.tutorial')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                href: \"#airdrop\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"flex items-center p-4 rounded-xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"text-green-500 mr-3\",\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: t('wallet.airdropInfo')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                href: \"#faq\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"flex items-center p-4 rounded-xl bg-gray-900/30 backdrop-blur-sm border border-gray-800 hover:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowDownLeft_ArrowUpRight_BookOpen_Gift_HelpCircle_Loader2_Wallet2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"text-green-500 mr-3\",\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300\",\n                                        children: t('wallet.faqs')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WithdrawModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                isOpen: isWithdrawModalOpen,\n                onClose: closeWithdrawModal\n            }, void 0, false, {\n                fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Development\\\\May Session 2025\\\\flow-trade-meme\\\\frontend\\\\components\\\\wallet\\\\Wallet.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n_s(WalletPage, \"OSqYqfDaONUYOmyBylqmhGqkxl4=\", false, function() {\n    return [\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _context_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage,\n        _context_SolanaWalletContext__WEBPACK_IMPORTED_MODULE_4__.useWallet\n    ];\n});\n_c = WalletPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WalletPage);\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/wallet/Wallet.tsx\n"));

/***/ })

});