"use client"

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { toast } from 'react-hot-toast'
import { 
  ExternalLink, 
  Loader2, 
  ChevronLeft, 
  ChevronRight, 
  Search,
  RotateCcw
} from 'lucide-react'

type WithdrawalStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
type WithdrawalCurrency = 'sol' | 'flow' | 'bonk';

interface Withdrawal {
  _id: string;
  user: {
    _id: string;
    displayName: string;
    telegramUsername?: string;
    telegramId?: string;
  };
  wallet: string;
  amount: number;
  currency: WithdrawalCurrency;
  status: WithdrawalStatus;
  txHash?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  cancelledAt?: string;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export default function WithdrawalsPage() {
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  })
  const [statusFilter, setStatusFilter] = useState<string | null>(null)
  const [currencyFilter, setCurrencyFilter] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: '',
  })

  // Fetch withdrawals
  const fetchWithdrawals = async () => {
    try {
      setIsLoading(true)
      const queryParams = new URLSearchParams({
        page: String(pagination.page),
        limit: String(pagination.limit),
      })

      if (statusFilter) {
        queryParams.append('status', statusFilter)
      }

      if (currencyFilter) {
        queryParams.append('currency', currencyFilter)
      }

      if (searchQuery) {
        queryParams.append('query', searchQuery)
      }

      if (dateRange.startDate) {
        queryParams.append('startDate', dateRange.startDate)
      }

      if (dateRange.endDate) {
        queryParams.append('endDate', dateRange.endDate)
      }

      const response = await fetch(`/api/withdrawal/getAll?${queryParams.toString()}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch withdrawals')
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch withdrawals')
      }

      setWithdrawals(data.data || [])
      setPagination(data.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0,
      })
    } catch (error: any) {
      console.error('Error fetching withdrawals:', error)
      toast.error(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  // Load withdrawals on mount and when filters change
  useEffect(() => {
    fetchWithdrawals()
  }, [pagination.page, statusFilter, currencyFilter])

  // Format date function
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Format wallet address
  const formatWalletAddress = (address: string) => {
    if (!address) return 'N/A'
    if (address.length < 20) return address
    return `${address.substring(0, 8)}...${address.substring(address.length - 8)}`
  }

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
    fetchWithdrawals()
  }

  // Get status color class
  const getStatusColorClass = (status: WithdrawalStatus) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'processing':
        return 'bg-blue-100 text-blue-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Withdrawals Management</h2>
          <p className="text-muted-foreground">
            View user withdrawal requests
          </p>
        </div>
      </div>
      
      <div className="my-6 h-[1px] bg-border" />
      
      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div className="flex-1 max-w-md">
          <form onSubmit={handleSearch} className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search by wallet or user..."
              className="w-full rounded-md border border-input bg-background pl-8 pr-10 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Button 
              type="submit" 
              variant="ghost" 
              size="sm" 
              className="absolute right-0 top-0 h-full px-3"
            >
              Search
            </Button>
          </form>
        </div>
        
        <select
          value={statusFilter || ''}
          onChange={(e) => {
            setStatusFilter(e.target.value || null)
            setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
          }}
          className="rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
        >
          <option value="">All Statuses</option>
          <option value="pending">Pending</option>
          <option value="processing">Processing</option>
          <option value="completed">Completed</option>
          <option value="failed">Failed</option>
          <option value="cancelled">Cancelled</option>
        </select>
        
        <select
          value={currencyFilter || ''}
          onChange={(e) => {
            setCurrencyFilter(e.target.value || null)
            setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
          }}
          className="rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
        >
          <option value="">All Currencies</option>
          <option value="sol">SOL</option>
          <option value="flow">FLOW</option>
          <option value="bonk">BONK</option>
        </select>
        
        <Button 
          variant="outline" 
          size="icon"
          onClick={() => {
            setSearchQuery('')
            setStatusFilter(null)
            setCurrencyFilter(null)
            setDateRange({ startDate: '', endDate: '' })
            setPagination(prev => ({ ...prev, page: 1 }))
            fetchWithdrawals()
          }}
          title="Reset filters"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Withdrawals Table */}
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : withdrawals.length > 0 ? (
        <div className="rounded-md border">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/50">
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">User</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Wallet</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Amount</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Date</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {withdrawals.map((withdrawal) => (
                  <tr key={withdrawal._id} className="hover:bg-muted/50">
                    <td className="px-4 py-3 text-sm">
                      <div>
                        <div className="font-medium">
                          {withdrawal.user?.displayName || 'Unknown User'}
                        </div>
                        {withdrawal.user?.telegramUsername && (
                          <div className="text-xs text-muted-foreground">
                            @{withdrawal.user.telegramUsername}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <a 
                        href={`https://solscan.io/address/${withdrawal.wallet}`}
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className="flex items-center text-blue-600 hover:underline"
                      >
                        <span className="truncate max-w-[150px]">{formatWalletAddress(withdrawal.wallet)}</span>
                        <ExternalLink className="h-3 w-3 ml-1 flex-shrink-0" />
                      </a>
                    </td>
                    <td className="px-4 py-3 text-sm font-medium">
                      {withdrawal.amount} {withdrawal.currency.toUpperCase()}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {formatDate(withdrawal.createdAt)}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColorClass(withdrawal.status)}`}>
                        {withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex items-center justify-center space-x-2 py-4">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">
                Page {pagination.page} of {pagination.pages}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page === pagination.pages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <p className="text-muted-foreground mb-4">No withdrawals found</p>
          <Button 
            variant="outline" 
            onClick={() => {
              setSearchQuery('')
              setStatusFilter(null)
              setCurrencyFilter(null)
              setDateRange({ startDate: '', endDate: '' })
              fetchWithdrawals()
            }}
          >
            Reset Filters
          </Button>
        </div>
      )}
    </div>
  )
}
