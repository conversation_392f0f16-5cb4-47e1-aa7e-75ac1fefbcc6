import mongoose, { Schema } from 'mongoose';
import slugify from 'slugify';


// News category types
export enum NewsCategory {
  MARKET = 'market',
  TECHNOLOGY = 'technology',
  DEFI = 'defi',
  TRADING = 'trading',
  GENERAL = 'general',
}

// User interaction interface for tracking likes and views
export interface IUserInteraction {
  userId: Schema.Types.ObjectId;
  timestamp: Date;
}

// News article interface
export interface INews extends Document {
  title: string;
  slug: string;
  summary: string;
  content: string;
  image: string;
  category: NewsCategory;
  authorName: string;
  likes: IUserInteraction[];
  views: IUserInteraction[];
  likesCount: number;
  viewsCount: number;
  isPublished: boolean;
  isHighlighted: boolean;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}


// News schema
const NewsSchema = new Schema<INews>(
  {
    title: { type: String, required: true, trim: true },
    slug: { type: String, unique: true, index: true },
    summary: { type: String, required: true },
    content: { type: String, required: true },
    image: { type: String, required: true },
    category: {
      type: String,
      enum: Object.values(NewsCategory),
      default: NewsCategory.GENERAL,
      index: true
    },
    authorName: { type: String, required: true },
    likes: [{
      userId: { type: Schema.Types.ObjectId, required: true, ref: 'User' },
      timestamp: { type: Date, default: Date.now }
    }],
    views: [{
      userId: { type: Schema.Types.ObjectId, required: true, ref: 'User' },
      timestamp: { type: Date, default: Date.now }
    }],
    likesCount: { type: Number, default: 0 },
    viewsCount: { type: Number, default: 0 },
    isPublished: { type: Boolean, default: true, index: true },
    isHighlighted: { type: Boolean, default: false, index: true },
    publishedAt: { type: Date, default: Date.now }
  },
  {
    timestamps: true,
    versionKey: false
  }
);

// Create slug from title before saving
NewsSchema.pre('save', function(next) {
  if (this.isModified('title')) {
    this.slug = slugify(this.title, { 
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g
    });
  }
  next();
});

// Indexes for faster queries
NewsSchema.index({ "category": 1, "publishedAt": -1 });
NewsSchema.index({ "publishedAt": -1 });
NewsSchema.index({ "isHighlighted": 1, "publishedAt": -1 });
NewsSchema.index({ "likes.userId": 1 });
NewsSchema.index({ "views.userId": 1 });

// Helper method to check if a user has liked this article
NewsSchema.methods.isLikedByUser = function(userId: string): boolean {
  return this.likes.some((like: any) => 
    like.userId.toString() === userId.toString()
  );
};

// Helper method to check if a user has viewed this article
NewsSchema.methods.isViewedByUser = function(userId: string): boolean {
  return this.views.some((view: any) => 
    view.userId.toString() === userId.toString()
  );
};

// Helper method to add a like
NewsSchema.methods.addLike = function(userId: string) {
  if (!this.isLikedByUser(userId)) {
    this.likes.push({
      userId,
      timestamp: new Date()
    });
    this.likesCount += 1;
  }
  return this;
};

// Helper method to remove a like
NewsSchema.methods.removeLike = function(userId: string) {
  const initialLength = this.likes.length;
  this.likes = this.likes.filter((like: any) => 
    like.userId.toString() !== userId.toString()
  );
  
  if (initialLength !== this.likes.length) {
    this.likesCount -= 1;
  }
  return this;
};

// Helper method to add a view
NewsSchema.methods.addView = function(userId: string) {
  if (!this.isViewedByUser(userId)) {
    this.views.push({
      userId,
      timestamp: new Date()
    });
    this.viewsCount += 1;
  }
  return this;
};

// Create model if it doesn't exist
export const News = mongoose.models.News || 
  mongoose.model<INews>('News', NewsSchema);

export default News;
