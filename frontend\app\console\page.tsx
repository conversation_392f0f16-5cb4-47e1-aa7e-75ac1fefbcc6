'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import MemeOracle from '@/components/console/MemeOracle';

export default function ConsolePage() {
  const [isInvested, setIsInvested] = useState(false);
  const [investmentAmount, setInvestmentAmount] = useState(0);
  const [currentROI, setCurrentROI] = useState(0);
  
  // Demo functions
  const handleInvest = () => {
    setIsInvested(true);
    setInvestmentAmount(1000);
    // Simulate ROI increasing over time
    let roi = 0;
    const interval = setInterval(() => {
      roi += 0.5;
      setCurrentROI(roi);
      if (roi >= 100) {
        clearInterval(interval);
      }
    }, 2000);
  };
  
  return (
    <div className="min-h-screen bg-gray-950 text-white p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6 text-green-500">Investment Console</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Investment Module */}
          <div className="md:col-span-2 bg-gray-900 rounded-lg p-6 border border-green-900/50">
            <h2 className="text-xl font-bold mb-4 text-green-500">Investment Status</h2>
            
            {!isInvested ? (
              <div className="text-center py-12">
                <p className="mb-6 text-gray-400">No active investments</p>
                <button 
                  onClick={handleInvest}
                  className="bg-green-600 hover:bg-green-700 px-6 py-2 rounded-md font-medium"
                >
                  Start Investing
                </button>
              </div>
            ) : (
              <div>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-gray-800 p-4 rounded-md">
                    <div className="text-sm text-gray-400">Investment Amount</div>
                    <div className="text-xl font-medium">${investmentAmount.toLocaleString()}</div>
                  </div>
                  <div className="bg-gray-800 p-4 rounded-md">
                    <div className="text-sm text-gray-400">Current ROI</div>
                    <div className="text-xl font-medium text-green-500">{currentROI.toFixed(1)}%</div>
                  </div>
                </div>
                
                {/* Investment Controls */}
                <div className="flex space-x-3">
                  <button className="bg-green-600 hover:bg-green-700 px-4 py-1.5 rounded-md text-sm">
                    Claim Returns
                  </button>
                  <button className="bg-red-600 hover:bg-red-700 px-4 py-1.5 rounded-md text-sm">
                    Withdraw
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* Meme Oracle Section */}
          <div className="md:col-span-1">
            <MemeOracle />
          </div>
        </div>
      </div>
    </div>
  );
} 