import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { News } from '@/libs/model/news.schema';
import slugify from 'slugify';

// Local NewsCategory enum to match the one in CreateNews.tsx
enum NewsCategory {
  MARKET = 'market',
  TECHNOLOGY = 'technology',
  DEFI = 'defi',
  TRADING = 'trading',
  GENERAL = 'general',
}


export async function GET(
  request: NextRequest,
  { params }: any 
) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    const { id } = params;
    
    // Validate ID ~~~
    if (!id) {
      return NextResponse.json(
        { error: 'News ID is required' },
        { status: 400 }
      );
    }
    
    // Find news article
    const article = await News.findById(id);
    
    if (!article) {
      return NextResponse.json(
        { error: 'News article not found' },
        { status: 404 }
      );
    }
    
    // Return response
    return NextResponse.json({
      success: true,
      data: article
    });
    
  } catch (error) {
    console.error('Error fetching news article:', error);
    
    return NextResponse.json(
      { error: 'Failed to fetch news article' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: any
) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    const { id } = params;
    
    // Validate ID
    if (!id) {
      return NextResponse.json(
        { error: 'News ID is required' },
        { status: 400 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    const { title, summary, content, category, authorName, image } = body;
    
    if (!title || !summary || !content || !category || !authorName || !image) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Validate category
    if (!Object.values(NewsCategory).includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      );
    }
    
    // Find news article
    const article = await News.findById(id);
    
    if (!article) {
      return NextResponse.json(
        { error: 'News article not found' },
        { status: 404 }
      );
    }
    
    // Check if title has changed, if yes, regenerate slug
    let slug = article.slug;
    
    if (title !== article.title) {
      const baseSlug = slugify(title, {
        lower: true,
        strict: true,
        remove: /[*+~.()'"!:@]/g,
      });
      
      // Check if slug already exists (excluding current article)
      let newSlug = baseSlug;
      let slugExists = await News.findOne({ slug: newSlug, _id: { $ne: id } });
      let counter = 1;
      
      // If slug exists, append a number until we find a unique slug
      while (slugExists) {
        newSlug = `${baseSlug}-${counter}`;
        slugExists = await News.findOne({ slug: newSlug, _id: { $ne: id } });
        counter++;
      }
      
      slug = newSlug;
    }
    
    // Update news article
    const isPublishStateChanged = 
      article.isPublished !== body.isPublished && body.isPublished === true;
    
    const updateData = {
      title,
      slug,
      summary,
      content,
      category,
      authorName,
      image,
      isPublished: Boolean(body.isPublished),
      isHighlighted: Boolean(body.isHighlighted),
      ...(isPublishStateChanged && { publishedAt: new Date() }),
    };
    
    const updatedArticle = await News.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    );
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'News article updated successfully',
      data: {
        id: updatedArticle._id,
        title: updatedArticle.title,
        slug: updatedArticle.slug,
      }
    });
    
  } catch (error) {
    console.error('Error updating news article:', error);
    
    return NextResponse.json(
      { error: 'Failed to update news article' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: any
) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    const { id } = params;
    
    // Validate ID
    if (!id) {
      return NextResponse.json(
        { error: 'News ID is required' },
        { status: 400 }
      );
    }
    
    // Find news article
    const article = await News.findById(id);
    
    if (!article) {
      return NextResponse.json(
        { error: 'News article not found' },
        { status: 404 }
      );
    }
    
    // Delete news article
    await News.findByIdAndDelete(id);
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'News article deleted successfully'
    });
    
  } catch (error) {
    console.error('Error deleting news article:', error);
    
    return NextResponse.json(
      { error: 'Failed to delete news article' },
      { status: 500 }
    );
  }
} 