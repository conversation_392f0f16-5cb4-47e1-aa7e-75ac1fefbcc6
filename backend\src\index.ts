import dotenv from "dotenv";
dotenv.config();
import bot from "./utils/bot";
import express from "express";
import { connectToDatabase } from "./utils/database";
const app = express();

// Connect to database
connectToDatabase();

const PORT = process.env.PORT || 3000;
app.use(express.json());
app.use(bot.webhookCallback("/webhook"));

app.listen(PORT, async () => {
  console.log(`Server is running on port ${PORT}`);

  // Launch bot
  await bot
    .launch()
    .then(() => {
      console.log("FlowTrade bot is running...");
    })
    .catch((error) => {
      console.error("Failed to start bot:", error);
      process.exit(1);
    });
});

// Basic error handling
process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
});

process.on("unhandledRejection", (error) => {
  console.error("Unhandled Rejection:", error);
});

// Enable graceful stop
process.once("SIGINT", () => bot.stop("SIGINT"));
process.once("SIGTERM", () => bot.stop("SIGTERM"));
