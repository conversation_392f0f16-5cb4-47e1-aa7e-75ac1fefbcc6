
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Zap, Users, MessageCircle, ExternalLink } from 'lucide-react';

interface CTASectionProps {
  scrollY: number;
}

const CTASection: React.FC<CTASectionProps> = ({ scrollY }) => {
  const actions = [
    {
      icon: Zap,
      title: "Start Zapping",
      desc: "Activate your zap journey now",
      button: "Start Bot",
      gradient: "from-green-500 to-emerald-600",
      primary: true,
      link: "https://t.me/FlowTrade_bot"
    },
    {
      icon: Users,
      title: "Share & Earn",
      desc: "Get your referral link",
      button: "Get Referral Link",
      gradient: "from-emerald-500 to-teal-600",
      link: "https://t.me/FlowTrade_bot?start="
    },
    {
      icon: MessageCircle,
      title: "Join Community",
      desc: "Connect with other zappers",
      button: "Telegram Channel",
      gradient: "from-teal-500 to-cyan-600",
      link: "https://t.me/FlowTrade_bot"
    },
    {
      icon: ExternalLink,
      title: "Learn More",
      desc: "Dive deeper into FlowTrade",
      button: "Documentation",
      gradient: "from-cyan-500 to-blue-600",
      link: "https://t.me/FlowTrade_bot"  
    }
  ];

  return (
    <section className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold mb-6 font-syne animate-slide-down">
            <span className="text-white">
              Ready to Start Zapping?
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto animate-scale-in animation-delay-300">
            The future is automated. The future is Flow. 
            <span className="block mt-2 text-green-400 font-semibold ">
              Join thousands already earning passive SOL rewards.
            </span>
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {actions.map((action, index) => (
            <Card 
              key={index}
              className={`bg-gray-950/50 backdrop-blur-xl border border-green-500/10 p-6 hover:bg-gray-950/70 transition-all duration-500 hover:border-green-500/20 hover:shadow-2xl hover:shadow-green-500/10 group text-center animate-bounce-in ${
                action.primary ? 'ring-1 ring-green-500/20' : ''
              }`}
              style={{ 
                transform: `translateY(${Math.sin((scrollY + index * 200) * 0.003) * 15}px) scale(${action.primary ? 1.02 : 1})`,
                animationDelay: `${index * 0.1 + 0.5}s`
              }}
            >
              <div className="relative mb-6">
                <action.icon className={`h-12 w-12 text-green-400 mx-auto group-hover:scale-125 transition-all duration-500 group-hover:rotate-6 animate-rotate-in`} style={{ animationDelay: `${index * 0.1 + 0.8}s` }} />
              </div>
              
              <h3 className={`text-lg font-bold text-white mb-2 group-hover:text-green-300 transition-colors font-syne animate-slide-up`} style={{ animationDelay: `${index * 0.1 + 1}s` }}>
                {action.title}
              </h3>
              <p className={`text-gray-400 text-sm mb-6 animate-fade-in-right`} style={{ animationDelay: `${index * 0.1 + 1.2}s` }}>
                {action.desc}
              </p>
              
              <Button 
                className={`w-full bg-gradient-to-r ${action.gradient} hover:scale-105 transition-all duration-300 text-black font-semibold border-0 shadow-lg group-hover:shadow-xl animate-gradient-x animate-scale-in`}
                size="sm"
                style={{ animationDelay: `${index * 0.1 + 1.4}s` }}
                onClick={() => window.open(action.link, '_blank')}
                >
                {action.button}
              </Button>
            </Card>
          ))}
        </div>

        {/* Final CTA */}
        <div className="text-center animate-bounce-in animation-delay-2000">
          <Card className="bg-gray-950/30 backdrop-blur-xl p-12 border border-green-500/10 inline-block max-w-4xl hover:border-green-500/20 transition-all duration-300">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <Zap className="h-16 w-16 text-green-400 " />
              <div className="text-left">
                <div className="text-2xl font-bold text-white font-syne">Maximum Yield: 250%</div>
                <div className="text-gray-300 animate-fade-in-left animation-delay-2200">Then reinvest to restart your zap cycle</div>
              </div>
            </div>
            
            <Button 
              size="lg" 
              onClick={() => window.open("https://t.me/FlowTrade_bot", '_blank')}
              className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-400 hover:to-emerald-500 text-black font-semibold border-0 shadow-2xl shadow-green-500/30 hover:shadow-green-500/50 px-12 py-6 text-xl group transition-all duration-300 hover:scale-105 animate-gradient-x animate-scale-in animation-delay-2400"
            >
              <Zap className="mr-3 h-6 w-6 group-hover:rotate-12 transition-transform duration-300 animate-rotate-in animation-delay-2600" />
              Activate Zapping Now
              <Zap className="ml-3 h-6 w-6 group-hover:rotate-12 transition-transform duration-300 animate-rotate-in animation-delay-2800" />
            </Button>
          </Card>
        </div>
      </div>

      <style>{`
        .animation-delay-300 { animation-delay: 0.3s; }
        .animation-delay-2000 { animation-delay: 2s; }
        .animation-delay-2200 { animation-delay: 2.2s; }
        .animation-delay-2400 { animation-delay: 2.4s; }
        .animation-delay-2600 { animation-delay: 2.6s; }
        .animation-delay-2800 { animation-delay: 2.8s; }
      `}</style>
    </section>
  );
};

export default CTASection;
