{"app": {"name": "Flow Trade", "description": "Продвинутый торговый бот с алгоритмическими стратегиями"}, "language": {"saved": "Сохранено"}, "nav": {"home": "Главная", "wallet": "Кошелек", "trading": "Торговля", "referrals": "Рефералы", "profile": "Профиль", "missions": "Миссии", "news": "Новости"}, "wallet": {"title": "Кошелек", "terms": "Условия", "totalAssets": "Общие активы", "connectWallet": "Подключить кошелек", "walletConnected": "Кошелек подключен", "invest": "Инвестировать", "withdraw": "Вывести", "assets": "Активы", "tutorial": "Руководство", "airdropInfo": "Информация о Airdrop", "faqs": "Часто задаваемые вопросы", "connecting": "Подключение..."}, "withdraw": {"title": "Вывод средств", "history": "История выводов", "backToWithdrawal": "Назад к выводу", "walletAddress": "Адрес кошелька", "enterWalletAddress": "Введите адрес вашего кошелька", "currency": "Валюта", "availableBalance": "Доступный баланс", "amountToWithdraw": "Сумма для вывода", "max": "МАКС", "withdrawButton": "Вывести", "processing": "Обработка...", "minimumWithdrawal": "Минимальная сумма вывода", "viewHistory": "Просмотр истории", "noHistoryFound": "История выводов не найдена", "needWalletConnection": "Вам необходимо подключить кошелек, прежде чем вы сможете выводить средства.", "successful": "Вывод успешно выполнен", "requestSubmitted": "Ваш запрос на вывод средств успешно отправлен."}, "refer": {"title": "Рекомендуй и зарабатывай", "creatorProgram": "Программа создателей", "referrals": "РЕФЕРАЛЫ", "solEarned": "ЗАРАБОТАНО SOL", "flowEarned": "ЗАРАБОТАНО FLOW", "bonkEarned": "ЗАРАБОТАНО BONK", "forInvitations": "За приглашения", "yourReferralLink": "Ваша реферальная ссылка", "shareViaTelegram": "Поделиться через Telegram", "shareTitle": "Присоединяйся ко мне в FLOW TRADE!", "shareText": "Используй мою реферальную ссылку для регистрации:", "referralsTab": "Рефералы", "tasksTab": "Задания", "yourReferrals": "Ваши рефералы", "noReferralsYet": "Вы еще никого не пригласили", "shareToEarn": "Поделитесь своей реферальной ссылкой, чтобы начать зарабатывать!", "noEarningsYet": "Пока нет заработка", "active": "Акти<PERSON><PERSON>н", "inactive": "Неактивен"}, "referTasks": {"referralRewards": "Реферальные награды", "inviteFriends": "Пригласите друзей, чтобы получить токены BONK", "refresh": "Обновить", "dataRefreshed": "Данные о рефералах обновлены", "refreshFailed": "Не удалось обновить данные", "loginRequired": "Вы должны войти в систему, чтобы получить награды", "claimFailed": "Не удалось получить награду", "claimSuccess": "{amount} {currency} успешно получено!", "errorClaimingReward": "Ошибка при получении награды. Пожалуйста, попробуйте снова.", "referralCount": "{current}/{required} Рефе<PERSON><PERSON><PERSON>ов", "claimed": "Получено", "claimReward": "Получить награду", "needMoreReferrals": "Нужно больше рефералов", "noTasksAvailable": "Нет доступных реферальных заданий", "checkBackLater": "Проверьте позже для новых возможностей получения наград"}, "referralTasks": {"rt-001": {"title": "Пригласите 5 друзей", "description": "Пригласите 5 новых пользователей, чтобы получить 10,000 BONK"}, "rt-002": {"title": "Пригласите 10 друзей", "description": "Пригласите 10 новых пользователей, чтобы получить 20,000 BONK"}, "rt-003": {"title": "Пригласите 25 друзей", "description": "Пригласите 25 новых пользователей, чтобы получить 50,000 BONK"}, "rt-004": {"title": "Пригласите 50 друзей", "description": "Пригласите 50 новых пользователей, чтобы получить 100,000 BONK"}, "rt-005": {"title": "Пригласите 100 друзей", "description": "Пригласите 100 новых пользователей, чтобы получить 200,000 BONK"}, "rt-006": {"title": "Пригласите 250 друзей", "description": "Пригласите 250 новых пользователей, чтобы получить 500,000 BONK"}, "rt-007": {"title": "Пригласите 500 друзей", "description": "Пригласите 500 новых пользователей, чтобы получить 1,000,000 BONK"}, "rt-008": {"title": "Пригласите 1000 друзей", "description": "Пригласите 1000 новых пользователей, чтобы получить 2,000,000 BONK"}}, "content": {"creators": "Создатели", "backToHome": "Вернуться на главную", "contentCreator": "Контент-создатель", "rewardsProgram": "Программа вознаграждений", "about": "О программе", "aboutDescription": "Создавайте и делитесь контентом, связанным с Flow Trade: видео, короткометражками, мемами, анимацией, графиками и руководствами. Ваше творчество может проявиться в любом формате.", "earnRewards": "Зарабатывайте вознаграждение за качественный контент, продвигающий Flow Trade", "submitYourContent": "Отправить ваш контент", "linkLabel": "Ссылка на ваш контент (Twitter, YouTube, Instagram и т.д.):", "linkPlaceholder": "https://twitter.com/yourusername/status/123456789", "pendingSubmissionWarning": "У вас уже есть заявка на рассмотрении. Пожалуйста, дождитесь проверки перед повторной отправкой.", "submitting": "Отправка...", "submitContent": "Отправить контент", "yourSubmissions": "Ваши заявки", "submittedOn": "Отправлено", "reward": "Награда", "note": "Примечание", "noSubmissionsYet": "Вы еще не отправляли контент.", "guidelines": "Рекомендации", "guideline1": {"title": "Фокус на контенте", "description": "Убедитесь, что Flow Trade упоминается или фигурирует в вашем творении."}, "guideline2": {"title": "Оригинальность", "description": "Контент должен быть оригинальным и созданным вами. Плагиат приведет к отклонению."}, "guideline3": {"title": "Качество", "description": "Высококачественный контент с четким аудио/видео, правильной грамматикой и профессиональной презентацией."}, "guideline4": {"title": "Точность", "description": "Информация должна быть точной и не вводящей в заблуждение. Никаких ложных обещаний или утверждений."}, "status": {"pending": "на рассмотрении", "approved": "одобрено", "rejected": "отклонено"}, "fetchFailed": "Не удалось загрузить заявки", "validLinkRequired": "Пожалуйста, введите действительную ссылку на контент", "submitFailed": "Не удалось отправить контент", "submitSuccess": "Ваш контент успешно отправлен!", "connectWalletWarning": "Пожалуйста, подключите вашу кошелек перед отправкой контента.", "walletConnected": "Кошелек подключен", "walletAddressInfo": "Отправки будут связаны с адресом вашего кошелька", "walletRequired": "Пожалуйста, подключите ваш кошелек для отправки контента"}, "news": {"articles": "Статьи", "hotTopics": "Горячие темы", "loadingArticles": "Загрузка последних статей...", "errorLoading": "Не удалось загрузить новостные статьи. Пожалуйста, попробуйте снова.", "tryAgain": "Попробовать снова", "noArticlesFound": "Статьи не найдены", "loading": "Загрузка...", "loadMore": "Загрузить еще", "fetchFailed": "Не удалось загрузить новостные статьи", "backToArticles": "Назад к статьям", "unknownDate": "Неизвестная дата", "openInTelegramToInteract": "Пожалуйста, откройте в Telegram для взаимодействия", "waitForAuthentication": "Пожалуйста, подождите, пока мы вас аутентифицируем", "failedToUpdateLikeStatus": "Не удалось обновить статус лайка", "alreadyLiked": "Вы уже поставили лайк этой статье", "linkCopied": "Ссылка скопирована!", "processing": "Обработка...", "liked": "Понравилось", "like": "Нравится", "share": "Поделиться"}, "missions": {"title": "Миссии", "tasks": "Задания", "partnerRewards": "Партнерские награды", "special": "Специальное", "telegramRequired": "Пожалуйста, откройте приложение через Telegram, чтобы начать миссии.", "telegramRequiredClaim": "Пожал<PERSON>йста, откройте через приложение Telegram, чтобы получить эту награду", "loading": "Загрузка...", "missionStarted": "Миссия начата! Ожидание проверки: {minutes}.", "partnerMissionStarted": "Партнерская миссия начата! Ожидание проверки.", "verificationInProgress": "Идет проверка миссии. Пожалуйста, дождитесь завершения проверки.", "alreadyCompleted": "Вы уже выполнили эту миссию.", "failedToStart": "Не удалось начать миссию", "somethingWentWrong": "Что-то пошло не так", "networkError": "Ошибка сети", "failedToComplete": "Не удалось завершить миссию", "missionCompleted": "Миссия выполнена! Вы заработали {amount} {type}", "noMissionsAvailable": "На данный момент нет доступных миссий.", "noPartnerRewardsAvailable": "На данный момент нет доступных партнерских наград.", "estimatedVerificationTime": "Расчетное время проверки", "completed": "Выполнено", "claimReward": "Получить награду", "continueTask": "Продолжить задание", "continuePartnerTask": "Продолжить партнерское задание", "startTask": "Начать задание", "visitPartner": "Посетить партнера", "enterPromoCode": "Пожалуйста, введите промо-код", "promoCodeSubmitted": "Промо-код отправлен! Получено 500 токенов!", "enterPromoCodeTitle": "Ввести промо-код", "redeemSpecialRewards": "Обменяйте специальные награды и токены", "enterCodeHere": "Введите код здесь", "submit": "Отправить"}}