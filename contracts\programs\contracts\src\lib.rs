use anchor_lang::prelude::*;
use anchor_lang::solana_program::{clock::Clock, program::invoke, program::invoke_signed, system_instruction};

declare_id!("6UX6hhvGZDitWT1Tfr2iUo7BpCFNmcPUccXKgB4cA2J7");

#[event]
pub struct InvestmentCreatedEvent {
    pub investment_pda: Pubkey,
    pub investment_id: String,
    pub owner: Pubkey,
    pub amount: u64,
    pub timestamp: i64,
    pub referral_count: u8,
}

#[event]
pub struct RoiClaimedEvent {
    pub investment_pda: Pubkey,
    pub investment_id: String,
    pub owner: Pubkey,
    pub amount: u64,
    pub timestamp: i64,
    pub total_claimed: u64,
    pub claims_count: u64,
}

#[event]
pub struct FundsAddedEvent {
    pub investment_pda: Pubkey,
    pub investment_id: String,
    pub owner: Pubkey,
    pub amount: u64,
    pub new_total: u64,
    pub timestamp: i64,
}

#[event]
pub struct FundsWithdrawnEvent {
    pub investment_pda: Pubkey,
    pub investment_id: String,
    pub owner: Pubkey,
    pub amount: u64,
    pub source: String,
    pub remaining_balance: u64,
    pub timestamp: i64,
}

#[event]
pub struct InvestmentCompletedEvent {
    pub investment_pda: Pubkey,
    pub investment_id: String,
    pub owner: Pubkey,
    pub initial_investment: u64,
    pub total_earned: u64,
    pub total_withdrawn: u64,
    pub lifetime_days: u64,
    pub timestamp: i64,
}

#[event]
pub struct AdminWithdrawalEvent {
    pub admin: Pubkey,
    pub amount: u64,
    pub timestamp: i64,
}

#[event]
pub struct AdminDepositEvent {
    pub admin: Pubkey,
    pub amount: u64,
    pub timestamp: i64,
}

#[program]
pub mod flow_trade {
    use super::*;

    pub fn initialize(ctx: Context<Initialize>, admin: Pubkey) -> Result<()> {
        let contract_state = &mut ctx.accounts.contract_state;
        contract_state.admin = admin;
        contract_state.pool_balance = 0;
        Ok(())
    }

    pub fn create_investment(
        ctx: Context<CreateInvestment>,
        amount: u64,
        referrer_keys: Vec<Pubkey>,
        referrer_percentages: Vec<u8>,
    ) -> Result<String> {
        // Extract data before mutable borrows
        let investment_key = ctx.accounts.investment.key();
        let current_time = Clock::get()?.unix_timestamp;
        let investment_id = format!("INV{}", &investment_key.to_string()[..8]);
        let referral_count = referrer_keys.len() as u8;
        let user_key = ctx.accounts.user.key();

        // Get mutable references
        let investment = &mut ctx.accounts.investment;
        let contract_state = &mut ctx.accounts.contract_state;
        let user = &ctx.accounts.user;

        // Validations
        require!(amount >= 50_000_000, FlowError::MinInvestment);
        require!(
            investment.initial_investment == 0 || investment.status == InvestmentStatus::Completed,
            FlowError::ExistingInvestment
        );
        
        // Initialize investment
        investment.owner = user_key;
        investment.initial_investment = amount;
        investment.earned_amount = 0;
        investment.refer_earned_amount = 0;
        investment.percentage_of_roi = 0;
        investment.claims_since_withdrawal = 0;
        investment.last_claim_time = current_time;
        investment.last_withdrawal_time = 0;
        investment.withdrawal_amount = 0;
        investment.withdrawn_refer_amount = 0;
        investment.status = InvestmentStatus::Active;
        investment.creation_time = current_time;
        investment.investment_id = investment_id.clone();
        investment.total_claimed_amount = 0;
        investment.referral_count = referral_count;
        investment.last_action = "Created".to_string();
        investment.total_lifetime_claims = 0;

        // Update pool balance
        contract_state.pool_balance = contract_state
            .pool_balance
            .checked_add(amount)
            .ok_or(FlowError::ArithmeticOverflow)?;

        // Transfer funds to pool
        invoke(
            &system_instruction::transfer(&user.key(), &ctx.accounts.pool.key(), amount),
            &[
                user.to_account_info(),
                ctx.accounts.pool.to_account_info(),
                ctx.accounts.system_program.to_account_info(),
            ],
        )?;

        // Emit investment created event
        emit!(InvestmentCreatedEvent {
            investment_pda: ctx.accounts.investment.key(),
            investment_id: investment_id.clone(),
            owner: user_key,
            amount,
            timestamp: current_time,
            referral_count: referral_count,
        });

        // Process referrals ONLY if we have valid referrer keys AND accounts
        if !referrer_keys.is_empty() {
            // Validate referral data length consistency
            require!(
                referrer_keys.len() == referrer_percentages.len(),
                FlowError::InvalidReferralData
            );
            
            // Validate maximum referrals allowed (max 5 levels)
            require!(
                referrer_keys.len() <= 5,
                FlowError::TooManyReferrals
            );

            // Get the remaining accounts for referrers
            let remaining_accounts = &ctx.remaining_accounts;

            // We need pairs of accounts: referrer_investment and referrer
            require!(
                remaining_accounts.len() >= referrer_keys.len() * 2,
                FlowError::InsufficientReferrerAccounts
            );

            // Process each referrer
            for i in 0..referrer_keys.len() {
                let referrer_key = referrer_keys[i];
                let percentage = referrer_percentages[i];

                // Validate percentage is reasonable (1-10%)
                require!(
                    percentage >= 1 && percentage <= 10,
                    FlowError::InvalidReferralPercentage
                );

                // Get referrer accounts from remaining accounts
                let referrer_investment_info = &remaining_accounts[i * 2];
                let referrer_info = &remaining_accounts[i * 2 + 1];

                // Verify the referrer account matches
                require!(
                    *referrer_info.key == referrer_key,
                    FlowError::InvalidReferrer
                );

                // Try to deserialize the referrer investment
                let mut referrer_investment_data =
                    referrer_investment_info.try_borrow_mut_data()?;
                let mut referrer_investment =
                    Investment::try_deserialize(&mut referrer_investment_data.as_ref())?;

                // Validate referrer investment - MUST be ACTIVE
                require!(
                    referrer_investment.status == InvestmentStatus::Active,
                    FlowError::ReferrerInactive
                );
                require!(
                    referrer_investment.owner == referrer_key,
                    FlowError::InvalidReferrer
                );

                // Calculate reward based on the dynamic percentage sent
                let reward = (amount as u128)
                    .checked_mul(percentage as u128)
                    .ok_or(FlowError::ArithmeticOverflow)?
                    .checked_div(100)
                    .ok_or(FlowError::ArithmeticOverflow)? as u64;

                // Check referrer's ROI cap (250%)
                let max_earnings = (referrer_investment.initial_investment as u128)
                    .checked_mul(250)
                    .ok_or(FlowError::ArithmeticOverflow)?
                    .checked_div(100)
                    .ok_or(FlowError::ArithmeticOverflow)?
                    as u64;

                let current_total = referrer_investment
                    .earned_amount
                    .checked_add(referrer_investment.refer_earned_amount)
                    .ok_or(FlowError::ArithmeticOverflow)?;

                // Adjust reward if it would exceed ROI cap
                let adjusted_reward = max_earnings
                    .checked_sub(current_total)
                    .unwrap_or(0)
                    .min(reward);

                // Only process if there's actual reward to give
                if adjusted_reward > 0 {
                    // Update referrer investment
                    referrer_investment.refer_earned_amount = referrer_investment
                        .refer_earned_amount
                        .checked_add(adjusted_reward)
                        .ok_or(FlowError::ArithmeticOverflow)?;

                    referrer_investment.percentage_of_roi = ((referrer_investment
                        .earned_amount
                        .checked_add(referrer_investment.refer_earned_amount)
                        .ok_or(FlowError::ArithmeticOverflow)?
                        as u128)
                        .checked_mul(100)
                        .ok_or(FlowError::ArithmeticOverflow)?
                        / (referrer_investment.initial_investment as u128))
                        as u64;

                    // Serialize the updated referrer investment back
                    referrer_investment.try_serialize(&mut referrer_investment_data.as_mut())?;

                    // Transfer reward to referrer
                    **ctx
                        .accounts
                        .pool
                        .to_account_info()
                        .try_borrow_mut_lamports()? -= adjusted_reward;
                    **referrer_info.try_borrow_mut_lamports()? += adjusted_reward;

                    // Update pool balance
                    contract_state.pool_balance = contract_state
                        .pool_balance
                        .checked_sub(adjusted_reward)
                        .ok_or(FlowError::InsufficientPoolBalance)?;
                }
            }
        }

        Ok(investment_id)
    }

    pub fn get_investment_details(ctx: Context<GetInvestmentDetails>) -> Result<InvestmentDetails> {
        let investment = &ctx.accounts.investment;
        Ok(InvestmentDetails {
            investment_id: format!("INV{}", &investment.key().to_string()[..8]),
            owner: investment.owner,
            initial_investment: investment.initial_investment,
            earned_amount: investment.earned_amount,
            refer_earned_amount: investment.refer_earned_amount,
            percentage_of_roi: investment.percentage_of_roi,
            claims_since_withdrawal: investment.claims_since_withdrawal,
            last_claim_time: investment.last_claim_time,
            last_withdrawal_time: investment.last_withdrawal_time,
            withdrawal_amount: investment.withdrawal_amount,
            withdrawn_refer_amount: investment.withdrawn_refer_amount,
            status: investment.status.clone(),
        })
    }

    pub fn claim_roi(ctx: Context<ClaimRoi>) -> Result<()> {
        let investment = &mut ctx.accounts.investment;
        require!(
            investment.status == InvestmentStatus::Active,
            FlowError::InvalidInvestmentStatus
        );

        let current_time = Clock::get()?.unix_timestamp;
        require!(
            investment.last_claim_time == 0 || current_time >= investment.last_claim_time + 129_600,
            FlowError::ClaimCooldown
        );

        let max_earnings = (investment.initial_investment as u128)
            .checked_mul(250)
            .ok_or(FlowError::ArithmeticOverflow)?
            .checked_div(100)
            .ok_or(FlowError::ArithmeticOverflow)? as u64;
        let current_total = investment
            .earned_amount
            .checked_add(investment.refer_earned_amount)
            .ok_or(FlowError::ArithmeticOverflow)?;
        require!(current_total < max_earnings, FlowError::RoiCapReached);

        let claim_amount = (investment.initial_investment as u128)
            .checked_mul(15)
            .ok_or(FlowError::ArithmeticOverflow)?
            .checked_div(1000)
            .ok_or(FlowError::ArithmeticOverflow)? as u64;

        let adjusted_claim = max_earnings
            .checked_sub(current_total)
            .unwrap_or(0)
            .min(claim_amount);

        investment.earned_amount = investment
            .earned_amount
            .checked_add(adjusted_claim)
            .ok_or(FlowError::ArithmeticOverflow)?;
        investment.percentage_of_roi =
            ((investment
                .earned_amount
                .checked_add(investment.refer_earned_amount)
                .ok_or(FlowError::ArithmeticOverflow)? as u128)
                .checked_mul(100)
                .ok_or(FlowError::ArithmeticOverflow)?
                / (investment.initial_investment as u128)) as u64;
        investment.claims_since_withdrawal = investment
            .claims_since_withdrawal
            .checked_add(1)
            .ok_or(FlowError::ArithmeticOverflow)?;
        investment.last_claim_time = current_time;

        // Update tracking fields
        investment.total_claimed_amount = investment
            .total_claimed_amount
            .checked_add(adjusted_claim)
            .ok_or(FlowError::ArithmeticOverflow)?;
        investment.total_lifetime_claims = investment
            .total_lifetime_claims
            .checked_add(1)
            .ok_or(FlowError::ArithmeticOverflow)?;
        investment.last_action = "Claimed ROI".to_string();

        // Emit enhanced ROI claimed event
        emit!(RoiClaimedEvent {
            investment_pda: investment.key(),
            investment_id: investment.investment_id.clone(),
            owner: investment.owner,
            amount: adjusted_claim,
            timestamp: current_time,
            total_claimed: investment.total_claimed_amount,
            claims_count: investment.total_lifetime_claims,
        });

        if investment.percentage_of_roi >= 250
            && investment.earned_amount == investment.withdrawal_amount
            && investment.refer_earned_amount == investment.withdrawn_refer_amount
        {
            investment.status = InvestmentStatus::Completed;

            // Emit investment completed event
            // Calculate lifetime in days
            let lifetime_days = ((current_time - investment.creation_time) / 86400) as u64;

            emit!(InvestmentCompletedEvent {
                investment_pda: investment.key(),
                investment_id: investment.investment_id.clone(),
                owner: investment.owner,
                initial_investment: investment.initial_investment,
                total_earned: investment
                    .earned_amount
                    .checked_add(investment.refer_earned_amount)
                    .unwrap_or(0),
                total_withdrawn: investment
                    .withdrawal_amount
                    .checked_add(investment.withdrawn_refer_amount)
                    .unwrap_or(0),
                lifetime_days,
                timestamp: current_time,
            });
        }

        Ok(())
    }

    pub fn add_more_funds(
        ctx: Context<AddMoreFunds>, 
        amount: u64,
        referrer_keys: Vec<Pubkey>,
        referrer_percentages: Vec<u8>,
    ) -> Result<()> {
        let investment = &mut ctx.accounts.investment;
        let contract_state = &mut ctx.accounts.contract_state;
        let pool = &mut ctx.accounts.pool;
        let user = &ctx.accounts.user;
    
        require!(
            investment.status == InvestmentStatus::Active,
            FlowError::InvalidInvestmentStatus
        );
        require!(amount >= 50_000_000, FlowError::MinDeposit);
    
        invoke(
            &system_instruction::transfer(&user.key(), &pool.key(), amount),
            &[
                user.to_account_info(),
                pool.to_account_info(),
                ctx.accounts.system_program.to_account_info(),
            ],
        )?;
    
        contract_state.pool_balance = contract_state
            .pool_balance
            .checked_add(amount)
            .ok_or(FlowError::ArithmeticOverflow)?;
    
        investment.initial_investment = investment
            .initial_investment
            .checked_add(amount)
            .ok_or(FlowError::ArithmeticOverflow)?;
        investment.percentage_of_roi =
            ((investment
                .earned_amount
                .checked_add(investment.refer_earned_amount)
                .ok_or(FlowError::ArithmeticOverflow)? as u128)
                .checked_mul(100)
                .ok_or(FlowError::ArithmeticOverflow)?
                / (investment.initial_investment as u128)) as u64;
        investment.last_claim_time = Clock::get()?.unix_timestamp;
    
        // Emit enhanced funds added event
        emit!(FundsAddedEvent {
            investment_pda: investment.key(),
            investment_id: investment.investment_id.clone(),
            owner: investment.owner,
            amount,
            new_total: investment.initial_investment,
            timestamp: Clock::get()?.unix_timestamp,
        });
    
        // Process referrals ONLY if we have valid referrer keys AND accounts
        if !referrer_keys.is_empty() {
            // Validate referral data length consistency
            require!(
                referrer_keys.len() == referrer_percentages.len(),
                FlowError::InvalidReferralData
            );
            
            // Validate maximum referrals allowed (max 5 levels)
            require!(
                referrer_keys.len() <= 5,
                FlowError::TooManyReferrals
            );
    
            // Get the remaining accounts for referrers
            let remaining_accounts = &ctx.remaining_accounts;
    
            // We need pairs of accounts: referrer_investment and referrer
            require!(
                remaining_accounts.len() >= referrer_keys.len() * 2,
                FlowError::InsufficientReferrerAccounts
            );
    
            // Process each referrer
            for i in 0..referrer_keys.len() {
                let referrer_key = referrer_keys[i];
                let percentage = referrer_percentages[i];
    
                // Validate percentage is reasonable (1-10%)
                require!(
                    percentage >= 1 && percentage <= 10,
                    FlowError::InvalidReferralPercentage
                );
    
                // Get referrer accounts from remaining accounts
                let referrer_investment_info = &remaining_accounts[i * 2];
                let referrer_info = &remaining_accounts[i * 2 + 1];
    
                // Verify the referrer account matches
                require!(
                    *referrer_info.key == referrer_key,
                    FlowError::InvalidReferrer
                );
    
                // Try to deserialize the referrer investment
                let mut referrer_investment_data =
                    referrer_investment_info.try_borrow_mut_data()?;
                let mut referrer_investment =
                    Investment::try_deserialize(&mut referrer_investment_data.as_ref())?;
    
                // Validate referrer investment - MUST be ACTIVE
                require!(
                    referrer_investment.status == InvestmentStatus::Active,
                    FlowError::ReferrerInactive
                );
                require!(
                    referrer_investment.owner == referrer_key,
                    FlowError::InvalidReferrer
                );
    
                // Calculate reward based on the dynamic percentage sent
                let reward = (amount as u128)
                    .checked_mul(percentage as u128)
                    .ok_or(FlowError::ArithmeticOverflow)?
                    .checked_div(100)
                    .ok_or(FlowError::ArithmeticOverflow)? as u64;
    
                // Check referrer's ROI cap (250%)
                let max_earnings = (referrer_investment.initial_investment as u128)
                    .checked_mul(250)
                    .ok_or(FlowError::ArithmeticOverflow)?
                    .checked_div(100)
                    .ok_or(FlowError::ArithmeticOverflow)?
                    as u64;
    
                let current_total = referrer_investment
                    .earned_amount
                    .checked_add(referrer_investment.refer_earned_amount)
                    .ok_or(FlowError::ArithmeticOverflow)?;
    
                // Adjust reward if it would exceed ROI cap
                let adjusted_reward = max_earnings
                    .checked_sub(current_total)
                    .unwrap_or(0)
                    .min(reward);
    
                // Only process if there's actual reward to give
                if adjusted_reward > 0 {
                    // Update referrer investment
                    referrer_investment.refer_earned_amount = referrer_investment
                        .refer_earned_amount
                        .checked_add(adjusted_reward)
                        .ok_or(FlowError::ArithmeticOverflow)?;
    
                    referrer_investment.percentage_of_roi = ((referrer_investment
                        .earned_amount
                        .checked_add(referrer_investment.refer_earned_amount)
                        .ok_or(FlowError::ArithmeticOverflow)?
                        as u128)
                        .checked_mul(100)
                        .ok_or(FlowError::ArithmeticOverflow)?
                        / (referrer_investment.initial_investment as u128))
                        as u64;
    
                    // Serialize the updated referrer investment back
                    referrer_investment.try_serialize(&mut referrer_investment_data.as_mut())?;
    
                    // Transfer reward to referrer
                    **ctx
                        .accounts
                        .pool
                        .to_account_info()
                        .try_borrow_mut_lamports()? -= adjusted_reward;
                    **referrer_info.try_borrow_mut_lamports()? += adjusted_reward;
    
                    // Update pool balance
                    contract_state.pool_balance = contract_state
                        .pool_balance
                        .checked_sub(adjusted_reward)
                        .ok_or(FlowError::InsufficientPoolBalance)?;
                }
            }
        }
    
        Ok(())
    }
   

    pub fn withdraw(ctx: Context<Withdraw>, amount: u64, source: WithdrawalSource) -> Result<()> {
        let investment = &mut ctx.accounts.investment;
        let pool = &ctx.accounts.pool;  // Note: removed &mut since SystemAccount handles it differently
        let contract_state = &mut ctx.accounts.contract_state;
        let user = &ctx.accounts.user;
    
        let withdrawable_amount: u64 = amount * 95 / 100;
        require!(amount >= 50_000_000, FlowError::MinWithdrawal);
        let current_time = Clock::get()?.unix_timestamp;
        require!(
            investment.last_withdrawal_time == 0
                || current_time >= investment.last_withdrawal_time + 24 * 3600,
            FlowError::WithdrawalCooldown
        );

        // Check pool has sufficient lamports for withdrawal
        require!(
            pool.to_account_info().lamports() >= withdrawable_amount && contract_state.pool_balance >= withdrawable_amount,
            FlowError::InsufficientPoolBalance
        );

        match source {
            WithdrawalSource::Zap => {
                if investment.percentage_of_roi < 250 {
                    require!(
                        investment.claims_since_withdrawal >= 5,
                        FlowError::InsufficientClaims
                    );
                }
                let available = investment
                    .earned_amount
                    .checked_sub(investment.withdrawal_amount)
                    .ok_or(FlowError::InsufficientBalance)?;
                require!(amount <= available, FlowError::InsufficientBalance);

                investment.withdrawal_amount = investment
                    .withdrawal_amount
                    .checked_add(amount)
                    .ok_or(FlowError::ArithmeticOverflow)?;
                investment.claims_since_withdrawal = 0;
            }
            WithdrawalSource::Referral => {
                let available = investment
                    .refer_earned_amount
                    .checked_sub(investment.withdrawn_refer_amount)
                    .ok_or(FlowError::InsufficientBalance)?;
                require!(amount <= available, FlowError::InsufficientBalance);

                investment.withdrawn_refer_amount = investment
                    .withdrawn_refer_amount
                    .checked_add(amount)
                    .ok_or(FlowError::ArithmeticOverflow)?;
            }
            WithdrawalSource::Both => {
                if investment.percentage_of_roi < 250 {
                    require!(
                        investment.claims_since_withdrawal >= 5,
                        FlowError::InsufficientClaims
                    );
                }
                let zap_available = investment
                    .earned_amount
                    .checked_sub(investment.withdrawal_amount)
                    .ok_or(FlowError::InsufficientBalance)?;
                let refer_available = investment
                    .refer_earned_amount
                    .checked_sub(investment.withdrawn_refer_amount)
                    .ok_or(FlowError::InsufficientBalance)?;
                let total_available = zap_available
                    .checked_add(refer_available)
                    .ok_or(FlowError::ArithmeticOverflow)?;
                require!(amount <= total_available, FlowError::InsufficientBalance);

                let zap_amount = amount.min(zap_available);
                let refer_amount = amount
                    .checked_sub(zap_amount)
                    .ok_or(FlowError::ArithmeticOverflow)?;

                if zap_amount > 0 {
                    investment.withdrawal_amount = investment
                        .withdrawal_amount
                        .checked_add(zap_amount)
                        .ok_or(FlowError::ArithmeticOverflow)?;
                    investment.claims_since_withdrawal = 0;
                }
                if refer_amount > 0 {
                    investment.withdrawn_refer_amount = investment
                        .withdrawn_refer_amount
                        .checked_add(refer_amount)
                        .ok_or(FlowError::ArithmeticOverflow)?;
                }
            }
        }

        // Transfer lamports directly from pool PDA to user
        **pool.to_account_info().try_borrow_mut_lamports()? -= withdrawable_amount;
        **user.to_account_info().try_borrow_mut_lamports()? += withdrawable_amount;

        contract_state.pool_balance = contract_state
            .pool_balance
            .checked_sub(withdrawable_amount)
            .ok_or(FlowError::InsufficientPoolBalance)?;
        investment.last_withdrawal_time = current_time;
    
        // Compute the remaining balance based on the withdrawal source
        let remaining_balance = match source {
            WithdrawalSource::Zap => investment.earned_amount.checked_sub(amount).unwrap_or(0),
            WithdrawalSource::Referral => investment
                .refer_earned_amount
                .checked_sub(amount)
                .unwrap_or(0),
            WithdrawalSource::Both => {
                let total = investment
                    .earned_amount
                    .checked_add(investment.refer_earned_amount)
                    .unwrap_or(0)
                    .checked_sub(amount)
                    .unwrap_or(0);
                total
            }
        };
    
        // Get string representation of the source
        let source_str = match source {
            WithdrawalSource::Zap => "Zap".to_string(),
            WithdrawalSource::Referral => "Referral".to_string(),
            WithdrawalSource::Both => "Both".to_string(),
        };
    
        // Emit enhanced funds withdrawn event
        emit!(FundsWithdrawnEvent {
            investment_pda: investment.key(),
            investment_id: investment.investment_id.clone(),
            owner: investment.owner,
            amount,
            source: source_str,
            remaining_balance,
            timestamp: current_time,
        });
    
        if investment.percentage_of_roi >= 250
            && investment.earned_amount == investment.withdrawal_amount
            && investment.refer_earned_amount == investment.withdrawn_refer_amount
        {
            investment.status = InvestmentStatus::Completed;
    
            // Emit investment completed event
            // Calculate lifetime in days
            let lifetime_days = ((current_time - investment.creation_time) / 86400) as u64;
    
            emit!(InvestmentCompletedEvent {
                investment_pda: investment.key(),
                investment_id: investment.investment_id.clone(),
                owner: investment.owner,
                initial_investment: investment.initial_investment,
                total_earned: investment
                    .earned_amount
                    .checked_add(investment.refer_earned_amount)
                    .unwrap_or(0),
                total_withdrawn: investment
                    .withdrawal_amount
                    .checked_add(investment.withdrawn_refer_amount)
                    .unwrap_or(0),
                lifetime_days,
                timestamp: current_time,
            });
        }
    
        Ok(())
    }
    
    
  
// Fixed admin_withdraw function
pub fn admin_withdraw(ctx: Context<AdminWithdrawAccounts>, amount: u64) -> Result<()> {
    let contract_state = &mut ctx.accounts.contract_state;
    let pool = &ctx.accounts.pool;
    let admin = &ctx.accounts.admin;

    require!(admin.key() == contract_state.admin, FlowError::Unauthorized);
    require!(
        contract_state.pool_balance >= amount,
        FlowError::InsufficientPoolBalance
    );

    // Transfer lamports directly from pool PDA to admin
    **pool.to_account_info().try_borrow_mut_lamports()? -= amount;
    **admin.to_account_info().try_borrow_mut_lamports()? += amount;

    contract_state.pool_balance = contract_state
        .pool_balance
        .checked_sub(amount)
        .ok_or(FlowError::InsufficientPoolBalance)?;

    // Emit admin withdrawal event
    emit!(AdminWithdrawalEvent {
        admin: admin.key(),
        amount,
        timestamp: Clock::get()?.unix_timestamp,
    });

    Ok(())
}

    pub fn admin_add_funds_to_pool(ctx: Context<AdminAddFundsAccounts>, amount: u64) -> Result<()> {
        let contract_state = &mut ctx.accounts.contract_state;
        let pool = &mut ctx.accounts.pool;
        let admin = &ctx.accounts.admin;

        require!(admin.key() == contract_state.admin, FlowError::Unauthorized);
        require!(amount >= 50_000_000, FlowError::MinDeposit);

        invoke(
            &system_instruction::transfer(&admin.key(), &pool.key(), amount),
            &[
                admin.to_account_info(),
                pool.to_account_info(),
                ctx.accounts.system_program.to_account_info(),
            ],
        )?;

        contract_state.pool_balance = contract_state
            .pool_balance
            .checked_add(amount)
            .ok_or(FlowError::ArithmeticOverflow)?;

        // Emit admin deposit event
        emit!(AdminDepositEvent {
            admin: admin.key(),
            amount,
            timestamp: Clock::get()?.unix_timestamp,
        });

        Ok(())
    }
}

#[derive(Accounts)]
pub struct Initialize<'info> {
    #[account(init, payer = signer, space = 8 + 32 + 8, seeds = [b"contract_state"], bump)]
    pub contract_state: Account<'info, ContractState>,
    #[account(init, payer = signer, space = 8, seeds = [b"pool"], bump)]
    pub pool: Account<'info, Pool>,
    #[account(mut)]
    pub signer: Signer<'info>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct CreateInvestment<'info> {
    #[account(init, payer = user, space = 8 + 32 + 8 + 8 + 8 + 8 + 8 + 8 + 8 + 8 + 8 + 1 + 8 + 64 + 8 + 1 + 32 + 8, seeds = [b"investment", user.key().as_ref()], bump)]
    pub investment: Account<'info, Investment>,
    #[account(mut, seeds = [b"contract_state"], bump)]
    pub contract_state: Account<'info, ContractState>,
    #[account(mut, seeds = [b"pool"], bump)]
    pub pool: Account<'info, Pool>,
    #[account(mut)]
    pub user: Signer<'info>,
    pub system_program: Program<'info, System>,
    // Referrer accounts will be passed as remaining_accounts
}

#[derive(Accounts)]
pub struct GetInvestmentDetails<'info> {
    #[account(seeds = [b"investment", owner.key().as_ref()], bump)]
    pub investment: Account<'info, Investment>,
    pub owner: Signer<'info>,
}

#[derive(Accounts)]
pub struct ClaimRoi<'info> {
    #[account(mut, seeds = [b"investment", user.key().as_ref()], bump)]
    pub investment: Account<'info, Investment>,
    #[account(mut)]
    pub user: Signer<'info>,
}

// Modified AddMoreFunds struct to include remaining_accounts for referrers
#[derive(Accounts)]
pub struct AddMoreFunds<'info> {
    #[account(mut, seeds = [b"investment", user.key().as_ref()], bump)]
    pub investment: Account<'info, Investment>,
    #[account(mut, seeds = [b"contract_state"], bump)]
    pub contract_state: Account<'info, ContractState>,
    #[account(mut, seeds = [b"pool"], bump)]
    pub pool: Account<'info, Pool>,
    #[account(mut)]
    pub user: Signer<'info>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct Withdraw<'info> {
    #[account(mut, seeds = [b"investment", user.key().as_ref()], bump)]
    pub investment: Account<'info, Investment>,
    #[account(mut, seeds = [b"contract_state"], bump)]
    pub contract_state: Account<'info, ContractState>,
    #[account(mut, seeds = [b"pool"], bump)]
    pub pool: Account<'info, Pool>,
    #[account(mut)]
    pub user: Signer<'info>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct AdminWithdrawAccounts<'info> {
    #[account(mut, seeds = [b"contract_state"], bump)]
    pub contract_state: Account<'info, ContractState>,
    #[account(mut, seeds = [b"pool"], bump)]
    pub pool: Account<'info, Pool>,
    #[account(mut)]
    pub admin: Signer<'info>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct AdminAddFundsAccounts<'info> {
    #[account(mut, seeds = [b"contract_state"], bump)]
    pub contract_state: Account<'info, ContractState>,
    #[account(mut, seeds = [b"pool"], bump)]
    pub pool: Account<'info, Pool>,
    #[account(mut)]
    pub admin: Signer<'info>,
    pub system_program: Program<'info, System>,
}

#[account]
pub struct ContractState {
    pub admin: Pubkey,
    pub pool_balance: u64,
}

#[account]
pub struct Pool {}

#[account]
pub struct Investment {
    pub owner: Pubkey,
    pub initial_investment: u64,
    pub earned_amount: u64,
    pub refer_earned_amount: u64,
    pub percentage_of_roi: u64,
    pub claims_since_withdrawal: u64,
    pub last_claim_time: i64,
    pub last_withdrawal_time: i64,
    pub withdrawal_amount: u64,
    pub withdrawn_refer_amount: u64,
    pub status: InvestmentStatus,
    pub creation_time: i64,    // Creation timestamp for tracking on Solscan
    pub investment_id: String, // Unique ID for investment tracking
    pub total_claimed_amount: u64, // Track total claimed ROI over lifetime
    pub referral_count: u8,    // Number of referrers in chain
    pub last_action: String,   // Last action performed (for tracking)
    pub total_lifetime_claims: u64, // Total number of ROI claims over lifetime
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone)]
pub struct InvestmentDetails {
    pub investment_id: String,
    pub owner: Pubkey,
    pub initial_investment: u64,
    pub earned_amount: u64,
    pub refer_earned_amount: u64,
    pub percentage_of_roi: u64,
    pub claims_since_withdrawal: u64,
    pub last_claim_time: i64,
    pub last_withdrawal_time: i64,
    pub withdrawal_amount: u64,
    pub withdrawn_refer_amount: u64,
    pub status: InvestmentStatus,
}
#[derive(AnchorSerialize, AnchorDeserialize, Clone, PartialEq, Copy)]
pub enum InvestmentStatus {
    Active,
    Completed,
}

// Default implementation for InvestmentStatus
impl Default for InvestmentStatus {
    fn default() -> Self {
        InvestmentStatus::Active
    }
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, PartialEq, Copy)]
pub enum WithdrawalSource {
    Zap,
    Referral,
    Both,
}

// Default implementation for WithdrawalSource
impl Default for WithdrawalSource {
    fn default() -> Self {
        WithdrawalSource::Zap
    }
}

#[error_code]
pub enum FlowError {
    #[msg("Unauthorized: Only admin can perform this action")]
    Unauthorized,
    #[msg("Investment amount below minimum (0.05 SOL)")]
    MinInvestment,
    #[msg("User already has an active investment")]
    ExistingInvestment,
    #[msg("Arithmetic overflow occurred")]
    ArithmeticOverflow,
    #[msg("Withdrawal amount below minimum (0.05 SOL)")]
    MinWithdrawal,
    #[msg("24-hour withdrawal cooldown not met")]
    WithdrawalCooldown,
    #[msg("Insufficient claims for withdrawal (need 5)")]
    InsufficientClaims,
    #[msg("Insufficient balance for withdrawal")]
    InsufficientBalance,
    #[msg("Insufficient pool balance")]
    InsufficientPoolBalance,
    #[msg("Invalid referral percentage")]
    InvalidReferralPercentage,
    #[msg("Referrer does not have an active investment")]
    ReferrerInactive,
    #[msg("36-hour claim cooldown not met")]
    ClaimCooldown,
    #[msg("ROI cap of 250% reached")]
    RoiCapReached,
    #[msg("Investment is not active")]
    InvalidInvestmentStatus,
    #[msg("Deposit amount below minimum (0.05 SOL)")]
    MinDeposit,
    #[msg("Too many referrals provided")]
    TooManyReferrals,
    #[msg("Invalid referrer account")]
    InvalidReferrer,
    #[msg("Referrer keys and percentages must have the same length")]
    InvalidReferralData,
    #[msg("Insufficient referrer accounts provided")]
    InsufficientReferrerAccounts,
}