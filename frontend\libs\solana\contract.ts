import { Connection, PublicKey, Transaction, SystemProgram, clusterApiUrl, Keypair, ComputeBudgetProgram } from '@solana/web3.js';
import { WalletAdapter } from '@solana/wallet-adapter-base';
import { BN, Program, AnchorProvider, web3, Idl } from '@coral-xyz/anchor';
import * as anchor from '@coral-xyz/anchor';


// Investment status enum
enum InvestmentStatus {
  Active = 'Active',
  Completed = 'Completed'
}

// Withdrawal source enum
enum WithdrawalSource {
  Zap = 'Zap',
  Referral = 'Referral',
  Both = 'Both'
}

// Investment data interface
interface InvestmentData {
  owner: PublicKey;
  initialInvestment: BN;
  earnedAmount: BN;
  referEarnedAmount: BN;
  percentageOfRoi: BN;
  claimsSinceWithdrawal: BN;
  lastClaimTime: BN;
  lastWithdrawalTime: BN;
  withdrawalAmount: BN;
  withdrawnReferAmount: BN;
  status: { [key: string]: {} };
}

// Contract State interface
interface ContractState {
  admin: PublicKey;
  poolBalance: BN;
}

// Define proper program type
type FlowTradeProgram = Program<Idl>;

// Import your IDL with proper error handling
let idlJson: Idl;
try {
  idlJson = require('./flow_trade.json') as Idl;
} catch (error) {
  console.error('Failed to load IDL:', error);
  throw new Error('IDL file not found. Make sure flow_trade.json exists in the same directory');
}

// Program ID - use the one from your IDL or the deployed program
const PROGRAM_ID = new PublicKey('6UX6hhvGZDitWT1Tfr2iUo7BpCFNmcPUccXKgB4cA2J7');

// Connection configuration
const DEVNET_URL = clusterApiUrl('devnet');
const connection = new Connection(DEVNET_URL, {
  commitment: 'confirmed',
  confirmTransactionInitialTimeout: 60000,
});

// Utility functions
const solToLamports = (sol: number): number => Math.floor(sol * 1_000_000_000);
const lamportsToSol = (lamports: number | BN): number => {
  const value = typeof lamports === 'number' ? lamports : lamports.toNumber();
  return value / 1_000_000_000;
};

// PDA derivation functions
const getContractStatePDA = (): [PublicKey, number] => {
  return PublicKey.findProgramAddressSync(
    [Buffer.from('contract_state')],
    PROGRAM_ID
  );
};

const getPoolPDA = (): [PublicKey, number] => {
  return PublicKey.findProgramAddressSync(
    [Buffer.from('pool')],
    PROGRAM_ID
  );
};

const getInvestmentPDA = (userPublicKey: PublicKey): [PublicKey, number] => {
  return PublicKey.findProgramAddressSync(
    [Buffer.from('investment'), userPublicKey.toBuffer()],
    PROGRAM_ID
  );
};

// Create Anchor provider with proper typing
const createProvider = (wallet: WalletAdapter): AnchorProvider => {
  if (!wallet.publicKey) {
    throw new Error('Wallet not connected');
  }

  const provider = new AnchorProvider(
    connection,
    wallet as any,
    {
      commitment: 'confirmed',
      preflightCommitment: 'confirmed',
    }
  );
  
  console.log('Provider created successfully', provider.wallet.publicKey.toBase58());
  return provider;
};

// Program instance management
let programInstance: FlowTradeProgram | null = null;
let currentWalletKey: string | null = null;

// Create program instance with proper error handling and typing
const createProgram = (provider: AnchorProvider): FlowTradeProgram => {
  try {
    console.log('Creating program with provider...');
    console.log('Provider wallet:', provider.wallet.publicKey.toBase58());
    console.log('Program ID:', PROGRAM_ID.toBase58());

    const program = new Program(idlJson, provider) as FlowTradeProgram;
    
    console.log('Program created successfully', program);
    return program;
    
  } catch (error) {
    console.error('Failed to create program:', error);
    throw new Error(`Program initialization failed: ${error instanceof Error ? error.message : String(error)}`);
  }
};

// Get or create program instance
const getProgram = (wallet: WalletAdapter): FlowTradeProgram => {
  if (!wallet.publicKey) {
    throw new Error('Wallet not connected');
  }

  const walletKey = wallet.publicKey.toString();
  
  if (!programInstance || currentWalletKey !== walletKey) {
    console.log('Creating new program instance...');
    const provider = createProvider(wallet);
    programInstance = createProgram(provider);
    currentWalletKey = walletKey;
  }
  
  return programInstance;
};

// Enhanced error handling for Anchor errors
const handleAnchorError = (error: any): string => {
  console.error('Anchor error:', error);
  
  // Handle Anchor program errors
  if (error?.error?.errorCode) {
    return getErrorMessage(error.error.errorCode.code);
  }
  
  if (error?.error?.errorMessage) {
    return error.error.errorMessage;
  }
  
  if (error?.message) {
    // Handle custom program errors
    if (error.message.includes('custom program error:')) {
      const match = error.message.match(/custom program error: (0x[0-9a-f]+)/i);
      if (match?.[1]) {
        const errorCode = parseInt(match[1], 16);
        return getErrorMessage(errorCode);
      }
    }
    
    // Handle account not found errors
    if (error.message.includes('Account does not exist')) {
      return 'Investment account not found. Please create an investment first.';
    }
    
    return error.message;
  }
  
  return 'Unknown error occurred';
};

// Map error codes to messages
const getErrorMessage = (errorCode: number): string => {
  const errorMessages: Record<number, string> = {
    6000: 'Unauthorized: Only admin can perform this action',
    6001: 'Investment amount below minimum (0.05 SOL)',
    6002: 'User already has an active investment',
    6003: 'Arithmetic overflow occurred',
    6004: 'Withdrawal amount below minimum (0.05 SOL)',
    6005: '24-hour withdrawal cooldown not met',
    6006: 'Insufficient claims for withdrawal (need 5)',
    6007: 'Insufficient balance for withdrawal',
    6008: 'Insufficient pool balance',
    6009: 'Invalid referral percentage',
    6010: 'Referrer does not have an active investment',
    6011: '36-hour claim cooldown not met',
    6012: 'ROI cap of 250% reached',
    6013: 'Investment is not active',
    6014: 'Deposit amount below minimum (0.05 SOL)',
    6015: 'Too many referrals provided',
    6016: 'Invalid referrer account',
    6017: 'Referrer keys and percentages must have the same length',
  };
  
  return errorMessages[errorCode] || `Program error (code: ${errorCode})`;
};

// Enhanced transaction sending with better error handling
const sendTransaction = async (
  transaction: Transaction,
  wallet: WalletAdapter,
  signers: Keypair[] = []
): Promise<string> => {
  try {
    if (!wallet.publicKey) {
      throw new Error('Wallet not connected');
    }

    // Set fee payer
    transaction.feePayer = wallet.publicKey;
    
    // Get recent blockhash with retry logic
    let retries = 3;
    let blockhash: string;
    
    while (retries > 0) {
      try {
        const { blockhash: bh } = await connection.getLatestBlockhash('confirmed');
        blockhash = bh;
        break;
      } catch (error) {
        retries--;
        if (retries === 0) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    transaction.recentBlockhash = blockhash!;
    
    // Sign with additional signers if any
    if (signers.length > 0) {
      transaction.partialSign(...signers);
    }
    
    // Sign with wallet
    // @ts-ignore
    const signedTx = await wallet.signTransaction!(transaction);
    
    // Send transaction with better configuration
    const signature = await connection.sendRawTransaction(signedTx.serialize(), {
      skipPreflight: false,
      preflightCommitment: 'confirmed',
      maxRetries: 3,
    });
    
    console.log('Transaction sent:', signature);
    
    // Confirm transaction with timeout
    const confirmation = await connection.confirmTransaction({
      signature,
      blockhash: blockhash!,
      lastValidBlockHeight: (await connection.getLatestBlockhash()).lastValidBlockHeight,
    }, 'confirmed');
    
    if (confirmation.value.err) {
      throw new Error(`Transaction failed: ${JSON.stringify(confirmation.value.err)}`);
    }
    
    return signature;
  } catch (error) {
    console.error('Transaction failed:', error);
    throw error;
  }
};

// Result type for async operations
interface OperationResult {
  success: boolean;
  txId?: string;
  error?: string;
}

// Add this new function in your contract.ts
export const initializeContract = async (
  wallet: WalletAdapter,
  adminPubkey?: PublicKey // Optional: if you want to set a specific admin other than the wallet calling
): Promise<OperationResult> => {
  try {
    if (!wallet.publicKey) {
      throw new Error('Wallet not connected for initialization');
    }
    console.log('Initializing contract...');

    const program = getProgram(wallet);
    const adminKey = adminPubkey || wallet.publicKey; // Admin will be the signer if not specified

    const [contractStatePDA] = getContractStatePDA();
    const [poolPDA] = getPoolPDA();

    console.log('Initialization accounts:', {
      contractState: contractStatePDA.toString(),
      pool: poolPDA.toString(),
      signer: wallet.publicKey.toString(), // The wallet calling initialize is the payer and signer
      adminForState: adminKey.toString(), // The pubkey to be stored as admin
    });

    const instruction = await program.methods
      .initialize(adminKey) // Pass the admin public key
      .accounts({
        contractState: contractStatePDA,
        pool: poolPDA,
        signer: wallet.publicKey, // This is the `payer` and `signer` for init
        systemProgram: SystemProgram.programId,
      })
      .instruction();

    const transaction = new Transaction().add(instruction);
    const signature = await sendTransaction(transaction, wallet);

    console.log('Contract initialized successfully:', signature);
    console.log(`View on Solscan: https://solscan.io/tx/${signature}?cluster=devnet`);

    return { success: true, txId: signature };

  } catch (error) {
    console.error('Error initializing contract:', error);
    return { success: false, error: handleAnchorError(error) };
  }
};

export const createInvestment = async (
  wallet: WalletAdapter,
  amountSol: number,
  referrals: Array<{ walletAddress: string; percentage: number; level: number }> = []
): Promise<OperationResult> => {
  try {

    console.log('Creating investment:', { amountSol, referrals });

    if (!wallet.publicKey) {
      throw new Error('Wallet not connected');
    }

    if (amountSol < 0.05) {
      throw new Error('Minimum investment is 0.05 SOL');
    }

    if (referrals.length > 5) {
      throw new Error('Maximum 5 referrals allowed');
    }

    const program = getProgram(wallet);
    const amount = new BN(solToLamports(amountSol));

    const [contractStatePDA] = getContractStatePDA();
    const [poolPDA] = getPoolPDA();
    const [investmentPDA] = getInvestmentPDA(wallet.publicKey);

    const referrerKeys: PublicKey[] = [];
    const referrerPercentages: number[] = [];
    const remainingAccounts: any[] = [];

    // Process referrals and validate them
    for (let i = 0; i < referrals.length; i++) {
      const ref = referrals[i];
      
      try {
        const referrerKey = new PublicKey(ref.walletAddress);
        
        // Validate percentage is reasonable (1-10%)
        if (ref.percentage < 1 || ref.percentage > 10) {
          console.warn(`Invalid percentage for referral ${i+1}: ${ref.percentage}%. Skipping.`);
          continue;
        }

        // Check if referrer has an investment
        const [referrerInvestmentPDA] = getInvestmentPDA(referrerKey);
        
        try {
          // Try to fetch the referrer's investment to validate it exists
          // @ts-ignore
          const referrerInvestmentAccount = await program.account.investment.fetch(referrerInvestmentPDA);
          
          // Verify the investment is active
          if (referrerInvestmentAccount.status.completed) {
            console.warn(`Referrer ${referrerKey.toBase58()} has completed investment, skipping`);
            continue;
          }

          // Add to our arrays
          referrerKeys.push(referrerKey);
          referrerPercentages.push(ref.percentage);

          // Add both investment account and referrer account to remaining accounts
          remainingAccounts.push({
            pubkey: referrerInvestmentPDA,
            isWritable: true,
            isSigner: false,
          });
          remainingAccounts.push({
            pubkey: referrerKey,
            isWritable: true,
            isSigner: false,
          });

          console.log(`Added valid referrer: Level ${ref.level}, ${ref.percentage}%, Wallet: ${referrerKey.toBase58()}`);

        } catch (fetchError) {
          console.warn(`Referrer ${referrerKey.toBase58()} doesn't have an active investment, skipping`);
          // Skip this referrer instead of failing the entire transaction
          continue;
        }

      } catch (error) {
        console.warn('Invalid referrer wallet address:', ref.walletAddress, error);
        // Skip invalid wallet addresses
        continue;
      }
    }

    console.log('Valid referrers found:', referrerKeys.length);
    console.log('Transaction accounts:', {
      investment: investmentPDA.toString(),
      contractState: contractStatePDA.toString(),
      pool: poolPDA.toString(),
      user: wallet.publicKey.toString(),
      remainingAccountsCount: remainingAccounts.length,
    });

    // Convert percentages to Buffer
    const referrerPercentagesBuffer = Buffer.from(referrerPercentages);

    // Create the instruction
    const instruction = await program.methods
      .createInvestment(amount, referrerKeys, referrerPercentagesBuffer)
      .accounts({
        investment: investmentPDA,
        contractState: contractStatePDA,
        pool: poolPDA,
        user: wallet.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .remainingAccounts(remainingAccounts) // Pass referrer accounts here
      .instruction();

    const transaction = new Transaction().add(instruction);
    
    // Add priority fee to avoid congestion issues
    const priorityFeeInstruction = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: 1000, // Adjust as needed
    });
    transaction.add(priorityFeeInstruction);

    const signature = await sendTransaction(transaction, wallet);

    console.log('Investment created successfully:', signature);
    console.log(`View on Solscan: https://solscan.io/tx/${signature}?cluster=devnet`);

    return { success: true, txId: signature };

  } catch (error) {
    console.error('Error creating investment:', error);
    return { success: false, error: handleAnchorError(error) };
  }
};

// Helper function to create investment without referrals (simplified version)
export const createSimpleInvestment = async (
  wallet: WalletAdapter,
  amountSol: number
): Promise<OperationResult> => {
  try {
    await initializeContract(wallet);

    if (!wallet.publicKey) {
      throw new Error('Wallet not connected');
    }

    if (amountSol < 0.05) {
      throw new Error('Minimum investment is 0.05 SOL');
    }

    const program = getProgram(wallet);
    const amount = new BN(solToLamports(amountSol));

    const [contractStatePDA] = getContractStatePDA();
    const [poolPDA] = getPoolPDA();
    const [investmentPDA] = getInvestmentPDA(wallet.publicKey);

    console.log('Creating simple investment without referrals');

    const instruction = await program.methods
      .createInvestment(amount, [], Buffer.from([])) // Empty arrays for no referrals
      .accounts({
        investment: investmentPDA,
        contractState: contractStatePDA,
        pool: poolPDA,
        user: wallet.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .instruction();

    const transaction = new Transaction().add(instruction);
    const signature = await sendTransaction(transaction, wallet);

    console.log('Simple investment created successfully:', signature);
    return { success: true, txId: signature };

  } catch (error) {
    console.error('Error creating simple investment:', error);
    return { success: false, error: handleAnchorError(error) };
  }
};

// Enhanced claim ROI function
export const claimRoi = async (wallet: WalletAdapter): Promise<OperationResult> => {
  try {
    if (!wallet.publicKey) {
      throw new Error('Wallet not connected');
    }
    
    const program = getProgram(wallet);
    const [investmentPDA] = getInvestmentPDA(wallet.publicKey);
    
    console.log('Claiming ROI for:', wallet.publicKey.toString());
    
    // Create instruction
    const instruction = await program.methods
      .claimRoi()
      .accounts({
        investment: investmentPDA,
        user: wallet.publicKey,
      })
      .instruction();
    
    // Create transaction
    const transaction = new Transaction().add(instruction);
    
    const signature = await sendTransaction(transaction, wallet);
    
    console.log('ROI claimed successfully:', signature);
    return { success: true, txId: signature };
    
  } catch (error) {
    console.error('Error claiming ROI:', error);
    return { success: false, error: handleAnchorError(error) };
  }
};

// Enhanced add funds function with referral support
export const addMoreFunds = async (
  wallet: WalletAdapter,
  amountSol: number,
  referrals: Array<{ walletAddress: string; percentage: number; level: number }> = []
): Promise<OperationResult> => {
  try {
    if (!wallet.publicKey) {
      throw new Error('Wallet not connected');
    }
    
    if (amountSol < 0.05) {
      throw new Error('Minimum additional investment is 0.05 SOL');
    }

    console.log('Adding more funds with referrals:', { amountSol, referrals });
    
    const program = getProgram(wallet);
    const amount = new BN(solToLamports(amountSol));
    
    const [contractStatePDA] = getContractStatePDA();
    const [poolPDA] = getPoolPDA();
    const [investmentPDA] = getInvestmentPDA(wallet.publicKey);

    const referrerKeys: PublicKey[] = [];
    const referrerPercentages: number[] = [];
    const remainingAccounts: any[] = [];

    // Process referrals and validate them (same logic as createInvestment)
    for (let i = 0; i < referrals.length; i++) {
      const ref = referrals[i];
      
      try {
        const referrerKey = new PublicKey(ref.walletAddress);
        
        // Validate percentage is reasonable (1-10%)
        if (ref.percentage < 1 || ref.percentage > 10) {
          console.warn(`Invalid percentage for referral ${i+1}: ${ref.percentage}%. Skipping.`);
          continue;
        }

        // Check if referrer has an investment
        const [referrerInvestmentPDA] = getInvestmentPDA(referrerKey);
        
        try {
          // Try to fetch the referrer's investment to validate it exists
          // @ts-ignore
          const referrerInvestmentAccount = await program.account.investment.fetch(referrerInvestmentPDA);
          
          // Verify the investment is active
          if (referrerInvestmentAccount.status.completed) {
            console.warn(`Referrer ${referrerKey.toBase58()} has completed investment, skipping`);
            continue;
          }

          // Add to our arrays
          referrerKeys.push(referrerKey);
          referrerPercentages.push(ref.percentage);

          // Add both investment account and referrer account to remaining accounts
          remainingAccounts.push({
            pubkey: referrerInvestmentPDA,
            isWritable: true,
            isSigner: false,
          });
          remainingAccounts.push({
            pubkey: referrerKey,
            isWritable: true,
            isSigner: false,
          });

          console.log(`Added valid referrer for addMoreFunds: Level ${ref.level}, ${ref.percentage}%, Wallet: ${referrerKey.toBase58()}`);

        } catch (fetchError) {
          console.warn(`Referrer ${referrerKey.toBase58()} doesn't have an active investment, skipping`);
          continue;
        }

      } catch (error) {
        console.warn('Invalid referrer wallet address:', ref.walletAddress, error);
        continue;
      }
    }

    console.log('Valid referrers found for addMoreFunds:', referrerKeys.length);

    // Convert percentages to Buffer
    const referrerPercentagesBuffer = Buffer.from(referrerPercentages);
    
    // Create instruction with referrer support
    const instruction = await program.methods
      .addMoreFunds(amount, referrerKeys, referrerPercentagesBuffer)
      .accounts({
        investment: investmentPDA,
        contractState: contractStatePDA,
        pool: poolPDA,
        user: wallet.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .remainingAccounts(remainingAccounts) // Pass referrer accounts here
      .instruction();
    
    // Create transaction
    const transaction = new Transaction().add(instruction);

    // Add priority fee to avoid congestion issues
    const priorityFeeInstruction = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: 1000,
    });
    transaction.add(priorityFeeInstruction);
    
    const signature = await sendTransaction(transaction, wallet);
    
    console.log('Funds added successfully with referral processing:', signature);
    console.log(`View on Solscan: https://solscan.io/tx/${signature}?cluster=devnet`);
    
    return { success: true, txId: signature };
    
  } catch (error) {
    console.error('Error adding funds with referrals:', error);
    return { success: false, error: handleAnchorError(error) };
  }
};

// Result type for data operations
interface DataResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Enhanced get investment details
export const getInvestmentDetails = async (
  wallet: WalletAdapter
): Promise<DataResult<any>> => {
  try {
    if (!wallet.publicKey) {
      throw new Error('Wallet not connected');
    }
    
    const program = getProgram(wallet);
    const [investmentPDA] = getInvestmentPDA(wallet.publicKey);
    
    console.log('Fetching investment details for:', investmentPDA.toString());
    
    // @ts-ignore
    const investmentData = await program.account.investment.fetch(investmentPDA) as InvestmentData;
    
    // Transform data to readable format
    const transformedData = {
      owner: investmentData.owner.toString(),
      initialInvestment: lamportsToSol(investmentData.initialInvestment),
      earnedAmount: lamportsToSol(investmentData.earnedAmount),
      referEarnedAmount: lamportsToSol(investmentData.referEarnedAmount),
      percentageOfRoi: investmentData.percentageOfRoi.toNumber(),
      claimsSinceWithdrawal: investmentData.claimsSinceWithdrawal.toNumber(),
      lastClaimTime: new Date(investmentData.lastClaimTime.toNumber() * 1000),
      lastWithdrawalTime: new Date(investmentData.lastWithdrawalTime.toNumber() * 1000),
      withdrawalAmount: lamportsToSol(investmentData.withdrawalAmount),
      withdrawnReferAmount: lamportsToSol(investmentData.withdrawnReferAmount),
      status: Object.keys(investmentData.status)[0], // Get the active key
    };
    
    console.log('Investment details:', transformedData);
    return { success: true, data: transformedData };
    
  } catch (error: any) {
    console.error('Error fetching investment details:', error);
    
    // Handle account not found
    if (error?.message?.includes('Account does not exist')) {
      return { success: true, data: null };
    }
    
    return { success: false, error: handleAnchorError(error) };
  }
};

// Check if user has an active investment
export const checkInvestmentExists = async (wallet: WalletAdapter): Promise<boolean> => {
  try {
    const result = await getInvestmentDetails(wallet);
    return result.success && result.data !== null;
  } catch {
    return false;
  }
};

// Get contract state (read-only operation)
export const getContractState = async (): Promise<DataResult<any>> => {
  try {
    // Create a temporary provider for read-only operations
    const provider = new AnchorProvider(
      connection,
      {} as anchor.Wallet,
      { commitment: 'confirmed' }
    );
    
    const program = new Program(idlJson, provider) as FlowTradeProgram;
    const [contractStatePDA] = getContractStatePDA();
    
    // @ts-ignore
    const contractState = await program.account.contractState.fetch(contractStatePDA) as ContractState;
    
    return {
      success: true,
      data: {
        admin: contractState.admin.toString(),
        poolBalance: lamportsToSol(contractState.poolBalance),
      }
    };
    
  } catch (error) {
    console.error('Error fetching contract state:', error);
    return { success: false, error: handleAnchorError(error) };
  }
};

// Enhanced debug function
export const debugProgram = async (wallet: WalletAdapter): Promise<void> => {
  try {
    console.log('=== PROGRAM DEBUG INFO ===');
    console.log('Program ID:', PROGRAM_ID.toString());
    console.log('Connection:', connection.rpcEndpoint);
    console.log('Wallet connected:', !!wallet.publicKey);
    
    if (wallet.publicKey) {
      console.log('Wallet pubkey:', wallet.publicKey.toString());
      
      const [investmentPDA] = getInvestmentPDA(wallet.publicKey);
      const [contractStatePDA] = getContractStatePDA();
      const [poolPDA] = getPoolPDA();
      
      console.log('Investment PDA:', investmentPDA.toString());
      console.log('Contract State PDA:', contractStatePDA.toString());
      console.log('Pool PDA:', poolPDA.toString());
      
      // Test connection
      const balance = await connection.getBalance(wallet.publicKey);
      console.log('Wallet balance:', lamportsToSol(balance), 'SOL');
      
      // Test program account existence
      try {
        const programAccount = await connection.getAccountInfo(PROGRAM_ID);
        console.log('Program account exists:', !!programAccount);
      } catch (error) {
        console.log('Program account check failed:', error);
      }
    }
    
    console.log('=== END DEBUG INFO ===');
  } catch (error) {
    console.error('Debug error:', error);
  }
};

// Enhanced withdraw function
export const withdrawFunds = async (
  wallet: WalletAdapter,
  amountSol: number,
  source: 'zap' | 'referral' | 'both'
): Promise<OperationResult> => {
  try {
    // await initializeContract(wallet);
    if (!wallet.publicKey) {
      throw new Error('Wallet not connected');
    }
    
    if (amountSol < 0.05) {
      throw new Error('Minimum withdrawal is 0.05 SOL');
    }

    console.log('Withdrawing funds:', { amountSol, source });
    
    const program = getProgram(wallet);
    const amount = new BN(solToLamports(amountSol));
    
    const [contractStatePDA] = getContractStatePDA();
    const [poolPDA] = getPoolPDA();
    const [investmentPDA] = getInvestmentPDA(wallet.publicKey);

    // Map source to contract enum
    let withdrawalSource;
    switch (source) {
      case 'zap':
        withdrawalSource = { zap: {} };
        break;
      case 'referral':
        withdrawalSource = { referral: {} };
        break;
      case 'both':
        withdrawalSource = { both: {} };
        break;
      default:
        throw new Error('Invalid withdrawal source');
    }
    
    // Create instruction
    const instruction = await program.methods
      .withdraw(amount, withdrawalSource)
      .accounts({
        investment: investmentPDA,
        contractState: contractStatePDA,
        pool: poolPDA,
        user: wallet.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .instruction();
    
    // Create transaction
    const transaction = new Transaction().add(instruction);

    // Add priority fee to avoid congestion issues
    const priorityFeeInstruction = ComputeBudgetProgram.setComputeUnitPrice({
      microLamports: 1000,
    });
    transaction.add(priorityFeeInstruction);
    
    const signature = await sendTransaction(transaction, wallet);
    
    console.log('Funds withdrawn successfully:', signature);
    console.log(`View on Solscan: https://solscan.io/tx/${signature}?cluster=devnet`);
    
    return { success: true, txId: signature };
    
  } catch (error) {
    console.error('Error withdrawing funds:', error);
    return { success: false, error: handleAnchorError(error) };
  }
};

// Export types and constants
export type {
  InvestmentData,
  ContractState,
  FlowTradeProgram,
  OperationResult,
  DataResult,
};

export {
  InvestmentStatus,
  WithdrawalSource,
  PROGRAM_ID,
  connection,
  lamportsToSol,
  solToLamports,
};