"use client"

import React, { useState, useRef } from 'react'
import { useE<PERSON>or, EditorContent, BubbleMenu } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import Placeholder from '@tiptap/extension-placeholder'
import TextAlign from '@tiptap/extension-text-align'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "react-hot-toast"
import { 
  Bold, 
  Italic, 
  List, 
  ListOrdered, 
  Link as LinkIcon, 
  AlignLeft, 
  AlignCenter, 
  AlignRight,
  Trash2,
  Loader2,
  Heading1,
  Heading2,
  Undo,
  Redo,
  Quote,
  ImagePlus,
  X,
  Upload,
  Check
} from 'lucide-react'
import Image from 'next/image'
// import { NewsCategory } from '@/types/news'


enum NewsCategory {
    MARKET = 'market',
    TECHNOLOGY = 'technology',
    DEFI = 'defi',
    TRADING = 'trading',
    GENERAL = 'general',
};

interface CloudinaryResult {
  secure_url: string;
  public_id: string;
  thumbnail_url: string;
}

const RichTextEditor = ({ content, onChange }: { content: string, onChange: (html: string) => void }) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3]
        },
        paragraph: {
          HTMLAttributes: {
            class: 'mb-4',
          }
        }
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline cursor-pointer hover:text-blue-700',
        }
      }),
      Placeholder.configure({
        placeholder: 'Write your content here...',
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right'],
        defaultAlignment: 'left',
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML()
        .replace(/<p><\/p>/g, '<p>&nbsp;</p>')
      onChange(html)
    },
    editorProps: {
      attributes: {
        class: 'focus:outline-none min-h-[250px] h-full prose prose-p:my-4 prose-headings:my-6',
      },
    },
  })

  if (!editor) {
    return <div className="h-[250px] border rounded-md p-4 bg-gray-50">Loading editor...</div>
  }

  const setLink = () => {
    const previousUrl = editor.getAttributes('link').href
    const url = window.prompt('URL', previousUrl)
    
    if (url === null) {
      return
    }

    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run()
      return
    }

    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
  }

  return (
    <div className="rich-text-editor">
      {editor && (
        <BubbleMenu 
          editor={editor} 
          tippyOptions={{ duration: 150 }}
          className="flex gap-1 p-1 bg-white border rounded-md shadow-md"
        >
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            className="h-7 w-7"
            onClick={() => editor.chain().focus().toggleBold().run()}
          >
            <Bold className="h-3 w-3" />
          </Button>
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            className="h-7 w-7"
            onClick={() => editor.chain().focus().toggleItalic().run()}
          >
            <Italic className="h-3 w-3" />
          </Button>
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            className="h-7 w-7"
            onClick={setLink}
          >
            <LinkIcon className="h-3 w-3" />
          </Button>
        </BubbleMenu>
      )}
      
      <div className="editor-toolbar flex flex-wrap gap-1 mb-2 p-2 border rounded-md bg-gray-50">
        <div className="flex gap-1 mr-2 border-r pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            className={editor.isActive('heading', { level: 1 }) ? 'bg-gray-200' : ''}
          >
            <Heading1 className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            className={editor.isActive('heading', { level: 2 }) ? 'bg-gray-200' : ''}
          >
            <Heading2 className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2 border-r pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={editor.isActive('bold') ? 'bg-gray-200' : ''}
          >
            <Bold className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive('italic') ? 'bg-gray-200' : ''}
          >
            <Italic className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2 border-r pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={editor.isActive('bulletList') ? 'bg-gray-200' : ''}
          >
            <List className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={editor.isActive('orderedList') ? 'bg-gray-200' : ''}
          >
            <ListOrdered className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            className={editor.isActive('blockquote') ? 'bg-gray-200' : ''}
          >
            <Quote className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2 border-r pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={setLink}
            className={editor.isActive('link') ? 'bg-gray-200' : ''}
          >
            <LinkIcon className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2 border-r pr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={editor.isActive({ textAlign: 'left' }) ? 'bg-gray-200' : ''}
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={editor.isActive({ textAlign: 'center' }) ? 'bg-gray-200' : ''}
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={editor.isActive({ textAlign: 'right' }) ? 'bg-gray-200' : ''}
          >
            <AlignRight className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex gap-1 mr-2">
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
          >
            <Undo className="h-4 w-4" />
          </Button>
          
          <Button 
            type="button"
            size="icon"
            variant="ghost"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="border rounded-md p-4 prose prose-sm max-w-none bg-white overflow-y-auto news-content" style={{ minHeight: '300px' }}>
        <EditorContent editor={editor} />
      </div>
    </div>
  )
}

const CreateNews = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [formData, setFormData] = useState({
    title: '',
    summary: '',
    content: '',
    category: NewsCategory.GENERAL,
    authorName: '',
    image: '',
    isPublished: true,
    isHighlighted: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [creatingArticle, setCreatingArticle] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: (e.target as HTMLInputElement).checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleContentChange = (html: string) => {
    setFormData({
      ...formData,
      content: html
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Image is too large. Maximum size is 10MB.");
      return;
    }
    
    // Validate file type
    if (!['image/jpeg', 'image/png', 'image/webp', 'image/gif'].includes(file.type)) {
      toast.error("Invalid file type. Please upload JPEG, PNG, WebP or GIF images.");
      return;
    }
    
    // Store the file for later upload
    setSelectedFile(file);
    
    // Show preview of the image
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setPreviewImage(e.target.result as string);
      }
    };
    reader.readAsDataURL(file);
    
    toast.success("Image selected. It will be uploaded when you submit the form.");
  };
  
  // Helper function to convert file to base64
  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const removeImage = () => {
    setSelectedFile(null);
    setPreviewImage('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    toast.success("Image removed");
  };

  const resetForm = () => {
    setFormData({
      title: '',
      summary: '',
      content: '',
      category: NewsCategory.GENERAL,
      authorName: '',
      image: '',
      isPublished: true,
      isHighlighted: false
    });
    setPreviewImage('');
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    toast.success("Form has been reset");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form fields
    if (!formData.title) {
      toast.error("Please enter a title");
      return;
    }
    if (!formData.summary) {
      toast.error("Please enter a summary");
      return;
    }
    if (!formData.content) {
      toast.error("Please enter content for the article");
      return;
    }
    
    // Process the content to preserve spacing if needed
    const processedContent = formData.content
      .replace(/<p><br><\/p>/g, '<p>&nbsp;</p>')
      .replace(/<p><\/p>/g, '<p>&nbsp;</p>');
    
    if (!selectedFile) {
      toast.error("Please select an image for the article");
      return;
    }
    if (!formData.authorName) {
      toast.error("Please enter an author name");
      return;
    }
    
    // Set overall submitting state
    setIsSubmitting(true);
    
    try {
      // First upload the image
      setUploadingImage(true);
      toast.loading("Uploading image...");
      
      const imageBase64 = await convertFileToBase64(selectedFile);
      
      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          image: imageBase64,
          folder: 'news_images',
        }),
      });
      
      if (!uploadResponse.ok) {
        throw new Error('Failed to upload image');
      }
      
      const uploadData = await uploadResponse.json();
      setUploadingImage(false);
      toast.success("Image uploaded successfully");
      
      // Then create the news article with the image URL
      setCreatingArticle(true);
      toast.loading("Creating article...");
      
      const newsData = {
        ...formData,
        content: processedContent,
        image: uploadData.url
      };
      
      const response = await fetch('/api/admin/news/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newsData),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create news article');
      }
      
      const data = await response.json();
      setCreatingArticle(false);
      
      toast.success("News article created successfully!");
      
      // Reset form
      resetForm();
      
    } catch (error) {
      console.error('Error creating news article:', error);
      toast.error("Failed to create news article. Please try again.");
    } finally {
      setIsSubmitting(false);
      setUploadingImage(false);
      setCreatingArticle(false);
    }
  };

  return (
    <div>
      <Card>
        <CardHeader>
          <CardTitle>Create News Article</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  name="title"
                  placeholder="Enter article title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <select
                  id="category"
                  name="category"
                  className="flex h-10 w-full rounded-md border outline-none border-input bg-background px-3 py-2 text-sm "
                  value={formData.category}
                  onChange={handleInputChange}
                  required
                >
                  {Object.values(NewsCategory).map((category) => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="authorName">Author Name</Label>
              <Input
                id="authorName"
                name="authorName"
                placeholder="Enter author name"
                value={formData.authorName}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="summary">Summary</Label>
              <Textarea
                id="summary"
                name="summary"
                placeholder="Enter a short summary of the article"
                rows={3}
                value={formData.summary}
                onChange={handleInputChange}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <RichTextEditor content={formData.content} onChange={handleContentChange} />
            </div>
            
            <div className="space-y-2 ">
              <Label>Featured Image</Label>
              <div className="flex flex-col space-y-4 cursor-pointer z-999 ">
                {!previewImage ? (
                  <div className="space-y-4 cursor-pointer z-999">
                    <input
                      type="file"
                      accept="image/jpeg,image/png,image/webp,image/gif"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      className="hidden"
                      id="image-upload"
                    />
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => fileInputRef.current?.click()}
                      className="w-full flex items-center justify-center gap-2 p-6 border-dashed"
                      disabled={isSubmitting}
                    >
                      <Upload className="h-5 w-5" />
                      <span>Select Image to Upload</span>
                    </Button>
                    {/* Image format hint */}
                    <p className="text-xs text-gray-500 text-center">
                      Supported formats: JPG, PNG, WebP, GIF. Max size: 10MB.
                    </p>
                  </div>
                ) : (
                  <div className="relative rounded-md overflow-hidden bg-gray-100">
                    <Image 
                      src={previewImage} 
                      alt="Preview" 
                      width={500}
                      height={300}
                      className="w-full h-60 object-cover"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 h-8 w-8 bg-white text-red-500 hover:bg-red-50 hover:text-red-700"
                      onClick={removeImage}
                      disabled={isSubmitting}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}
                {!formData.image && !previewImage && (
                  <p className="text-sm text-red-500">Featured image is required</p>
                )}
              </div>
            </div>
            
            <div className="flex flex-col space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPublished"
                  name="isPublished"
                  className="h-4 w-4 rounded border-gray-300"
                  checked={formData.isPublished}
                  onChange={handleInputChange}
                  disabled={isSubmitting}
                />
                <Label htmlFor="isPublished">Publish immediately</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isHighlighted"
                  name="isHighlighted"
                  className="h-4 w-4 rounded border-gray-300"
                  checked={formData.isHighlighted}
                  onChange={handleInputChange}
                  disabled={isSubmitting}
                />
                <Label htmlFor="isHighlighted">Highlight To Show Fire Emoji Over this News</Label>
              </div>
            </div>
            
            <CardFooter className="flex justify-end space-x-2 px-0 z-999  cursor-pointer">
              <Button 
                type="button" 
                variant="outline"
                onClick={() => {
                  if (window.confirm('Are you sure you want to reset the form?')) {
                    resetForm();
                  }
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className="min-w-[150px] transition-all z-999 cursor-pointer"
              >
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {uploadingImage && "Uploading Image..."}
                {!uploadingImage && creatingArticle && "Creating Article..."}
                {!uploadingImage && !creatingArticle && !isSubmitting && "Create Article"}
                {!uploadingImage && !creatingArticle && isSubmitting && "Processing..."}
              </Button>
            </CardFooter>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

export default CreateNews