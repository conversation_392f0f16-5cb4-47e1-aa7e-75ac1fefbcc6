import React from 'react';

interface NewsContentProps {
  content: string;
  className?: string;
}

/**
 * NewsContent component for properly displaying rich text content from the editor
 * Ensures proper spacing and formatting of the article content
 */
const NewsContent: React.FC<NewsContentProps> = ({ content, className = '' }) => {
  return (
    <div 
      className={`news-content prose prose-invert max-w-none ${className}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

export default NewsContent; 