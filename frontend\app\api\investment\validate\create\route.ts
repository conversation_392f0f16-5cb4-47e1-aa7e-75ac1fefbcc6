import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment, InvestmentStatus } from '@/libs/model/investment.schema';
import { User } from '@/libs/model/user.schema';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';
import { getValidReferrers } from '../../create/route';

// Validation response interface
interface ValidationResponse {
  success: boolean;
  error?: string;
  data?: {
    isValid: boolean;
    validReferrers: Array<{
      walletAddress: string;
      percentage: number;
      level: number;
    }>;
    userData: {
      id: string;
      hasActiveInvestment: boolean;
    };
    validationDetails: {
      amountValid: boolean;
      userExists: boolean;
      noActiveInvestment: boolean;
      referrersFound: number;
    };
  };
}

export async function POST(req: NextRequest): Promise<NextResponse<ValidationResponse>> {
  try {
    // Get the user ID from cookies
    const userId = "68380e3e3b44aa85b374c1f0";
    
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Parse request body
    const body = await req.json();
    const { amount, currency = 'sol', walletAddress } = body;
    
    // Initialize validation results
    let isValid = true;
    let errors: string[] = [];
    const validationDetails = {
      amountValid: false,
      userExists: false,
      noActiveInvestment: false,
      referrersFound: 0
    };
    
    // Validate amount
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      errors.push('Valid amount is required');
      isValid = false;
    } else {
      const investmentAmount = parseFloat(parseFloat(amount).toFixed(6));
      
      if (investmentAmount < 0.05) {
        errors.push('Minimum zapping amount is 0.05 SOL');
        isValid = false;
      } else {
        validationDetails.amountValid = true;
      }
    }
    
    // Validate wallet address
    if (!walletAddress || typeof walletAddress !== 'string') {
      errors.push('Valid wallet address is required');
      isValid = false;
    }
    
    // Find the user
    const user = await User.findById(userId);
    
    if (!user) {
      errors.push('User not found');
      isValid = false;
    } else {
      validationDetails.userExists = true;
    }
    
    // Check if user already has an active investment
    let hasActiveInvestment = false;
    if (user) {
      const existingInvestment = await Investment.findOne({
        user: new Types.ObjectId(userId),
        status: InvestmentStatus.ACTIVE
      });
      
      if (existingInvestment) {
        hasActiveInvestment = true;
        errors.push('You already have an active investment');
        isValid = false;
      } else {
        validationDetails.noActiveInvestment = true;
      }
    }
    
    // Get valid referrers
    let validReferrers: Array<{
      walletAddress: string;
      percentage: number;
      level: number;
    }> = [];
    
    if (user) {
      try {
        validReferrers = await getValidReferrers(userId);
        validationDetails.referrersFound = validReferrers.length;
      } catch (error) {
        console.error('Error getting valid referrers:', error);
        // This is not a validation failure, just no referrers
      }
    }
    
    // Return validation results
    if (isValid) {
      return NextResponse.json({
        success: true,
        data: {
          isValid: true,
          validReferrers,
          userData: {
            id: userId,
            hasActiveInvestment
          },
          validationDetails
        }
      });
    } else {
      return NextResponse.json({
        success: true,
        data: {
          isValid: false,
          validReferrers: [],
          userData: {
            id: userId,
            hasActiveInvestment
          },
          validationDetails
        },
        error: errors.join('; ')
      });
    }
    
  } catch (error: any) {
    console.error('Error validating investment creation:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to validate investment creation'
    }, { status: 500 });
  }
} 