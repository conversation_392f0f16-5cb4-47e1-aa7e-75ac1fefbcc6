"use client"
import React from 'react'
import { Heart, Eye, ChevronRight } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

type NewsItem = {
  id: string
  title: string
  image: string
  views: number
  likes: number
  category: string
  slug?: string
}

type NewsCardProps = {
  news: NewsItem
  index: number
}

// Function to format numbers with K (thousands)
const formatNumber = (num: number): string => {
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// Function to get a category color
const getCategoryColor = (category: string): string => {
  switch(category.toLowerCase()) {
    case 'market':
    case 'markets':
      return 'bg-blue-500/20 text-blue-400 border-blue-500/20'
    case 'global':
      return 'bg-purple-500/20 text-purple-400 border-purple-500/20'
    case 'regulation':
      return 'bg-amber-500/20 text-amber-400 border-amber-500/20'
    case 'defi':
      return 'bg-green-500/20 text-green-400 border-green-500/20'
    case 'technology':
      return 'bg-cyan-500/20 text-cyan-400 border-cyan-500/20'
    case 'trading':
      return 'bg-red-500/20 text-red-400 border-red-500/20'
    case 'general':
    default:
      return 'bg-gray-500/20 text-gray-400 border-gray-500/20'
  }
}

// Function to generate a placeholder pattern based on index
const getPlaceholderPattern = (id: number) => {
  const patterns = [
    'bg-gradient-to-br from-blue-600 to-purple-700', // Abstract crypto art
    'bg-gradient-to-r from-green-500 to-blue-600', // Brazil colors
    'bg-gradient-to-br from-purple-600 to-blue-500', // Meta digital
    'bg-gradient-to-br from-amber-500 to-orange-600', // Solana orange
    'bg-gradient-to-tr from-blue-500 to-purple-500', // Solana price
  ]
  
  return patterns[(id - 1) % patterns.length]
}

export const NewsCard: React.FC<NewsCardProps> = ({ news, index }) => {
  return (
    <Link href={`/news/${news.slug || news.id}`}>
      <div className="bg-black/60 backdrop-blur-md rounded-lg overflow-hidden border border-gray-800/50 relative cursor-pointer hover:-translate-y-1 hover:shadow-lg hover:border-green-500/20 transition-all duration-200">
        {/* Background glow effect */}
        <div className="absolute -right-10 -top-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>
        <div className="absolute -left-5 bottom-2 w-10 h-10 bg-blue-500/10 rounded-full blur-lg"></div>
        
        <div className="flex items-center">
          {/* Image with hover effect */}
          <div className="w-[76px] h-[76px] relative flex-shrink-0 group m-0.5">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-transparent z-10 rounded-lg"></div>
            <div className="w-full h-full bg-gray-800 relative overflow-hidden rounded-lg shadow-inner group-hover:scale-105 transition-transform duration-200">
              {/* Category badge */}
              <div className={`absolute top-1.5 left-1.5 z-20 px-1.5 py-0.5 text-[8px] font-medium rounded border ${getCategoryColor(news.category)}`}>
                {news.category}
              </div>
              
              {/* Actual image */}
              {news.image ? (
                <Image
                  src={news.image}
                  alt={news.title}
                  fill
                  className="object-cover"
                />
              ) : (
                // Fallback color background if no image
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-700">
                  <div className="absolute inset-0 bg-[radial-gradient(rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[size:4px_4px] opacity-50"></div>
                </div>
              )}
            </div>
          </div>
          
          {/* Content */}
          <div className="p-3.5 flex-1">
            {/* Title - truncate after 2 lines */}
            <h3 className="text-sm font-medium text-white line-clamp-2 mb-2 leading-tight">
              {news.title}
            </h3>
            
            {/* Stats */}
            <div className="flex items-center text-gray-400 text-xs">
              {/* Views */}
              <div className="flex items-center mr-4 bg-black/40 backdrop-blur-md rounded-full px-2 py-0.5">
                <Eye className="w-3 h-3 mr-1 text-gray-500" />
                <span>{formatNumber(news.views)}</span>
              </div>
              
              {/* Likes - in red to match screenshot */}
              <div className="flex items-center text-red-400 bg-black/40 backdrop-blur-md rounded-full px-2 py-0.5">
                <Heart className="w-3 h-3 mr-1 fill-red-500 text-red-500" />
                <span>{formatNumber(news.likes)}</span>
              </div>
            </div>
          </div>
          
          {/* Arrow indicator with hover effect */}
          <div className="pr-3.5 group">
            <div className="w-6 h-6 rounded-full bg-black/80 flex items-center justify-center group-hover:translate-x-1 transition-transform duration-200">
              <ChevronRight className="w-4 h-4 text-gray-400" />
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}
