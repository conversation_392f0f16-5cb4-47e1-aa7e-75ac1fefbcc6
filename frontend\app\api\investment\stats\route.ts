import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment } from '@/libs/model/investment.schema';
import { User } from '@/libs/model/user.schema';
import { cookies } from 'next/headers';
import { Types } from 'mongoose';

// Cache results for 10 minutes to prevent repeated DB queries
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes in ms
const statsCache = new Map<string, { data: any, timestamp: number }>();

// GET handler to get investment stats for the user or admin
export async function GET(req: NextRequest) {
  try {
    // Get the user ID from cookies
    const userId = (await cookies()).get('userId')?.value;
    // const userId = "68380e3e3b44aa85b374c1f0";
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Find the user to check if they're an admin
    const user = await User.findById(userId);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Check if requesting admin stats
    const url = new URL(req.url);
    const getAdminStats = url.searchParams.get('admin') === 'true';
    
    // If requesting admin stats, verify admin role
    if (getAdminStats) {
      if (user.role !== 'admin') {
        return NextResponse.json(
          { success: false, error: 'Administrator access required' },
          { status: 403 }
        );
      }
      
      // Check cache for admin requests
      const now = Date.now();
      const cacheKey = 'admin_stats';
      const cachedData = statsCache.get(cacheKey);
      
      if (cachedData && now - cachedData.timestamp < CACHE_DURATION) {
        return NextResponse.json({
          success: true,
          data: cachedData.data,
          cached: true
        });
      }
      
      // Get platform-wide statistics
      
      // Total active investments
      const totalActiveInvestments = await Investment.countDocuments({ status: 'active' });
      
      // Total completed investments
      const totalCompletedInvestments = await Investment.countDocuments({ status: 'completed' });
      
      // Total initial investments (sum of initialAmount)
      const initialInvestmentStats = await Investment.aggregate([
        {
          $group: {
            _id: null,
            totalInitialSol: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'sol'] }, '$initialAmount', 0]
              }
            },
            totalInitialFlow: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'flow'] }, '$initialAmount', 0]
              }
            },
            totalInitialBonk: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'bonk'] }, '$initialAmount', 0]
              }
            }
          }
        }
      ]);
      
      // Active investment stats (sum of activeInvestment)
      const activeInvestmentStats = await Investment.aggregate([
        {
          $match: { status: 'active' }
        },
        {
          $group: {
            _id: null,
            totalActiveSol: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'sol'] }, '$activeInvestment', 0]
              }
            },
            totalActiveFlow: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'flow'] }, '$activeInvestment', 0]
              }
            },
            totalActiveBonk: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'bonk'] }, '$activeInvestment', 0]
              }
            }
          }
        }
      ]);
      
      // Total withdrawn stats
      const withdrawnStats = await Investment.aggregate([
        {
          $group: {
            _id: null,
            totalWithdrawnSol: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'sol'] }, '$withdrawnAmount', 0]
              }
            },
            totalWithdrawnFlow: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'flow'] }, '$withdrawnAmount', 0]
              }
            },
            totalWithdrawnBonk: {
              $sum: {
                $cond: [{ $eq: ['$currency', 'bonk'] }, '$withdrawnAmount', 0]
              }
            }
          }
        }
      ]);
      
      // Total Zapped rewards claimed stats
      const zapClaimStats = await Investment.aggregate([
        {
          $unwind: '$claims'
        },
        {
          $match: { 'claims.type': 'zap' }
        },
        {
          $group: {
            _id: '$currency',
            totalZapClaimed: { $sum: '$claims.amount' },
            claimCount: { $sum: 1 }
          }
        }
      ]);
      
      // Total referral bonus stats
      const referralBonusStats = await Investment.aggregate([
        {
          $unwind: '$claims'
        },
        {
          $match: { 'claims.type': 'referral' }
        },
        {
          $group: {
            _id: '$currency',
            totalReferralBonus: { $sum: '$claims.amount' },
            referralCount: { $sum: 1 }
          }
        }
      ]);
      
      // Format results
      const initialInvestment = initialInvestmentStats.length > 0 ? initialInvestmentStats[0] : {
        totalInitialSol: 0,
        totalInitialFlow: 0,
        totalInitialBonk: 0
      };
      
      const activeInvestment = activeInvestmentStats.length > 0 ? activeInvestmentStats[0] : {
        totalActiveSol: 0,
        totalActiveFlow: 0,
        totalActiveBonk: 0
      };
      
      const withdrawnAmount = withdrawnStats.length > 0 ? withdrawnStats[0] : {
        totalWithdrawnSol: 0,
        totalWithdrawnFlow: 0,
        totalWithdrawnBonk: 0
      };
      
      // Format Zap claims by currency
      const zapClaims = {
        sol: { totalZapClaimed: 0, claimCount: 0 },
        flow: { totalZapClaimed: 0, claimCount: 0 },
        bonk: { totalZapClaimed: 0, claimCount: 0 }
      };
      
      zapClaimStats.forEach(stat => {
        // @ts-ignore
        if (stat._id && zapClaims[stat._id]) {
          // @ts-ignore
          zapClaims[stat._id] = {
            totalZapClaimed: stat.totalZapClaimed,
            claimCount: stat.claimCount
          };
        }
      });
      
      // Format referral bonus by currency
      const referralBonus = {
        sol: { totalReferralBonus: 0, referralCount: 0 },
        flow: { totalReferralBonus: 0, referralCount: 0 },
        bonk: { totalReferralBonus: 0, referralCount: 0 }
      };
      
      referralBonusStats.forEach(stat => {
        // @ts-ignore
        if (stat._id && referralBonus[stat._id]) {
          // @ts-ignore
          referralBonus[stat._id] = {
            totalReferralBonus: stat.totalReferralBonus,
            referralCount: stat.referralCount
          };
        }
      });
      
      // Compile the admin stats
      const adminStats = {
        investments: {
          totalActive: totalActiveInvestments,
          totalCompleted: totalCompletedInvestments
        },
        initialInvestment: {
          sol: initialInvestment.totalInitialSol || 0,
          flow: initialInvestment.totalInitialFlow || 0,
          bonk: initialInvestment.totalInitialBonk || 0
        },
        activeInvestment: {
          sol: activeInvestment.totalActiveSol || 0,
          flow: activeInvestment.totalActiveFlow || 0,
          bonk: activeInvestment.totalActiveBonk || 0
        },
        withdrawnAmount: {
          sol: withdrawnAmount.totalWithdrawnSol || 0,
          flow: withdrawnAmount.totalWithdrawnFlow || 0,
          bonk: withdrawnAmount.totalWithdrawnBonk || 0
        },
        zapClaims,
        referralBonus
      };
      
      // Cache the results
      statsCache.set(cacheKey, {
        data: adminStats,
        timestamp: now
      });
      
      return NextResponse.json({
        success: true,
        data: adminStats
      });
    }
    
    // Otherwise get user-specific stats
    
    // Get all user investments
    const userInvestments = await Investment.find({
      user: new Types.ObjectId(userId)
    }).lean();
    
    if (userInvestments.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          totalInvestments: 0,
          activeInvestments: 0,
          completedInvestments: 0,
          totalInvested: { sol: 0, flow: 0, bonk: 0 },
          totalWithdrawn: { sol: 0, flow: 0, bonk: 0 },
          totalZapClaimed: { sol: 0, flow: 0, bonk: 0 },
          totalReferralBonus: { sol: 0, flow: 0, bonk: 0 }
        }
      });
    }
    
    // Calculate statistics
    const stats = {
      totalInvestments: userInvestments.length,
      activeInvestments: userInvestments.filter(inv => inv.status === 'active').length,
      completedInvestments: userInvestments.filter(inv => inv.status === 'completed').length,
      totalInvested: { sol: 0, flow: 0, bonk: 0 },
      totalWithdrawn: { sol: 0, flow: 0, bonk: 0 },
      totalZapClaimed: { sol: 0, flow: 0, bonk: 0 },
      totalReferralBonus: { sol: 0, flow: 0, bonk: 0 }
    };
    
    // Calculate currency-specific stats
    userInvestments.forEach(investment => {
      const currency = investment.currency;
      
      // Add to total invested
      // @ts-ignore
      stats.totalInvested[currency] += investment.initialAmount || 0;
      
      // Add to total withdrawn
      // @ts-ignore
      stats.totalWithdrawn[currency] += investment.withdrawnAmount || 0;
      
      // Calculate claimed Zap rewards and referral bonuses
      if (investment.claims && Array.isArray(investment.claims)) {
        investment.claims.forEach(claim => {
          if (claim.type === 'zap') {
            // @ts-ignore
            stats.totalZapClaimed[currency] += claim.amount || 0;
          } else if (claim.type === 'referral') {
            // @ts-ignore
            stats.totalReferralBonus[currency] += claim.amount || 0;
          }
        });
      }
    });
    
    return NextResponse.json({
      success: true,
      data: stats
    });
    
  } catch (error: any) {
    console.error('Error getting investment stats:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to get investment stats' },
      { status: 500 }
    );
  }
} 