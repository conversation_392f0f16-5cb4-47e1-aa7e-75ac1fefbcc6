import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // Get the response
  const response = NextResponse.next();
  
  // Read userID from cookies
  const userId = request.cookies.get('userId')?.value;
  // const userId = "68380e3e3b44aa85b374c1f0"
  // Add userId to headers for API routes to use
  if (userId && request.nextUrl.pathname.startsWith('/api/')) {
    response.headers.set('x-user-id', userId);
  }
  
  return response;
}

// Configure which paths the middleware runs on
export const config = {
  matcher: [
    '/api/news/like/:path*',
    '/api/missions/:path*',
    '/api/user/:path*',
    '/api/admin/:path*',
  ],
}; 