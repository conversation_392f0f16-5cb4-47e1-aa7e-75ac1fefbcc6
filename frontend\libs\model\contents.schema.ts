import { Schema, model, models, Document, Model, Types } from 'mongoose';

export interface IContentSubmission extends Document {
    walletAddress : string;
    txHash : string;
    user: Types.ObjectId;
    contentLink: string;
    status: 'pending' | 'approved' | 'rejected';
    submittedAt: Date;
    updatedAt: Date;
    reviewedAt?: Date;
  rewardAmount: number;
  rewardCurrency: 'sol' | 'flow' | 'bonk';
  reviewNote?: string;
  platform?: string; // e.g., 'twitter', 'youtube', etc.
  hadPendingSubmission?: any;
}

const ContentSubmissionSchema = new Schema<IContentSubmission>(
  {
    walletAddress : {
      type: String,
      required: [true, 'Wallet address is required'],
      trim: true,
    },
    txHash : {
      type: String,
      trim: true,
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User is required'],
      index: true
    },
    contentLink: {
      type: String,
      required: [true, 'Content link is required'],
      trim: true,
    },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected'],
      default: 'pending',
      index: true
    },
    submittedAt: {
      type: Date,
      default: Date.now
    },
    updatedAt: {
      type: Date,
      default: Date.now
    },
    reviewedAt: {
      type: Date
    },
    rewardAmount: {
      type: Number,
      default: 0
    },
    rewardCurrency: {
      type: String,
      enum: ['sol', 'flow', 'bonk'],
      default: 'bonk',
      lowercase: true
    },
    reviewNote: {
      type: String
    },
    platform: {
      type: String
    }
  },
  {
    timestamps: {
      createdAt: 'submittedAt',
      updatedAt: 'updatedAt'
    }
  }
);

// Create a unique compound index to prevent duplicate submissions
ContentSubmissionSchema.index({ user: 1, contentLink: 1 }, { unique: true });

// Create a utility method to check if a user has any pending submissions
ContentSubmissionSchema.statics.hasPendingSubmission = async function(userId: Types.ObjectId): Promise<boolean> {
  const count = await this.countDocuments({
    user: userId,
    status: 'pending'
  });
  return count > 0;
};

// Clean and normalize URLs before saving
ContentSubmissionSchema.pre('save', function(next) {
  // Basic URL normalization to prevent duplicate submissions
  try {
    const url = new URL(this.contentLink);
    this.contentLink = url.toString();
    
    // Try to detect platform
    if (!this.platform) {
      if (url.hostname.includes('twitter') || url.hostname.includes('x.com')) {
        this.platform = 'twitter';
      } else if (url.hostname.includes('youtube')) {
        this.platform = 'youtube';
      } else if (url.hostname.includes('instagram')) {
        this.platform = 'instagram';
      } else if (url.hostname.includes('tiktok')) {
        this.platform = 'tiktok';
      }
    }
    
    next();
  } catch (error) {
    next(new Error('Invalid URL provided'));
  }
});

export interface ContentSubmissionModel extends Model<IContentSubmission> {
  hasPendingSubmission(userId: Types.ObjectId): Promise<boolean>;
}

export default models.ContentSubmission || model<IContentSubmission, ContentSubmissionModel>('ContentSubmission', ContentSubmissionSchema);
