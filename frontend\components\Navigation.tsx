"use client"
import React from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { motion } from 'framer-motion'
import { Compass, Target, BookOpen, Share2, Wallet } from 'lucide-react'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'

const Navigation = () => {
  const router = useRouter()
  const pathname = usePathname()
  const { isTelegram } = useAuth()
  const { t } = useLanguage()
  
  // Define navigation items with fallback names
  const navItems = [
    { 
      key: 'home',
      name: 'Console', 
      translationKey: 'nav.home',
      href: '/', 
      icon: Compass 
    },
    { 
      key: 'missions',
      name: 'Missions', 
      translationKey: 'nav.missions',
      href: '/missions', 
      icon: Target 
    },
    { 
      key: 'news',
      name: 'News', 
      translationKey: 'nav.news',
      href: '/news', 
      icon: BookOpen 
    },
    { 
      key: 'refer',
      name: '<PERSON><PERSON>', 
      translationKey: 'nav.referrals',
      href: '/refer', 
      icon: Share2 
    },
    { 
      key: 'wallet',
      name: 'Wallet', 
      translationKey: 'nav.wallet',
      href: '/wallet', 
      icon: Wallet 
    },
  ]
  
  // Helper to get display name with fallback
  const getNavName = (item: typeof navItems[0]): string => {
    // Try to get from translation
    const translated = t(item.translationKey)
    // If key is returned (meaning translation not found), use fallback
    return translated === item.translationKey ? item.name : translated
  }
  
  const handleNavigation = (href: string) => {
    if (isTelegram) {
      // Force history update and router update
      window.history.pushState({}, '', href)
      router.push(href)
    } else {
      router.push(href)
    }
  }
  
  return (
    <motion.nav 
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="fixed bottom-0 left-0 right-0 z-50 backdrop-blur-md bg-black/80 border-t border-green-500/20 pb-safe-area"
    >
      <div className="max-w-[500px] mx-auto px-2 relative">
        {/* Subtle top highlight */}
        <div className="absolute inset-x-0 top-0 h-[1px] bg-gradient-to-r from-transparent via-green-500/30 to-transparent"></div>
        
        <ul className="flex justify-between items-center py-1">
          {navItems.map((item) => {
            const isActive = pathname === item.href
            const IconComponent = item.icon
            
            return (
              <li key={item.key} className="w-full text-center">
                <button 
                  onClick={() => handleNavigation(item.href)}
                  className="w-full z-999 focus:outline-none"
                >
                  <div className="flex flex-col items-center py-3">
                    <div className="relative">
                      {isActive && (
                        <motion.div
                          layoutId="navGlow"
                          className="absolute inset-0 -z-10 w-9 h-9"
                          style={{
                            background: 'radial-gradient(circle, rgba(34, 197, 94, 0.2) 0%, rgba(0, 0, 0, 0) 70%)',
                            top: '-2px',
                            left: '-2px'
                          }}
                          transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                        />
                      )}
                      <div className={`transition-all duration-300 ${isActive ? 'text-green-400' : 'text-gray-500'}`}>
                        <IconComponent 
                          className={`w-5 h-5 ${isActive ? 'stroke-[2.5px]' : 'stroke-[1.5px]'}`} 
                        />
                      </div>
                    </div>
                    
                    <span className={`text-xs mt-1 transition-colors ${isActive ? 'text-green-400 font-medium' : 'text-gray-500'}`}>
                      {getNavName(item)}
                    </span>
                    
                    {isActive && (
                      <motion.div
                        layoutId="activeIndicator"
                        className="absolute -top-[1px] h-[2px] w-8 bg-green-500"
                        style={{ borderRadius: '0 0 2px 2px' }}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.2 }}
                      />
                    )}
                  </div>
                </button>
              </li>
            )
          })}
        </ul>
      </div>
    </motion.nav>
  )
}

export default Navigation