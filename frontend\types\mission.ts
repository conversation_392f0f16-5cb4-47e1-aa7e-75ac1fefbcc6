import { Types } from 'mongoose';

export enum MissionType {
  TASK = 'task',
  PARTNER = 'partner',
}

export enum MissionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  COMPLETED = 'completed'
}

export enum MissionProgressStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CLAIMED = 'claimed'
}

export enum RewardType {
  SOL = 'sol',
  FLOW = 'flow',
}

export interface MissionReward {
  type: RewardType;
  amount: number;
}

export interface MissionRedemption {
  userId: Types.ObjectId | string;
  redeemedAt: Date;
}

export interface MissionProgress {
  userId: Types.ObjectId | string;
  startedAt: Date;
  status: MissionProgressStatus;
  estimatedCompletionAt: Date;
}

export interface IMission {
  _id?: Types.ObjectId | string;
  title: string;
  description: string;
  type: MissionType;
  status: MissionStatus;
  image: string;
  taskLink?: string;
  reward: MissionReward;
  currentParticipants: number;
  redeemedBy: MissionRedemption[];
  startedBy?: MissionProgress[];
  isHighlighted: boolean;
  minTimeToComplete?: number;
  maxTimeToComplete?: number;
  createdAt?: Date;
  updatedAt?: Date;
  
  // Methods
  isRedeemedByUser?: (userId: string) => boolean;
  isStartedByUser?: (userId: string) => boolean;
  getUserProgress?: (userId: string) => MissionProgress | null;
  addRedemption?: (userId: string) => IMission;
  startMission?: (userId: string) => IMission;
}

export interface Mission {
  _id: string;
  title: string;
  description: string;
  type: MissionType;
  rewardType: RewardType;
  rewardAmount: number;
  timeToComplete: number;
  isActive: boolean;
  isHighlighted: boolean;
  createdAt: string;
  updatedAt: string;
} 