"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Trophy, UserCircle, X, TrendingUp, Users, BadgeDollarSign, Star, Medal, ChevronRight, Loader2, Crown, Coins, Wallet } from 'lucide-react'
import Image from 'next/image'
import { createPortal } from 'react-dom'

// Type definitions for leaderboard data
interface LeaderboardUser {
  _id: string
  displayName: string
  telegramUsername?: string
  telegramPhotoUrl?: string
}

interface ReferralLeader extends LeaderboardUser {
  referralCount: number
  totalRewards: number
}

interface FlowLeader extends LeaderboardUser {
  flowBalance: number
  dailyStreakDays: number
}

interface InvestmentLeader extends LeaderboardUser {
  totalInvested: number
  activeInvestments: number
  totalActiveAmount: number
}

interface LeaderboardStats {
  totalUsers: number
  totalInvestments: number
  activeInvestments: number
  totalInvestmentVolume: number
  totalFlowInWallets: number
}

interface LeaderboardData {
  referrals: ReferralLeader[]
  flow: FlowLeader[]
  investments: InvestmentLeader[]
  stats?: LeaderboardStats
}

const Leaderboard = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('referrals')
  const [isLoading, setIsLoading] = useState(true)
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardData>({
    referrals: [],
    flow: [],
    investments: []
  })

  // Fetch leaderboard data when modal opens
  useEffect(() => {
    const fetchLeaderboardData = async () => {
      if (!isOpen) return
      
      setIsLoading(true)
      try {
        const response = await fetch('/api/leaderboard')
        if (!response.ok) throw new Error('Failed to fetch leaderboard data')
        
        const data = await response.json()
        setLeaderboardData({
          referrals: data.leaderboards.referrals,
          flow: data.leaderboards.flow,
          investments: data.leaderboards.investments,
          stats: data.stats
        })
      } catch (error) {
        console.error('Error fetching leaderboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchLeaderboardData()
  }, [isOpen])

  // Get position indicator styles
  const getMedalColor = (position: number) => {
    switch (position) {
      case 0: return 'text-yellow-400' // Gold
      case 1: return 'text-gray-400' // Silver
      case 2: return 'text-amber-700' // Bronze
      default: return 'text-gray-500'
    }
  }

  // Get background gradient for position
  const getPositionBackground = (position: number) => {
    switch (position) {
      case 0: return 'bg-gradient-to-r from-yellow-300/10 to-yellow-500/10 border-yellow-500/30'
      case 1: return 'bg-gradient-to-r from-gray-300/10 to-gray-500/10 border-gray-500/30'
      case 2: return 'bg-gradient-to-r from-amber-700/10 to-amber-800/10 border-amber-700/30'
      default: return 'bg-black/30 border-gray-800/50'
    }
  }

  // Get tab icon
  const getTabIcon = (tabId: string) => {
    switch (tabId) {
      case 'referrals': return Users
      case 'flow': return Coins
      case 'investments': return TrendingUp
      default: return Trophy
    }
  }

  // Format number with commas
  const formatNumber = (num: number) => {
    return num.toLocaleString('en-US', { maximumFractionDigits: 2 })
  }

  // Function to check if we're in a browser environment
  const isBrowser = () => typeof window !== 'undefined'

  return (
    <>
      {/* Floating button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className=" bg-black/80 hover:bg-black/90 text-white p-3 rounded-full border border-orange-500/30 backdrop-blur-lg shadow-lg hover:shadow-orange-500/10 transition-all duration-300"
        whileHover={{ scale: 1.05, boxShadow: "0 0 15px rgba(249, 115, 22, 0.3)" }}
        whileTap={{ scale: 0.95 }}
      >
        <Trophy className="w-6 h-6 text-orange-400" />
      </motion.button>

      {/* Leaderboard Modal - Using Portal */}
      {isBrowser() && isOpen && createPortal(
        <AnimatePresence>
          <>
            <motion.div
              className="fixed inset-0 bg-black/70 backdrop-blur-md z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />

            <motion.div
              className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md bg-black/90 border border-orange-500/20 rounded-xl overflow-hidden z-50 backdrop-blur-xl shadow-[0_0_15px_rgba(249,115,22,0.15)]"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", duration: 0.5 }}
            >
              {/* Header */}
              <div className="relative bg-gradient-to-r from-orange-900/30 to-orange-800/20 border-b border-orange-500/20 p-4">
                <button
                  onClick={() => setIsOpen(false)}
                  className="absolute top-4 right-4 text-gray-400 hover:text-white"
                >
                  <X className="w-5 h-5" />
                </button>
                
                <div className="flex items-center">
                  <Trophy className="w-6 h-6 text-orange-400 mr-2" />
                  <h2 className="text-xl font-bold text-white">Leaderboard</h2>
                </div>
                <p className="text-sm text-gray-400 mt-1">
                  See who's leading the charts in our community
                </p>
              </div>

              {/* Stats Overview */}
              {!isLoading && leaderboardData.stats && (
                <div className="grid grid-cols-3 gap-2 p-4 bg-black/40">
                  <div className="bg-black/60 rounded-lg p-2 text-center border border-orange-500/10">
                    <p className="text-xs text-orange-300/70">Users</p>
                    <p className="text-lg font-medium text-white">{formatNumber(leaderboardData.stats.totalUsers)}</p>
                  </div>
                  <div className="bg-black/60 rounded-lg p-2 text-center border border-orange-500/10">
                    <p className="text-xs text-orange-300/70">FLOW Volume</p>
                    <p className="text-lg font-medium text-white">{formatNumber(leaderboardData.stats.totalFlowInWallets)}</p>
                  </div>
                  <div className="bg-black/60 rounded-lg p-2 text-center border border-orange-500/10">
                    <p className="text-xs text-orange-300/70">Investments</p>
                    <p className="text-lg font-medium text-white">{formatNumber(leaderboardData.stats.totalInvestments)}</p>
                  </div>
                </div>
              )}

              {/* Tabs */}
              <div className="grid grid-cols-3 gap-3 p-4">
                {['referrals', 'flow', 'investments'].map((tab) => {
                  const TabIcon = getTabIcon(tab);
                  const isActive = activeTab === tab;
                  
                  return (
                    <motion.div
                      key={tab}
                      className={`
                        relative cursor-pointer rounded-lg border p-3 flex flex-col items-center justify-center
                        transition-all duration-200 backdrop-blur-sm
                        ${isActive 
                          ? 'border-orange-500/50 bg-black/70 shadow-lg shadow-orange-500/10' 
                          : 'border-gray-800/50 bg-black/30 hover:bg-black/40'}
                      `}
                      onClick={() => setActiveTab(tab)}
                      whileHover={{ y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      {isActive && (
                        <motion.div 
                          className="absolute inset-0 -z-10 rounded-lg opacity-30"
                          layoutId="leaderboardTabGlow"
                          animate={{ 
                            boxShadow: "0 0 15px 2px rgba(249, 115, 22, 0.2)" 
                          }}
                          transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                        />
                      )}

                      <div className={`
                        mb-1.5 p-1.5 rounded
                        ${isActive ? 'text-orange-400' : 'text-gray-400'}
                      `}>
                        <TabIcon className={`w-5 h-5 ${isActive ? 'stroke-[2.5px]' : 'stroke-[1.5px]'}`} />
                      </div>
                      <span className={`
                        text-xs font-medium text-center
                        ${isActive ? 'text-orange-400' : 'text-gray-400'}
                      `}>
                        {tab.charAt(0).toUpperCase() + tab.slice(1)}
                      </span>
                      
                      {isActive && (
                        <motion.div
                          layoutId="leaderboardActiveTabIndicator"
                          className="absolute bottom-0 w-10 h-[2px] bg-orange-500/70 rounded-full"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.2 }}
                        />
                      )}
                    </motion.div>
                  );
                })}
              </div>

              {/* List Content */}
              <div className="max-h-[350px] overflow-y-auto p-4 pt-0">
                {isLoading ? (
                  <div className="flex justify-center items-center py-16">
                    <Loader2 className="w-8 h-8 text-orange-500 animate-spin" />
                  </div>
                ) : (
                  <AnimatePresence mode="wait">
                    {activeTab === 'referrals' && (
                      <motion.div 
                        key="referrals"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="space-y-2 pt-3"
                      >
                        {leaderboardData.referrals.map((user, index) => (
                          <motion.div
                            key={user._id}
                            className={`flex items-center p-3 rounded-lg border ${getPositionBackground(index)}`}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            <div className="flex-shrink-0 mr-3">
                              {index < 3 ? (
                                <div className={`w-8 h-8 flex items-center justify-center ${getMedalColor(index)}`}>
                                  <Crown className="w-6 h-6" />
                                </div>
                              ) : (
                                <div className="w-8 h-8 flex items-center justify-center text-gray-500 font-bold">
                                  {index + 1}
                                </div>
                              )}
                            </div>
                            
                            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-800 flex-shrink-0 mr-3">
                              {user.telegramPhotoUrl ? (
                                <Image 
                                  src={user.telegramPhotoUrl} 
                                  alt={user.displayName} 
                                  width={32} 
                                  height={32}
                                  className="object-cover"
                                />
                              ) : (
                                <UserCircle className="w-8 h-8 text-gray-400" />
                              )}
                            </div>
                            
                            <div className="flex-grow">
                              <p className="text-sm text-white font-medium truncate">
                                {user.displayName}
                              </p>
                              <p className="text-xs text-gray-400">
                                @{user.telegramUsername || 'anonymous'}
                              </p>
                            </div>
                            
                            <div className="flex-shrink-0 text-right">
                              <p className="text-sm font-medium text-white">
                                {user.referralCount}
                              </p>
                              <p className="text-xs text-orange-400">referrals</p>
                            </div>
                          </motion.div>
                        ))}

                        {leaderboardData.referrals.length === 0 && (
                          <div className="text-center py-8 text-gray-400">
                            No referral data available yet
                          </div>
                        )}
                      </motion.div>
                    )}

                    {activeTab === 'flow' && (
                      <motion.div 
                        key="flow"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="space-y-2 pt-3"
                      >
                        {leaderboardData.flow.map((user, index) => (
                          <motion.div
                            key={user._id}
                            className={`flex items-center p-3 rounded-lg border ${getPositionBackground(index)}`}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            <div className="flex-shrink-0 mr-3">
                              {index < 3 ? (
                                <div className={`w-8 h-8 flex items-center justify-center ${getMedalColor(index)}`}>
                                  <Crown className="w-6 h-6" />
                                </div>
                              ) : (
                                <div className="w-8 h-8 flex items-center justify-center text-gray-500 font-bold">
                                  {index + 1}
                                </div>
                              )}
                            </div>
                            
                            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-800 flex-shrink-0 mr-3">
                              {user.telegramPhotoUrl ? (
                                <Image 
                                  src={user.telegramPhotoUrl} 
                                  alt={user.displayName} 
                                  width={32} 
                                  height={32}
                                  className="object-cover"
                                />
                              ) : (
                                <UserCircle className="w-8 h-8 text-gray-400" />
                              )}
                            </div>
                            
                            <div className="flex-grow">
                              <p className="text-sm text-white font-medium truncate">
                                {user.displayName}
                              </p>
                              <div className="flex items-center text-xs text-gray-400">
                                <p>Streak: {user.dailyStreakDays} days</p>
                              </div>
                            </div>
                            
                            <div className="flex-shrink-0 text-right">
                              <p className="text-sm font-medium text-white">
                                {formatNumber(user.flowBalance)}
                              </p>
                              <p className="text-xs text-orange-400">FLOW</p>
                            </div>
                          </motion.div>
                        ))}

                        {leaderboardData.flow.length === 0 && (
                          <div className="text-center py-8 text-gray-400">
                            No FLOW balance data available yet
                          </div>
                        )}
                      </motion.div>
                    )}

                    {activeTab === 'investments' && (
                      <motion.div 
                        key="investments"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="space-y-2 pt-3"
                      >
                        {leaderboardData.investments.map((user, index) => (
                          <motion.div
                            key={user._id}
                            className={`flex items-center p-3 rounded-lg border ${getPositionBackground(index)}`}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            <div className="flex-shrink-0 mr-3">
                              {index < 3 ? (
                                <div className={`w-8 h-8 flex items-center justify-center ${getMedalColor(index)}`}>
                                  <Crown className="w-6 h-6" />
                                </div>
                              ) : (
                                <div className="w-8 h-8 flex items-center justify-center text-gray-500 font-bold">
                                  {index + 1}
                                </div>
                              )}
                            </div>
                            
                            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-800 flex-shrink-0 mr-3">
                              {user.telegramPhotoUrl ? (
                                <Image 
                                  src={user.telegramPhotoUrl} 
                                  alt={user.displayName} 
                                  width={32} 
                                  height={32}
                                  className="object-cover"
                                />
                              ) : (
                                <UserCircle className="w-8 h-8 text-gray-400" />
                              )}
                            </div>
                            
                            <div className="flex-grow">
                              <p className="text-sm text-white font-medium truncate">
                                {user.displayName}
                              </p>
                              <div className="flex items-center text-xs text-gray-400">
                                <p>{user.activeInvestments} active</p>
                              </div>
                            </div>
                            
                            <div className="flex-shrink-0 text-right">
                              <p className="text-sm font-medium text-white">
                                {formatNumber(user.totalInvested)}
                              </p>
                              <p className="text-xs text-orange-400">invested</p>
                            </div>
                          </motion.div>
                        ))}

                        {leaderboardData.investments.length === 0 && (
                          <div className="text-center py-8 text-gray-400">
                            No investment data available yet
                          </div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                )}
              </div>

              {/* Footer */}
              <div className="bg-black/50 border-t border-orange-500/20 p-4 text-center">
                <p className="text-xs text-gray-400">Leaderboards update every hour</p>
              </div>
            </motion.div>
          </>
        </AnimatePresence>,
        document.body
      )}
    </>
  )
}

export default Leaderboard