import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { User } from '@/libs/model/user.schema';
import { UserRole, UserStatus } from '@/types/user';

export async function POST(req: NextRequest) {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Parse request body
    const body = await req.json();
    const { telegramId,  telegramData } = body;
    
    console.log(`Login attempt for telegramId: ${telegramId}`);
    console.log(`Telegram data:`, telegramData ? JSON.stringify(telegramData) : 'None');
    
    if (!telegramId) {
      return NextResponse.json(
        { success: false, error: 'Telegram ID is required' },
        { status: 400 }
      );
    }
    
    // Find or create the user
    let user = await User.findOne({ telegramId });
    let isNewUser = false;
    
    if (!user) {
      // Create a new user
      isNewUser = true;
      console.log(`Creating new user for telegramId: ${telegramId}`);
      
      // Setup referral if code was provided
      let referredBy = null;
        
      const displayName = telegramData?.username || 
                        telegramData?.first_name || 
                        `User_${telegramId.substring(0, 5)}`;
                      
      user = new User({
        telegramId,
        telegramUsername: telegramData?.username || undefined,
        telegramName: telegramData?.first_name || undefined,
        displayName,
        role: UserRole.USER,
        status: UserStatus.ACTIVE,
        referredBy,
        wallet: {
          balance: {
            sol: 0,
            flow: 0,
          }
        }
      });
      
      await user.save();
      console.log(`New user created: ${displayName} (${telegramId})`);
    } else {
      console.log(`Existing user found: ${user.displayName} (${telegramId})`);
      
      // Update existing user's Telegram data if provided
      if (telegramData) {
        let hasChanges = false;
        
        if (telegramData.username && telegramData.username !== user.telegramUsername) {
          console.log(`Updating username from ${user.telegramUsername || 'none'} to ${telegramData.username}`);
          user.telegramUsername = telegramData.username;
          hasChanges = true;
        }
        
        if (telegramData.first_name && (!user.telegramName || user.telegramName !== telegramData.first_name)) {
          console.log(`Updating name from ${user.telegramName || 'none'} to ${telegramData.first_name}`);
          user.telegramName = telegramData.first_name;
          hasChanges = true;
        }
        
        // If no display name or it's still the default format, update it
        if (!user.displayName || user.displayName.startsWith('User_')) {
          const betterName = telegramData.username || telegramData.first_name;
          if (betterName) {
            console.log(`Updating displayName from ${user.displayName} to ${betterName}`);
            user.displayName = betterName;
            hasChanges = true;
          }
        }
        
        if (hasChanges) {
          await user.save();
          console.log(`Updated user data for ${user.displayName} (${telegramId})`);
        }
      }
    }
    
    // Update last login time
    user.lastLogin = new Date();
    await user.save();
    
    // Create a response object to set cookies
    const response = NextResponse.json({
      success: true,
      isNewUser,
      user: {
        _id: user._id,
        telegramId: user.telegramId,
        telegramUsername: user.telegramUsername,
        telegramName: user.telegramName,
        displayName: user.displayName,
        wallet: user.wallet,
        role: user.role,
        status: user.status,
        likedNews: user.likedNews || [],
        completedMissions: user.completedMissions || [],
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
      }
    });
    
    // Set a secure HTTP-only cookie with user ID
    response.cookies.set('userId', user._id.toString(), { 
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/'
    });
    
    console.log(`Login successful for ${user.displayName} (${telegramId})`);
    return response;
    
  } catch (error) {
    console.error('Login error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to login' },
      { status: 500 }
    );
  }
} 