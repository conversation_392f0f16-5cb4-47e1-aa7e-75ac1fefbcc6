import { NextResponse } from 'next/server'
import { User } from '@/libs/model/user.schema'
import { connectToDatabase } from '@/libs/db'
import { cookies } from 'next/headers';

// Define the same reward structure as in the frontend DailyStreak component
const DAILY_REWARDS = [
  { day: 1, amount: 100 },
  { day: 2, amount: 200 },
  { day: 3, amount: 350 },
  { day: 4, amount: 500 },
  { day: 5, amount: 750 },
  { day: 6, amount: 1000 },
  { day: 7, amount: 1500 },
];

export async function POST() {
  try {
    await connectToDatabase()
    
      // Get the user ID from cookies
      const userId = (await cookies()).get('userId')?.value;
      // const userId = "68380e3e3b44aa85b374c1f0";
      
      if (!userId) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

const user = await User.findById(userId);
if(!user){
  return NextResponse.json({ error: 'User not found' }, { status: 404 });
}
      
    // Check if user can claim
    const now = new Date()
    const lastClaim = user.dailyStreak?.lastClaimDate ? new Date(user.dailyStreak.lastClaimDate) : null
    
    // Reset times to midnight for comparison
    now.setHours(0, 0, 0, 0)
    if (lastClaim) {
      lastClaim.setHours(0, 0, 0, 0)
    }
    
    // If already claimed today
    if (lastClaim && lastClaim.getTime() === now.getTime()) {
      return NextResponse.json({ error: 'Already claimed today' }, { status: 400 })
    }
    
    // Calculate streak status
    let currentStreak = user.dailyStreak?.currentStreak || 0
    let weeklyProgress = user.dailyStreak?.weeklyProgress || 0
    
    // If this is first claim or continuing streak
    if (!lastClaim || 
        (lastClaim && (now.getTime() - lastClaim.getTime()) <= 86400000)) {
      currentStreak++
      weeklyProgress = (weeklyProgress + 1) % 7
    } else {
      // Streak broken
      currentStreak = 1
      weeklyProgress = 0
    }
    
    // Get reward from the custom rewards array (0-based index for weeklyProgress)
    const dayIndex = weeklyProgress === 0 ? 6 : weeklyProgress - 1;
    const reward = DAILY_REWARDS[dayIndex].amount;
    
    // Update user
    const updatedUser = await User.findOneAndUpdate(
      { _id: user._id },
      {
        $set: {
          'dailyStreak.currentStreak': currentStreak,
          'dailyStreak.highestStreak': Math.max(currentStreak, user.dailyStreak?.highestStreak || 0),
          'dailyStreak.lastClaimDate': now,
          'dailyStreak.weeklyProgress': weeklyProgress,
          'wallet.balance.flow': (user.wallet?.balance?.flow || 0) + reward
        },
        $inc: {
          'dailyStreak.totalDaysVisited': 1,
          'dailyStreak.totalRewardsEarned': reward
        },
        $push: {
          'dailyStreak.streakHistory': {
            date: now,
            reward: reward,
            streakDay: currentStreak
          }
        }
      },
      { new: true }
    )
    
    return NextResponse.json({
      success: true,
      reward,
      streak: {
        current: currentStreak,
        highest: updatedUser.dailyStreak?.highestStreak,
        weeklyProgress
      }
    })
    
  } catch (error) {
    console.error('Error in streak claim:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 