import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase, withDatabaseRetry } from '@/libs/db';
import { User } from '@/libs/model/user.schema';

// Cache results for 1 minute to prevent repeated DB queries
const CACHE_DURATION = 60 * 1000; // 1 minute in ms
const statsCache = new Map<string, { data: any, timestamp: number }>();

export async function GET(req: NextRequest) {
  try {
    // Get the user ID from the query or from the cookie
    const url = new URL(req.url);
    
    // Get the userId from the auth cookie or query param
    let userId = req.cookies.get('userId')?.value;
    const queryUserId = url.searchParams.get('userId');
    
    if (queryUserId) {
      userId = queryUserId;
    }
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    // Check cache first
    const now = Date.now();
    const cacheKey = `referrals_${userId}`;
    const cachedData = statsCache.get(cacheKey);
    
    if (cachedData && now - cachedData.timestamp < CACHE_DURATION) {
      console.log('Using cached referral stats');
      return NextResponse.json({
        success: true,
        data: cachedData.data,
        cached: true
      });
    }
    
    try {
      // Connect to database with retry logic
      await connectToDatabase();
      
      // Find the user with referrals populated
      const user = await withDatabaseRetry(async () => {
        return await User.findById(userId).populate({
          path: 'referrals',
          select: 'telegramId telegramUsername telegramName displayName createdAt lastLogin',
          options: { sort: { createdAt: -1 } }
        });
      });
      
      if (!user) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 404 }
        );
      }
      
      // If referralStats doesn't exist yet, initialize it with default values
      const referralStats = user.referralStats || {
        totalReferrals: 0,
        rewardsSol: 0,
        rewardsFlow: 0
      };
      
      // Get referrals from the populated field
      const referrals = user.referrals || [];
      
      // Prepare response data
      const responseData = {
        stats: {
          totalReferrals: referralStats.totalReferrals || referrals.length, 
          rewardsSol: referralStats.rewardsSol || 0,
          rewardsFlow: referralStats.rewardsFlow || 0
        },
        referrals: referrals.map((ref: any) => ({
          id: ref._id,
          name: ref.displayName,
          telegramUsername: ref.telegramUsername,
          telegramName: ref.telegramName,
          telegramId: ref.telegramId,
          joinedAt: ref.createdAt,
          lastActive: ref.lastLogin,
          status: 'active'
        }))
      };
      
      // Cache the results
      statsCache.set(cacheKey, {
        data: responseData,
        timestamp: now
      });
      
      return NextResponse.json({
        success: true,
        data: responseData
      });
    } catch (dbError) {
      console.error('Database operation failed:', dbError);
      return NextResponse.json(
        { success: false, error: 'Database operation failed' },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error('Error getting referrals:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get referrals' },
      { status: 500 }
    );
  }
} 