import mongoose, { Schema } from 'mongoose';
import { IUser, UserRole, UserStatus } from '../types/user';

// User schema
const UserSchema = new Schema<IUser>(
  {
    // Telegram information
    telegramId: { type: String, required: true, unique: true, index: true },
    telegramUsername: { type: String, sparse: true },
    telegramName: { type: String, sparse: true },
    telegramLanguage: { type: String, sparse: true },
    telegramPhotoUrl: { type: String, sparse: true },
    telegramChatId: { type: String, sparse: true },
    telegramChatType: { type: String, sparse: true },
    
    // Profile information
    displayName: { type: String, required: true },
    
    // Wallet information
    wallet: {
      balance: {
        sol: { type: Number, default: 0 , min: 0},
        flow: { type: Number, default: 0 , min: 0},
        bonk: { type: Number, default: 0 , min: 0},
      },
      lastUpdated: { type: Date }
    },
    
    // Activity tracking
    completedMissions: [{ type: Schema.Types.ObjectId, ref: 'Mission' }],
    likedNews: [{ type: Schema.Types.ObjectId, ref: 'News' }],
    
    // Account management
    role: { 
      type: String, 
      enum: Object.values(UserRole), 
      default: UserRole.USER,
      index: true
    },
    status: { 
      type: String, 
      enum: Object.values(UserStatus), 
      default: UserStatus.ACTIVE,
      index: true
    },
    lastLogin: { type: Date, default: Date.now },
    
    // Referral system
    referredBy: { type: Schema.Types.ObjectId, ref: 'User' },
    
    // Combined referrals with earnings
    referrals: [{
      user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
      earnedSol: { type: Number, default: 0 , min: 0},
      earnedFlow: { type: Number, default: 0 , min: 0},
      earnedBonk: { type: Number, default: 0 , min: 0},
      joinedAt: { type: Date, default: Date.now },
      lastEarned: { type: Date, default: Date.now }
    }],
    
    // Referral statistics (overall)
    referralStats: {
      totalReferrals: { type: Number, default: 0 },
      rewardsSol: { type: Number, default: 0 },
      rewardsFlow: { type: Number, default: 0 },
      rewardsBonk: { type: Number, default: 0 },
    },
    
    // Redeemed referral tasks (tracking claimed milestone rewards)
    redeemedReferralTasks: [{ type: String }], // Stores task IDs that have been claimed
    
    // Daily Streak System
    dailyStreak: {
      currentStreak: { type: Number, default: 0 , min: 0},
      highestStreak: { type: Number, default: 0 , min: 0},
      lastClaimDate: { type: Date },
      totalDaysVisited: { type: Number, default: 0 , min: 0},
      streakHistory: [{
        date: { type: Date },
        reward: { type: Number },
        streakDay: { type: Number }
      }],
      weeklyProgress: { type: Number, default: 0 , min: 0}, // Resets every week (0-7)
      totalRewardsEarned: { type: Number, default: 0 , min: 0}
    },  
    lastWithdrawal: { type: Date },  
    claimsSinceWithdrawal: { type: Number, default: 0 , min: 0},
  },
  {
    timestamps: true,
    versionKey: false
  }
);

// Create indexes for faster queries
UserSchema.index({ telegramId: 1 }, { unique: true });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ referredBy: 1 });
UserSchema.index({ 'referralStats.totalReferrals': -1 });
UserSchema.index({ 'referrals.user': 1 });

// Instance method to add a completed mission
UserSchema.methods.addCompletedMission = function(missionId: string) {
  if (!this.completedMissions.includes(missionId)) {
    this.completedMissions.push(missionId);
  }
  return this;
};

// Instance method to add a liked news post
UserSchema.methods.addLikedNews = function(newsId: string) {
  if (!this.likedNews.includes(newsId)) {
    this.likedNews.push(newsId);
  }
  return this;
};

// Instance method to remove a liked news post
UserSchema.methods.removeLikedNews = function(newsId: string) {
  this.likedNews = this.likedNews.filter(
    (id: any) => id.toString() !== newsId.toString()
  );
  return this;
};

// Instance method to add a referral
UserSchema.methods.addReferral = function(userId: mongoose.Types.ObjectId | string) {
  if (!this.referrals) {
    this.referrals = [];
  }
  
  // Check if this user is already in our referrals
  const existingIndex = this.referrals.findIndex(
    (ref: any) => ref.user && ref.user.toString() === userId.toString()
  );
  
  if (existingIndex === -1) {
    // Add new referral with earnings initialized to zero
    this.referrals.push({
      user: userId,
      earnedSol: 0,
      earnedFlow: 0,
      earnedBonk: 0,
      joinedAt: new Date(),
      lastEarned: new Date()
    });
    
    // Initialize referralStats if it doesn't exist
    if (!this.referralStats) {
      this.referralStats = {
        totalReferrals: 0,
        rewardsSol: 0,
        rewardsFlow: 0,
        rewardsBonk: 0
      };
    }
    
    // Update referral stats
    this.referralStats.totalReferrals = (this.referralStats.totalReferrals || 0) + 1;
  }
  
  return this;
};

// Instance method to add referral reward
UserSchema.methods.addReferralReward = function(currency: string, amount: number, userId: mongoose.Types.ObjectId | string) {
  // Initialize referralStats if it doesn't exist
  if (!this.referralStats) {
    this.referralStats = {
      totalReferrals: this.referrals?.length || 0,
      rewardsSol: 0,
      rewardsFlow: 0,
      rewardsBonk: 0
    };
  }
  
  // Add reward to the appropriate currency in overall stats
  if (currency === 'sol' && this.referralStats.rewardsSol !== undefined) {
    this.referralStats.rewardsSol += amount;
  } else if (currency === 'flow' && this.referralStats.rewardsFlow !== undefined) {
    this.referralStats.rewardsFlow += amount;
  } else if (currency === 'bonk' && this.referralStats.rewardsBonk !== undefined) {
    this.referralStats.rewardsBonk += amount;
  }
  
  // Find the specific referral and update earnings
  if (!this.referrals) {
    this.referrals = [];
  }
  
  const referralIndex = this.referrals.findIndex(
    (ref: any) => ref.user && ref.user.toString() === userId.toString()
  );
  
  if (referralIndex >= 0) {
    // Update existing referral earnings
    if (currency === 'sol') {
      this.referrals[referralIndex].earnedSol += amount;
    } else if (currency === 'flow') {
      this.referrals[referralIndex].earnedFlow += amount;
    } else if (currency === 'bonk') {
      this.referrals[referralIndex].earnedBonk += amount;
    }
    this.referrals[referralIndex].lastEarned = new Date();
  } else {
    // If the referral doesn't exist, add it (should not typically happen)
    this.addReferral(userId);
    
    // Now update the earnings for the newly added referral
    const newIndex = this.referrals.length - 1;
    if (currency === 'sol') {
      this.referrals[newIndex].earnedSol += amount;
    } else if (currency === 'flow') {
      this.referrals[newIndex].earnedFlow += amount;
    } else if (currency === 'bonk') {
      this.referrals[newIndex].earnedBonk += amount;
    }
  }
  
  return this;
};

// Instance method to check if a referral task has been redeemed
UserSchema.methods.hasRedeemedReferralTask = function(taskId: string) {
  if (!this.redeemedReferralTasks) {
    this.redeemedReferralTasks = [];
  }
  return this.redeemedReferralTasks.includes(taskId);
};

// Instance method to mark a referral task as redeemed
UserSchema.methods.redeemReferralTask = function(taskId: string) {
  if (!this.redeemedReferralTasks) {
    this.redeemedReferralTasks = [];
  }
  
  if (!this.redeemedReferralTasks.includes(taskId)) {
    this.redeemedReferralTasks.push(taskId);
  }
  
  return this;
};

// Static method to create a user from telegram data
UserSchema.statics.createFromTelegram = async function(telegramData: any, chatData?: any) {
  const user = new this({
    telegramId: telegramData.id.toString(),
    telegramUsername: telegramData.username,
    telegramName: `${telegramData.first_name || ''} ${telegramData.last_name || ''}`.trim(),
    telegramLanguage: telegramData.language_code,
    telegramPhotoUrl: telegramData.photo_url,
    telegramChatId: chatData?.id,
    telegramChatType: chatData?.type,
    displayName: telegramData.username || telegramData.first_name || 'User',
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    lastLogin: new Date(),
    wallet: {
      balance: {
        sol: 0,
        flow: 0,
        bonk: 0,
      }
    },
    referrals: [],
    referralStats: {
      totalReferrals: 0,
      rewardsSol: 0,
      rewardsFlow: 0,
      rewardsBonk: 0
    },
    redeemedReferralTasks: [],
    dailyStreak: {
      currentStreak: 0,
      highestStreak: 0,
      lastClaimDate: null,
      totalDaysVisited: 0,
      streakHistory: [],
      weeklyProgress: 0,
      totalRewardsEarned: 0
    },
    claimsSinceWithdrawal: 0
  });
  
  return await user.save();
};

// Helper method to get total referral earnings
UserSchema.methods.getTotalReferralEarnings = function() {
  let totalSol = 0;
  let totalFlow = 0;
  let totalBonk = 0;
  if (this.referrals && this.referrals.length > 0) {
    for (const ref of this.referrals) {
      totalSol += ref.earnedSol || 0;
      totalFlow += ref.earnedFlow || 0;
      totalBonk += ref.earnedBonk || 0;
    }
  }
  
  return { sol: totalSol, flow: totalFlow, bonk: totalBonk };
};
  
export const User = mongoose.model('User', UserSchema);
