import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { Investment, InvestmentStatus } from '@/libs/model/investment.schema';
import { User } from '@/libs/model/user.schema';
import { Types } from 'mongoose';
import { cookies } from 'next/headers';
import { getValidReferrers } from '../../create/route';

// Validation response interface
interface InvestMoreValidationResponse {
  success: boolean;
  error?: string;
  data?: {
    isValid: boolean;
    investment?: any;
    validReferrers: Array<{
      walletAddress: string;
      percentage: number;
      level: number;
    }>;
    validationDetails: {
      amountValid: boolean;
      investmentExists: boolean;
      investmentActive: boolean;
      walletMatches: boolean;
      referrersFound: number;
    };
    investmentData?: {
      currentAmount: number;
      newTotalAmount: number;
      currentROI: number;
      projectedROI: number;
    };
  };
}

export async function POST(req: NextRequest): Promise<NextResponse<InvestMoreValidationResponse>> {
  try {
    // Get the user ID from cookies
    const userId = "68380e3e3b44aa85b374c1f0";
    
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }
    
    // Connect to database
    await connectToDatabase();
    
    // Parse request body
    const body = await req.json();
    const { amount, walletAddress, investmentId } = body;
    
    // Initialize validation results
    let isValid = true;
    let errors: string[] = [];
    const validationDetails = {
      amountValid: false,
      investmentExists: false,
      investmentActive: false,
      walletMatches: false,
      referrersFound: 0
    };
    
    // Validate amount
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      errors.push('Valid amount is required');
      isValid = false;
    } else {
      const additionalAmount = parseFloat(parseFloat(amount).toFixed(6));
      
      if (additionalAmount < 0.05) {
        errors.push('Minimum additional investment is 0.05 SOL');
        isValid = false;
      } else {
        validationDetails.amountValid = true;
      }
    }
    
    // Validate wallet address
    if (!walletAddress || typeof walletAddress !== 'string') {
      errors.push('Valid wallet address is required');
      isValid = false;
    }
    
    // Find the user's active investment
    let investment = null;
    
    if (investmentId) {
      // Find by specific investment ID
      investment = await Investment.findOne({
        _id: new Types.ObjectId(investmentId),
        user: new Types.ObjectId(userId),
        status: InvestmentStatus.ACTIVE
      });
    } else {
      // Find any active investment for the user
      investment = await Investment.findOne({
        user: new Types.ObjectId(userId),
        status: InvestmentStatus.ACTIVE
      });
    }
    
    if (!investment) {
      errors.push('No active investment found');
      isValid = false;
    } else {
      validationDetails.investmentExists = true;
      validationDetails.investmentActive = true;
      
      // Check if wallet address matches
      if (investment.walletAddress && investment.walletAddress !== walletAddress) {
        errors.push('Wallet address does not match the investment');
        isValid = false;
      } else {
        validationDetails.walletMatches = true;
      }
    }
    
    // Get valid referrers
    let validReferrers: Array<{
      walletAddress: string;
      percentage: number;
      level: number;
    }> = [];
    
    try {
      validReferrers = await getValidReferrers(userId);
      validationDetails.referrersFound = validReferrers.length;
    } catch (error) {
      console.error('Error getting valid referrers:', error);
      // This is not a validation failure, just no referrers
    }
    
    // Calculate investment data if valid
    let investmentData = undefined;
    
    if (investment && validationDetails.amountValid) {
      const additionalAmount = parseFloat(parseFloat(amount).toFixed(6));
      const currentAmount = investment.initialAmount;
      const newTotalAmount = currentAmount + additionalAmount;
      
      // Calculate current ROI
      const currentTotal = investment.earnedAmount + investment.referralBonus;
      const currentROI = (currentTotal / currentAmount) * 100;
      
      // Calculate projected ROI (will be adjusted proportionally)
      const projectedROI = (currentTotal / newTotalAmount) * 100;
      
      investmentData = {
        currentAmount,
        newTotalAmount,
        currentROI,
        projectedROI
      };
    }
    
    // Return validation results
    if (isValid) {
      return NextResponse.json({
        success: true,
        data: {
          isValid: true,
          investment: investment ? {
            id: investment._id,
            initialAmount: investment.initialAmount,
            earnedAmount: investment.earnedAmount,
            referralBonus: investment.referralBonus,
            currency: investment.currency,
            walletAddress: investment.walletAddress
          } : undefined,
          validReferrers,
          validationDetails,
          investmentData
        }
      });
    } else {
      return NextResponse.json({
        success: true,
        data: {
          isValid: false,
          investment: investment ? {
            id: investment._id,
            initialAmount: investment.initialAmount,
            earnedAmount: investment.earnedAmount,
            referralBonus: investment.referralBonus,
            currency: investment.currency,
            walletAddress: investment.walletAddress
          } : undefined,
          validReferrers: [],
          validationDetails
        },
        error: errors.join('; ')
      });
    }
    
  } catch (error: any) {
    console.error('Error validating invest more:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to validate invest more'
    }, { status: 500 });
  }
} 