// ======= TIMING CONFIGURATION (EASILY ADJUSTABLE) =======
export const TIME_CONFIG = {
    // Time required to accumulate 1.5% ROI (in seconds)
    ROI_ACCUMULATION_SECONDS: 129600, // 36 hours (129600 seconds)
    // ROI_ACCUMULATION_SECONDS: 1, // 1 hour (3600 seconds)
    
    // Number of claims required before withdrawal
    CLAIMS_BEFORE_WITHDRAWAL: 5, // 1 for testing (5 in production)
    
    // Time before next withdrawal is allowed (in seconds)
    WITHDRAWAL_COOLDOWN_SECONDS: 30, // 30 seconds for testing (24 hours in production would be 86400)
    
    // Total ROI cap (as multiplier of initial investment)
    TOTAL_ROI_CAP: 2.5, // 250% total return
    
    // Single claim percentage
    SINGLE_CLAIM_PERCENTAGE: 0.015 // 1.5% per claim
  };
  