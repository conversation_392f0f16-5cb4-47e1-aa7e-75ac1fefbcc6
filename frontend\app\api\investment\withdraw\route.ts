import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/libs/db";
import { Investment, InvestmentStatus } from "@/libs/model/investment.schema";
import { User } from "@/libs/model/user.schema";
import { cookies } from "next/headers";
import { Types } from "mongoose";
import { revalidatePath } from "next/cache";

// Define Withdrawal schema for tracking withdrawals
interface IWithdrawal {
  userId: string;
  wallet: string;
  amount: number;
  currency: string;
  source: string;
  status: 'pending' | 'completed' | 'failed';
  txId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function POST(req: NextRequest) {
  try {
    // Get the user ID from cookies
    // const userId = (await cookies()).get('userId')?.value;
    const userId = "68380e3e3b44aa85b374c1f0";
    if (!userId) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Parse request body
    const body = await req.json();
    const { wallet, amount, source, txId } = body;

    // Validate required fields
    if (!wallet) {
      return NextResponse.json(
        { success: false, error: "Wallet address is required" },
        { status: 400 }
      );
    }

    if (!amount || isNaN(amount) || amount <= 0) {
      return NextResponse.json(
        { success: false, error: "Valid amount is required" },
        { status: 400 }
      );
    }

    if (!source || !['zap', 'referral', 'both'].includes(source)) {
      return NextResponse.json(
        { success: false, error: "Valid withdrawal source is required" },
        { status: 400 }
      );
    }

    if (amount < 0.05) {
      return NextResponse.json(
        { success: false, error: "Minimum withdrawal amount is 0.05 SOL" },
        { status: 400 }
      );
    }

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    // Check withdrawal cooldown (24 hours)
    if (user.lastWithdrawal) {
      const secondsSinceLastWithdrawal =
        (new Date().getTime() - user.lastWithdrawal.getTime()) / 1000;
      const cooldownPeriod = 24 * 60 * 60; // 24 hours

      if (secondsSinceLastWithdrawal < cooldownPeriod) {
        const hoursRemaining = Math.ceil(
          (cooldownPeriod - secondsSinceLastWithdrawal) / 3600
        );
        return NextResponse.json(
          {
            success: false,
            error: `Withdrawal cooldown active. You can withdraw again in ${hoursRemaining} hours.`,
          },
          { status: 400 }
        );
      }
    }

    // Find active investment
    const investment = await Investment.findOne({
      user: new Types.ObjectId(userId),
      status: "active",
    });
    
    if (!investment) {
      return NextResponse.json(
        { success: false, error: "No active investment found." },
        { status: 400 }
      );
    }
    
    // Check claims requirement for non-referral withdrawals
    if (source !== "referral") {
      if (investment.percentageOfROI !== 250 || source === "zap" || source === "both") {
        if (user.claimsSinceWithdrawal < 5) {
          return NextResponse.json(
            { success: false, error: `No sufficient claims found! Required: 5 | Available: ${user.claimsSinceWithdrawal}` },
            { status: 400 }
          );
        }
      }
    }

    // Check if investment is already completed
    if (investment.earnedAmount === investment.withdrawnAmount && investment.percentageOfROI === 250) {
      investment.status = InvestmentStatus.COMPLETED;
      await investment.save();
      return NextResponse.json(
        { success: false, error: "You have already claimed all funds earned from zapping." },
        { status: 400 }
      );
    }

    // Validate withdrawal amounts based on source
    let withdrawalValidation = validateWithdrawalAmounts(investment, user, amount, source);
    if (!withdrawalValidation.isValid) {
      return NextResponse.json(
        { success: false, error: withdrawalValidation.error },
        { status: 400 }
      );
    }

    // Create withdrawal record (pending status)
    const withdrawalRecord = {
      userId: userId,
      wallet: wallet,
      amount: amount,
      currency: 'sol',
      source: source,
      status: 'pending',
      txId: txId || undefined,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      // Process the withdrawal based on source
      const withdrawalResult : any = await processWithdrawal(investment, user, amount, source)! ;
      
      if (withdrawalResult.success) {
        // Update withdrawal record to completed
        withdrawalRecord.status = 'completed';
        withdrawalRecord.updatedAt = new Date();
        
        revalidatePath("/");
        revalidatePath("/wallet");

        return NextResponse.json({
          success: true,
          data: {
            withdrawalAmount: amount,
            source,
            ...withdrawalResult.data,
            txId: txId,
            withdrawalId: withdrawalRecord.createdAt.getTime().toString(), // Simple ID for now
          },
          message: withdrawalResult.message
        });
      } else {
        // Update withdrawal record to failed
        withdrawalRecord.status = 'failed';
        withdrawalRecord.updatedAt = new Date();
        
        return NextResponse.json(
          { success: false, error: withdrawalResult.error },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error("Error processing withdrawal:", error);
      
      // Update withdrawal record to failed
      withdrawalRecord.status = 'failed';
      withdrawalRecord.updatedAt = new Date();
      
      return NextResponse.json(
        { success: false, error: "Failed to process withdrawal" },
        { status: 500 }
      );
    }

  } catch (error: any) {
    console.error("Error processing withdrawal:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to process withdrawal",
      },
      { status: 500 }
    );
  }
}

// Helper function to validate withdrawal amounts
function validateWithdrawalAmounts(investment: any, user: any, amount: number, source: string) {
  const amountWithdrawable = investment.earnedAmount - investment.withdrawnAmount;
  const walletZapAmount = user.wallet.balance.sol;
  const referralAmount = user.referralStats?.rewardsSol || 0;

  switch (source) {
    case "zap":
      if (amountWithdrawable <= 0) {
        return { isValid: false, error: "No zap earnings available to withdraw." };
      }
      if (amount > amountWithdrawable) {
        return { isValid: false, error: "Insufficient zap earnings available to withdraw." };
      }
      if (amount > walletZapAmount) {
        return { isValid: false, error: "Insufficient wallet balance available to withdraw." };
      }
      break;

    case "referral":
      if (referralAmount <= 0) {
        return { isValid: false, error: "No referral earnings available to withdraw." };
      }
      if (amount > referralAmount) {
        return { isValid: false, error: "Insufficient referral earnings available to withdraw." };
      }
      if (amount > amountWithdrawable) {
        return { isValid: false, error: "Insufficient zap earnings available to withdraw." };
      }
      break;

    case "both":
      const totalAvailable = walletZapAmount + referralAmount;
      if (amount > totalAvailable) {
        return { isValid: false, error: `Insufficient balance available to withdraw. Available: ${amountWithdrawable}` };
      }
      if (amount > amountWithdrawable) {
        return { isValid: false, error: "Insufficient zap earnings available to withdraw." };
      }
      break;

    default:
      return { isValid: false, error: "Invalid withdrawal source." };
  }

  return { isValid: true };
}

// Helper function to process withdrawal based on source
async function processWithdrawal(investment: any, user: any, amount: number, source: string) {
  try {
    const isCompletingInvestment = investment.percentageOfROI === 250 && 
                                   investment.earnedAmount === investment.withdrawnAmount + amount;

    switch (source) {
      case "zap":
        return await processZapWithdrawal(investment, user, amount, isCompletingInvestment);
      
      case "referral":
        return await processReferralWithdrawal(investment, user, amount, isCompletingInvestment);
      
      case "both":
        return await processBothWithdrawal(investment, user, amount, isCompletingInvestment);
      
      default:
        return { success: false, error: "Invalid withdrawal source" };
    }
  } catch (error) {
    console.error("Error in processWithdrawal:", error);
    return { success: false, error: "Failed to process withdrawal" };
  }
}

async function processZapWithdrawal(investment: any, user: any, amount: number, isCompleting: boolean) {
  investment.withdrawnAmount += amount;
  user.wallet.balance.sol -= amount;
  user.lastWithdrawal = new Date();
  user.claimsSinceWithdrawal = 0;

  if (isCompleting) {
    investment.status = InvestmentStatus.COMPLETED;
  }

  await investment.save();
  await user.save();

  return {
    success: true,
    data: {
      nextWithdrawalEligibility: new Date(Date.now() + 24 * 60 * 60 * 1000),
      claimsSinceWithdrawal: user.claimsSinceWithdrawal,
      currentValue: investment.initialAmount + investment.earnedAmount - investment.withdrawnAmount,
      isCompleted: investment.status === InvestmentStatus.COMPLETED,
    },
    message: isCompleting 
      ? "Final withdrawal completed. Your zapping engine is now completed."
      : "Withdrawal successful to your wallet balance"
  };
}

async function processReferralWithdrawal(investment: any, user: any, amount: number, isCompleting: boolean) {
  investment.withdrawnAmount += amount;
  user.referralStats.rewardsSol -= amount;
  user.lastWithdrawal = new Date();
  user.claimsSinceWithdrawal = 0;

  if (isCompleting) {
    investment.status = InvestmentStatus.COMPLETED;
  }

  await investment.save();
  await user.save();

  return {
    success: true,
    data: {
      nextWithdrawalEligibility: new Date(Date.now() + 24 * 60 * 60 * 1000),
      claimsSinceWithdrawal: user.claimsSinceWithdrawal,
      currentValue: investment.initialAmount + investment.earnedAmount - investment.withdrawnAmount,
      isCompleted: investment.status === InvestmentStatus.COMPLETED,
    },
    message: isCompleting
      ? "Final withdrawal completed. Your zapping engine is now completed."
      : "Referral rewards withdrawn successfully to your wallet balance."
  };
}

async function processBothWithdrawal(investment: any, user: any, amount: number, isCompleting: boolean) {
  const walletZapAmount = user.wallet.balance.sol;
  const referralAmount = user.referralStats?.rewardsSol || 0;

  // Calculate how much to take from each source
  let zapDeduction = Math.min(amount, walletZapAmount);
  let referralDeduction = amount - zapDeduction;

  investment.withdrawnAmount += amount;
  user.wallet.balance.sol -= zapDeduction;
  user.referralStats.rewardsSol -= referralDeduction;
  user.lastWithdrawal = new Date();
  user.claimsSinceWithdrawal = 0;

  if (isCompleting) {
    investment.status = InvestmentStatus.COMPLETED;
  }

  await investment.save();
  await user.save();

  return {
    success: true,
    data: {
      zapDeduction,
      referralDeduction,
      nextWithdrawalEligibility: new Date(Date.now() + 24 * 60 * 60 * 1000),
      claimsSinceWithdrawal: user.claimsSinceWithdrawal,
      currentValue: investment.initialAmount + investment.earnedAmount - investment.withdrawnAmount,
      isCompleted: investment.status === InvestmentStatus.COMPLETED,
    },
    message: isCompleting
      ? "Final withdrawal completed. Your zapping engine is now completed."
      : `Withdrawal successful: ${zapDeduction} SOL from zap balance and ${referralDeduction} SOL from referral rewards.`
  };
}
