import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { News } from '@/libs/model/news.schema';
import { User } from '@/libs/model/user.schema';
import { revalidatePath } from 'next/cache';

export async function POST(
  req: NextRequest,
  context: { params: any }
) {
  try {
    // Get the article ID
    const id = context.params.id;
    
    if (!id) {
      return NextResponse.json(
        { error: 'News ID is required' },
        { status: 400 }
      );
    }
    
    // Get user ID from cookie or session
    const userId = req.cookies.get('userId')?.value;
    // Get Telegram ID if available (this should be the primary auth method)
    const telegramId = req.headers.get('x-telegram-id') || '';
    
    if (!userId && !telegramId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Connect to the database
    await connectToDatabase();
    
    // Find the user - prioritize finding by userId, fallback to telegramId
    let user;
    if (userId) {
      user = await User.findById(userId);
    }
    
    if (!user && telegramId) {
      user = await User.findOne({ telegramId });
    }
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Find the news article
    const article = await News.findById(id);
    
    if (!article) {
      return NextResponse.json(
        { success: false, error: 'Article not found' },
        { status: 404 }
      );
    }
    
    // Check if user already liked the article
    const alreadyLiked = article.likes.some((like: any) => 
      like.userId.toString() === user._id.toString()
    );
    
    if (alreadyLiked) {
      return NextResponse.json({
        success: false,
        error: 'You have already liked this article',
        alreadyLiked: true,
        likesCount: article.likesCount
      }, { status: 400 });
    }
    
    // Add the like to the article
    article.likes.push({
      userId: user._id,
      timestamp: new Date()
    });
    
    // Update the likes count
    article.likesCount = article.likes.length;
    await article.save();
    
    // Also update the user's liked news array
    if (!user.likedNews) {
      user.likedNews = [];
    }
    
    if (!user.likedNews.includes(article._id)) {
      user.likedNews.push(article._id);
      await user.save();
    }
    
    // Revalidate both news listing and the specific article page
    revalidatePath('/news');
    revalidatePath(`/news/${article.slug}`);
    
    // Return success response with updated data
    return NextResponse.json({
      success: true,
      likesCount: article.likesCount,
      userLiked: true
    });
    
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to like article' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  context: { params: any }
) {
  try {
    // Get the article ID
    const id = context.params.id;
    
    if (!id) {
      return NextResponse.json(
        { error: 'News ID is required' },
        { status: 400 }
      );
    }
    
    // Get user ID from cookie or session
    const userId = req.cookies.get('userId')?.value;
    // Get Telegram ID if available (this should be the primary auth method)
    const telegramId = req.headers.get('x-telegram-id') || '';
    
    if (!userId && !telegramId) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Connect to the database
    await connectToDatabase();
    
    // Find the user - prioritize finding by userId, fallback to telegramId
    let user;
    if (userId) {
      user = await User.findById(userId);
    }
    
    if (!user && telegramId) {
      user = await User.findOne({ telegramId });
    }
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Find the news article
    const article = await News.findById(id);
    
    if (!article) {
      return NextResponse.json(
        { success: false, error: 'Article not found' },
        { status: 404 }
      );
    }
    
    // Check if user has liked the article
    const likeIndex = article.likes.findIndex((like: any) => 
      like.userId.toString() === user._id.toString()
    );
    
    if (likeIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'You have not liked this article',
        notLiked: true,
        likesCount: article.likesCount
      }, { status: 400 });
    }
    
    // Remove the like from the article
    article.likes.splice(likeIndex, 1);
    
    // Update the likes count
    article.likesCount = article.likes.length;
    await article.save();
    
    // Also update the user's liked news array
    if (user.likedNews && user.likedNews.length > 0) {
      user.likedNews = user.likedNews.filter((newsId: any) => 
        newsId.toString() !== article._id.toString()
      );
      await user.save();
    }
    
    // Revalidate both news listing and the specific article page
    revalidatePath('/news');
    revalidatePath(`/news/${article.slug}`);
    
    // Return success response with updated data
    return NextResponse.json({
      success: true,
      likesCount: article.likesCount,
      userLiked: false
    });
    
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to unlike article' },
      { status: 500 }
    );
  }
} 