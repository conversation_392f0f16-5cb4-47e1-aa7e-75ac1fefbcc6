@import "tailwindcss";


input::-webkit-scrollbar-button {
  display: none;
}

body {
  margin: 0;
  padding : 0;
  box-sizing:border-box;
  scroll-behavior: "smooth";
}

::-webkit-scrollbar {
  display: none;
}

::selection{
  background-color: #04eb78;
  color: #fff;
}

button {
  cursor : "pointer";
}

/* Editor styles */
.news-content {
  line-height: 1.6;
}

.news-content p {
  margin-bottom: 1.25em;
  white-space: pre-wrap;
}

.news-content h1 {
  font-size: 1.8em;
  margin-top: 1.5em;
  margin-bottom: 0.8em;
}

.news-content h2 {
  font-size: 1.5em;
  margin-top: 1.25em;
  margin-bottom: 0.7em;
}

.news-content h3 {
  font-size: 1.25em;
  margin-top: 1em;
  margin-bottom: 0.6em;
}

.news-content ul,
.news-content ol {
  margin-bottom: 1.25em;
  padding-left: 1.5em;
}

.news-content li {
  margin-bottom: 0.5em;
}

.news-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  margin-bottom: 1.25em;
}

/* Fix for empty paragraphs which should still take up space */
.news-content p:empty::before {
  content: "\00a0";
  white-space: pre;
}

/* Force double spacing between paragraphs even when editor doesn't show it */
.news-content p + p {
  margin-top: 1.5em;
}

/* Add some air around lists and blockquotes */
.news-content ul,
.news-content ol,
.news-content blockquote {
  margin-top: 1em;
  margin-bottom: 1em;
}

/* Wallet Connect Styles */
.wallet-connect-container {
  display: flex;
  align-items: center;
  gap: 1rem;

}

.wallet-connect-button {
  width : 100%!important;
  background: linear-gradient(45deg, #00c3ff, #0099ff) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1.25rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease-in-out !important;
}

.wallet-connect-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 195, 255, 0.3) !important;
}

.wallet-connect-button.loading {
  opacity: 0.7 !important;
  cursor: wait !important;
}

.wallet-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.wallet-address {
  font-family: monospace;
  font-size: 0.875rem;
}

/* Wallet adapter styles */
.wallet-adapter-button {
  text-transform: none !important;
  font-family: inherit !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
}

.wallet-adapter-button-trigger {
  background: linear-gradient(45deg, #04eb78, #03be60) !important;
}

.wallet-adapter-dropdown-list {
  background: rgba(30, 30, 30, 0.95) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.wallet-adapter-dropdown-list-item {
  font-family: inherit !important;
}

.wallet-adapter-modal-wrapper {
  background: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: blur(10px) !important;
}

.wallet-adapter-modal-button-close {
  background: rgba(255, 255, 255, 0.1) !important;
}

.wallet-adapter-modal-content {
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  background: rgba(30, 30, 30, 0.95) !important;
} 