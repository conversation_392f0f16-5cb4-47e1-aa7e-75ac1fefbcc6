"use client"
import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Compass, Target, Gift, Star, Check, ArrowRight, Loader2, Clock, Award, X, AlertCircle, Info } from 'lucide-react'
import { LucideIcon } from 'lucide-react'
import { IMission, RewardType, MissionProgressStatus } from '@/types/mission'
import { useAuth } from '@/context/AuthContext'
import { useLanguage } from '@/context/LanguageContext'
import Image from 'next/image'
import { showErrorToast, showSuccessToast } from '../initial/ToasterProvider'

interface TasksComponentProps {
  missions: IMission[]
  isLoading: boolean
  refreshMissions: () => Promise<void>
}



interface MissionState {
  [missionId: string]: {
    status: 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETABLE' | 'COMPLETED' | 'REDEEMED' | 'ERROR';
    timeRemaining: number; // in seconds
    error?: string;
    completedAt?: string;
  }
}

const TasksComponent = ({ missions, isLoading, refreshMissions }: TasksComponentProps) => {
  const { user, isAuthenticated, isTelegram, telegramUser } = useAuth();
  const [processingMissionId, setProcessingMissionId] = useState<string | null>(null)
  const [missionStates, setMissionStates] = useState<MissionState>({})
  const { t } = useLanguage()
  
  // Use static user ID for testing
  const testMode = process.env.NODE_ENV === 'development';
  const testUserId = "68060cc9f9e4a17223e51a44"; // Same ID as in middleware
  const userId = user?._id;
  
  // Store mission state in localStorage instead of sessionStorage for better persistence
  useEffect(() => {
    if (Object.keys(missionStates).length > 0) {
      try {
        localStorage.setItem('taskMissionStates', JSON.stringify(missionStates));
      } catch (error) {
        console.error('Error saving mission states:', error);
      }
    }
  }, [missionStates]);
  
  // Set up timers for active missions
  useEffect(() => {
    // First try to restore state from localStorage
    try {
      const savedStates = localStorage.getItem('taskMissionStates');
      if (savedStates) {
        const parsedStates = JSON.parse(savedStates);
        if (Object.keys(parsedStates).length > 0) {
          setMissionStates(parsedStates);
        }
      }
    } catch (error) {
      console.error('Error retrieving mission states:', error);
    }
    
    // Then initialize mission states based on progress from server
    const initializeMissionStates = async () => {
      if (!userId) return;
      
      const states: MissionState = {};
      
      for (const mission of missions) {
        if (!mission._id) continue;
        
        // If mission is already redeemed by user
        if (isMissionRedeemed(mission)) {
          states[mission._id.toString()] = {
            status: 'REDEEMED',
            timeRemaining: 0,
          };
          continue;
        }
        
        // Check if mission is in progress
        if (mission.startedBy && mission.startedBy.length > 0) {
          const userProgress = mission.startedBy.find(
            progress => progress.userId.toString() === userId
          );
          
          if (userProgress) {
            const now = new Date();
            const completionTime = new Date(userProgress.estimatedCompletionAt);
            
            if (userProgress.status === MissionProgressStatus.COMPLETED ||
                userProgress.status === MissionProgressStatus.CLAIMED) {
              states[mission._id.toString()] = {
                status: 'COMPLETED',
                timeRemaining: 0,
                completedAt: userProgress.estimatedCompletionAt.toString(),
              };
            } else if (now >= completionTime) {
              states[mission._id.toString()] = {
                status: 'COMPLETABLE',
                timeRemaining: 0,
              };
            } else {
              const remainingMs = completionTime.getTime() - now.getTime();
              states[mission._id.toString()] = {
                status: 'IN_PROGRESS',
                timeRemaining: Math.ceil(remainingMs / 1000),
              };
            }
          } else {
            // Check localStorage before setting NOT_STARTED
            const savedStates = localStorage.getItem('taskMissionStates');
            if (savedStates) {
              const parsedStates = JSON.parse(savedStates);
              const savedState = parsedStates[mission._id.toString()];
              if (savedState && (savedState.status === 'IN_PROGRESS' || savedState.status === 'COMPLETABLE')) {
                states[mission._id.toString()] = savedState;
                continue;
              }
            }
            
            states[mission._id.toString()] = {
              status: 'NOT_STARTED',
              timeRemaining: 0,
            };
          }
        } else {
          // Check localStorage before setting NOT_STARTED
          const savedStates = localStorage.getItem('taskMissionStates');
          if (savedStates) {
            const parsedStates = JSON.parse(savedStates);
            const savedState = parsedStates[mission._id.toString()];
            if (savedState && (savedState.status === 'IN_PROGRESS' || savedState.status === 'COMPLETABLE')) {
              states[mission._id.toString()] = savedState;
              continue;
            }
          }
          
          states[mission._id.toString()] = {
            status: 'NOT_STARTED',
            timeRemaining: 0,
          };
        }
      }
      
      // Merge with existing missionStates
      setMissionStates(prevStates => ({
        ...prevStates,
        ...states
      }));
    };
    
    initializeMissionStates();
  }, [missions, userId]);
  
  // Update timers every second
  useEffect(() => {
    const timer = setInterval(() => {
      setMissionStates(prevStates => {
        const newStates = { ...prevStates };
        let changed = false;
        
        Object.keys(newStates).forEach(missionId => {
          const state = newStates[missionId];
          
          if (state.status === 'IN_PROGRESS' && state.timeRemaining > 0) {
            state.timeRemaining -= 1;
            changed = true;
            
            if (state.timeRemaining <= 0) {
              state.status = 'COMPLETABLE';
            }
          }
        });
        
        // Only trigger state update if something changed
        return changed ? newStates : prevStates;
      });
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);


  // Determine if a mission is redeemed by the current user
  const isMissionRedeemed = (mission: IMission) => {
    if (!userId) return false;
    return mission.redeemedBy.some(
      (redemption) => redemption.userId.toString() === userId
    )
  }

  // Start mission
  const startMission = async (missionId: string) => {
    if (!userId) {
      showErrorToast(t('missions.telegramRequired'))
      return
    }

    try {
      setProcessingMissionId(missionId)
      
      // Update local state immediately to prevent tab switching issues
      setMissionStates(prev => ({
        ...prev,
        [missionId]: {
          status: 'IN_PROGRESS',
          timeRemaining: 0, // Will be updated with server response
          error: t('missions.loading')
        }
      }))
      
      const response = await fetch('/api/missions/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ missionId })
      })

      const data = await response.json()

      if (response.ok) {
        // If mission started successfully
        if (data.status === 'STARTED') {
          showSuccessToast(t('missions.missionStarted', { minutes: data.mission.waitTimeMinutes }))
          
          // Update mission state
          setMissionStates(prev => ({
            ...prev,
            [missionId]: {
              status: 'IN_PROGRESS',
              timeRemaining: data.mission.waitTimeMinutes * 60,
            }
          }))
          
          // Open task link if available
          if (data.mission.taskLink) {
            window.open(data.mission.taskLink, '_blank')
          }
        } 
        // If mission is already completable
        else if (data.status === 'COMPLETABLE') {
          setMissionStates(prev => ({
            ...prev,
            [missionId]: {
              status: 'COMPLETABLE',
              timeRemaining: 0,
            }
          }))
        }
      } else {
        // Handle errors
        if (data.status === 'IN_PROGRESS') {
          showSuccessToast(t('missions.verificationInProgress'))
          
          setMissionStates(prev => ({
            ...prev,
            [missionId]: {
              status: 'IN_PROGRESS',
              timeRemaining: data.remainingTimeMinutes * 60,
              error: data.error
            }
          }))
          
          // Open task link if available
          if (data.taskLink) {
            window.open(data.taskLink, '_blank')
          }
        } else if (data.status === 'REDEEMED') {
          showSuccessToast(t('missions.alreadyCompleted'))
          setMissionStates(prev => ({
            ...prev,
            [missionId]: {
              status: 'REDEEMED',
              timeRemaining: 0,
            }
          }))
        } else {
          showErrorToast(data.error || t('missions.failedToStart'))
          // Revert to NOT_STARTED state if there was an error
          setMissionStates(prev => ({
            ...prev,
            [missionId]: {
              status: 'NOT_STARTED',
              timeRemaining: 0,
              error: data.error
            }
          }))
        }
      }
    } catch (error) {
      console.error('Error starting mission:', error)
      showErrorToast(t('missions.somethingWentWrong'))
      // Revert to NOT_STARTED state if there was an error
      setMissionStates(prev => ({
        ...prev,
        [missionId]: {
          status: 'NOT_STARTED',
          timeRemaining: 0,
          error: t('missions.networkError')
        }
      }))
    } finally {
      setProcessingMissionId(null)
    }
  }

  // Complete mission and claim reward
  const completeMission = async (missionId: string) => {
    if (!userId) {
      showErrorToast(t('missions.telegramRequiredClaim'))
      return
    }

    try {
      setProcessingMissionId(missionId)
      const response = await fetch('/api/missions/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ missionId })
      })

      const data = await response.json()

      if (response.ok) {
        showSuccessToast(t('missions.missionCompleted', { amount: data.reward.amount, type: data.reward.type }))
        
        // Update mission state
        setMissionStates(prev => ({
          ...prev,
          [missionId]: {
            status: 'REDEEMED',
            timeRemaining: 0,
          }
        }))
        
        refreshMissions()
      } else {
        showErrorToast(data.error || t('missions.failedToComplete'))
      }
    } catch (error) {
      console.error('Error completing mission:', error)
      showErrorToast(t('missions.somethingWentWrong'))
    } finally {
      setProcessingMissionId(null)
    }
  }

  // Render the appropriate icon based on mission type or custom icon
  const renderIcon = (mission: IMission): React.ReactNode => {
    // Default mapping for mission icons
    const iconMap: Record<string, LucideIcon> = {
      wealth: Compass,
      play: Target,
      reward: Gift,
      special: Star
    }
    
    // Use first word of mission title as fallback key
    const fallbackKey = mission.title.split(' ')[0].toLowerCase()
    
    // Try to get icon from mission.image if it contains an icon key
    const iconKey = mission.image?.split('/').pop()?.split('.')[0].toLowerCase() || fallbackKey
    
    const IconComponent = iconMap[iconKey] || Star
    return <IconComponent className="w-5 h-5 text-white" />
  }

  // Format remaining time as mm:ss
  const formatRemainingTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
  }

  // Get background color based on reward type but with special styling
  const getIconBgColor = (rewardType: RewardType) => {
    switch (rewardType) {
      case RewardType.SOL:
        return 'bg-gradient-to-br from-green-500 to-emerald-600'
      case RewardType.FLOW:
        return 'bg-gradient-to-br from-blue-500 to-indigo-600'
      default:
        return 'bg-gradient-to-br from-green-400 to-teal-500'
    }
  }

  // Get task link for a mission
  const getTaskLink = (mission: IMission): string => {
    return mission.taskLink || '#';
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="w-8 h-8 text-green-500 animate-spin" />
      </div>
    )
  }

  if (missions.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-400">{t('missions.noMissionsAvailable')}</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {missions.map((mission, index) => {
        const missionId = mission._id?.toString() || '';
        const missionState = missionStates[missionId] || { status: 'NOT_STARTED', timeRemaining: 0 };
        const isProcessing = processingMissionId === missionId;
        
        const isRedeemed = missionState.status === 'REDEEMED';
        const isInProgress = missionState.status === 'IN_PROGRESS';
        const isCompletable = missionState.status === 'COMPLETABLE';
        
        return (
          <motion.div 
            key={missionId}
            className="bg-black/60 rounded-lg overflow-hidden relative border border-gray-800/50 hover:border-green-500/30"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            whileHover={{ 
              y: -2,
              boxShadow: "0 4px 15px rgba(0, 0, 0, 0.2)",
            }}
          >
            {/* Background glow */}
            <div className="absolute -right-20 -top-20 w-40 h-40 bg-green-500/5 rounded-full blur-xl"></div>
            <div className="absolute -left-10 -bottom-10 w-20 h-20 bg-green-500/10 rounded-full blur-xl"></div>
            
            {/* Task label */}
            <div className="absolute top-2 right-2 bg-black/60 backdrop-blur-md border border-green-500/30 px-2 py-2 rounded-full flex items-center space-x-1">
              <Target className="w-3 h-3 text-green-400" />
            </div>
            {
              mission.isHighlighted && (
                <div className="absolute top-2 right-12 bg-black/60 backdrop-blur-md border border-yellow-500/30 px-2 py-2 rounded-full flex items-center space-x-1">
                <Star className="w-3 h-3 text-yellow-500" />
              </div>
              )
            }
            
            {/* Icon and title */}
            <div className="p-3 pb-0">
              <div className="flex items-center">
                <div className="w-10 h-10 flex items-center justify-center rounded-lg mr-3 relative overflow-hidden shadow-lg">
                  <Image src={mission.image} alt={mission.title} width={40} height={40} className='object-cover w-full h-full' />
                </div>
                <div>
                  <h3 className="text-base font-medium text-white">{mission.title}</h3>
                  <div className="text-xs text-green-400 flex items-center">
                    <span className="text-green-500">+</span>
                    <span className="mx-0.5 font-medium">{mission.reward.amount}</span>
                    <span>{mission.reward.type.toUpperCase()}</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Description */}
            <div className="px-3 pt-2 pb-2">
              <p className="text-xs text-gray-400">{mission.description}</p>
            </div>
            
            {/* Timer display for in-progress missions */}
            {isInProgress && (
              <div className="px-3 pb-2">
                <div className="flex items-center text-xs text-yellow-400">
                  <Clock className="w-3 h-3 mr-1" />
                  <span>{t('missions.estimatedVerificationTime')}: {formatRemainingTime(missionState.timeRemaining)}</span>
                </div>
              </div>
            )}
            
            {/* Action footer */}
            <div className="flex justify-end bg-black/30 p-2 border-t border-gray-800/50">
              {isRedeemed ? (
                <div className="bg-green-500/20 text-green-400 text-xs font-medium py-1.5 px-3 rounded-md border border-green-500/30 flex items-center space-x-1">
                  <Check className="w-3 h-3" />
                  <span>{t('missions.completed')}</span>
                </div>
              ) : isCompletable ? (
                <motion.button
                  onClick={() => completeMission(missionId)}
                  disabled={isProcessing}
                  className="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 text-xs font-medium py-1.5 px-3.5 rounded-md border border-yellow-500/30 backdrop-blur-sm flex items-center space-x-1 disabled:opacity-50"
                  whileTap={{ scale: isProcessing ? 1 : 0.95 }}
                >
                  {isProcessing ? (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  ) : (
                    <>
                      <Award className="w-3 h-3 mr-1" />
                      <span>{t('missions.claimReward')}</span>
                    </>
                  )}
                </motion.button>
              ) : isInProgress ? (
                <a 
                  href={getTaskLink(mission)}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 text-xs font-medium py-1.5 px-3 rounded-md border border-blue-500/30 flex items-center space-x-1 transition-colors"
                >
                  <Loader2 className="w-3 h-3 animate-spin mr-1" />
                  <span>{t('missions.continueTask')}</span>
                </a>
              ) : (
                <motion.button
                  onClick={() => startMission(missionId)}
                  disabled={isProcessing}
                  className="bg-green-500/20 hover:bg-green-500/30 text-green-400 text-xs font-medium py-1.5 px-3.5 rounded-md border border-green-500/30 backdrop-blur-sm flex items-center space-x-1 disabled:opacity-50"
                  whileTap={{ scale: isProcessing ? 1 : 0.95 }}
                >
                  {isProcessing ? (
                    <Loader2 className="w-3 h-3 animate-spin" />
                  ) : (
                    <>
                      <span>{t('missions.startTask')}</span>
                      <ArrowRight className="w-3 h-3 ml-1" />
                    </>
                  )}
                </motion.button>
              )}
            </div>
          </motion.div>
        )
      })}

    </div>
  )
}

export default TasksComponent 