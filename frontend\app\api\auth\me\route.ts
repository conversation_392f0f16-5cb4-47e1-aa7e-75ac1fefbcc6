import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/libs/db';
import { User } from '@/libs/model/user.schema';



export async function GET(req: NextRequest) {
  try {
    // First check for user ID in cookies
    const userId = "68380e3e3b44aa85b374c1f0";
    const telegramId = 5195131141;
    
    // Also check for telegram ID in headers (fallback)
      // const userId = req.cookies.get('userId')?.value;
      // const telegramId = req.headers.get('x-telegram-id') || req.cookies.get('telegramId')?.value;
    
    if (!userId && !telegramId) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }
    
    // Connect to the database
    await connectToDatabase();
    
    // Find the user by ID first, then by telegramId if ID not found
    let user;
    if (userId) {
      user = await User.findById(userId)
        .populate({
          path: 'referredBy',
          select: 'telegramId telegramUsername telegramName displayName telegramPhotoUrl'
        })
        .populate({
          path: 'referrals.user',
          select: 'telegramId telegramUsername telegramName displayName telegramPhotoUrl'
        })
    }
    
    if (!user && telegramId) {
      user = await User.findOne({ telegramId })
        .populate({
          path: 'referredBy',
          select: 'telegramId telegramUsername telegramName displayName telegramPhotoUrl'
        })
        .populate({
          path: 'referrals.user',
          select: 'telegramId telegramUsername telegramName displayName telegramPhotoUrl'
        })
    }

    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    console.log(user)
    // Return the complete user object with all fields
    return NextResponse.json({
      success: true,
      data: user
    });
    
  } catch (error) {
    console.error('Error fetching user:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user data' },
      { status: 500 }
    );
  }
} 